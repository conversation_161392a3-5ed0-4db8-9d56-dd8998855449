#!/usr/bin/env python3
"""
测试导入
"""

try:
    from backend.agent.agent_coordinator import AgentCoordinator
    print("AgentCoordinator 导入成功")
except Exception as e:
    print(f"AgentCoordinator 导入失败: {e}")
    import traceback
    traceback.print_exc()

try:
    from backend.agent.material_manager import MaterialManager
    print("MaterialManager 导入成功")
except Exception as e:
    print(f"MaterialManager 导入失败: {e}")

try:
    from backend.agent.content_analyzer import ContentAnalyzer
    print("ContentAnalyzer 导入成功")
except Exception as e:
    print(f"ContentAnalyzer 导入失败: {e}")

print("测试完成")
