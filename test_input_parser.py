#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试InputParser模块
"""

import logging
import os
import sys

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

try:
    # 导入模块
    from backend.agent.perception_engine.input_parser import InputParser

    def test_input_parser():
        """测试InputParser模块"""
        print("=== 测试 InputParser 模块 ===")

        parser = InputParser()

        # 测试解析JSON格式命令
        json_command = '{"action": "create_and_publish_video", "params": {"material_path": "/path/to/video.mp4", "platforms": ["douyin", "bilibili"]}}'
        result = parser.parse_user_command(json_command)
        print("JSON命令解析结果: {result}")

        # 测试解析自然语言命令
        nl_command = "创建一个视频，素材路径：/path/to/video.mp4，平台：抖音、B站，时长：30秒，风格：动作"
        result = parser.parse_user_command(nl_command)
        print("自然语言命令解析结果: {result}")

        # 测试解析配置文件
        config_path = os.path.join(os.getcwd(), "config", "editing_rules.json")
        if os.path.exists(config_path):
            result = parser.parse_config_file(config_path)
            print("配置文件解析结果: {list(result.keys())}")
        else:
            print("配置文件不存在: {config_path}")

        # 测试获取命令帮助
        help_text = parser.get_command_help("create_and_publish_video")
        print("命令帮助:\n{help_text}")

    if __name__ == "__main__":
        test_input_parser()

except Exception as e:
    print("错误: {e}")
    import traceback

    traceback.print_exc()
