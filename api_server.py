#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IntelliCutAgent API 服务
提供 RESTful API 接口，允许通过 HTTP 请求使用 IntelliCutAgent 的功能
"""

import os
import sys
import json
import logging
import uvicorn
from typing import Dict, Any, List, Optional
from fastapi import FastAPI, File, UploadFile, Form, HTTPException, BackgroundTasks, Query
from fastapi.responses import JSONResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入模块
from backend.agent.perception_engine.input_parser import InputParser
from backend.agent.perception_engine.video_analyzer import VideoAnalyzer
from backend.agent.perception_engine.audio_analyzer import AudioAnalyzer
from backend.agent.smart_editor.smart_editor import SmartEditor
from backend.agent.publisher.video_publisher import VideoPublisher

# 创建 FastAPI 应用
app = FastAPI(
    title="IntelliCutAgent API",
    description="智能视频剪辑代理系统 API 接口",
    version="0.1.0"
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，生产环境应该限制
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建模块实例
input_parser = InputParser()
video_analyzer = VideoAnalyzer()
audio_analyzer = AudioAnalyzer()
smart_editor = SmartEditor()
video_publisher = VideoPublisher()

# 定义数据模型
class AnalysisRequest(BaseModel):
    analysis_types: List[str] = Field(default=["basic", "scene_detection"], 
                                    description="分析类型列表")
    
class EditRequest(BaseModel):
    duration: str = Field(default="60s", description="目标时长，例如: 60s, 5min")
    style: str = Field(default="standard", description="编辑风格")
    transitions: List[str] = Field(default=["fade"], description="转场效果列表")
    effects: List[str] = Field(default=[], description="视频效果列表")
    
class PublishRequest(BaseModel):
    platforms: List[str] = Field(..., description="目标平台列表")
    title: str = Field(..., description="视频标题")
    description: Optional[str] = Field(None, description="视频描述")
    tags: Optional[List[str]] = Field(None, description="视频标签")
    schedule_time: Optional[str] = Field(None, description="计划发布时间")
    
class CommandRequest(BaseModel):
    command: str = Field(..., description="命令字符串或JSON")

# 后台任务
task_storage = {}

def process_video_analysis(task_id: str, video_path: str, analysis_types: List[str]):
    """后台处理视频分析任务"""
    try:
        logger.info(f"开始处理视频分析任务 {task_id}: {video_path}")
        result = video_analyzer.analyze_video(video_path, analysis_types)
        task_storage[task_id] = {
            "status": "completed",
            "result": result
        }
        logger.info(f"视频分析任务 {task_id} 完成")
    except Exception as e:
        logger.error(f"视频分析任务 {task_id} 失败: {e}")
        task_storage[task_id] = {
            "status": "failed",
            "error": str(e)
        }

def process_video_editing(task_id: str, video_path: str, edit_request: EditRequest):
    """后台处理视频编辑任务"""
    try:
        logger.info(f"开始处理视频编辑任务 {task_id}: {video_path}")
        
        # 分析视频
        analysis_result = video_analyzer.analyze_video(video_path, ["basic", "scene_detection"])
        
        # 准备编辑规则
        edit_rules = {
            "duration": edit_request.duration,
            "style": edit_request.style,
            "transitions": edit_request.transitions,
            "effects": edit_request.effects
        }
        
        # 编辑视频
        output_path = smart_editor.auto_edit_video([video_path], analysis_result, edit_rules)
        
        task_storage[task_id] = {
            "status": "completed",
            "result": {
                "output_path": output_path,
                "edit_rules": edit_rules
            }
        }
        logger.info(f"视频编辑任务 {task_id} 完成")
    except Exception as e:
        logger.error(f"视频编辑任务 {task_id} 失败: {e}")
        task_storage[task_id] = {
            "status": "failed",
            "error": str(e)
        }

# API 路由
@app.get("/")
async def root():
    """API 根路径，返回基本信息"""
    return {
        "name": "IntelliCutAgent API",
        "version": "0.1.0",
        "description": "智能视频剪辑代理系统 API 接口"
    }

@app.post("/analyze/video")
async def analyze_video(
    background_tasks: BackgroundTasks,
    video: UploadFile = File(...),
    request: str = Form(...),
):
    """
    分析视频内容
    
    - **video**: 视频文件
    - **request**: JSON 格式的请求参数，包含 analysis_types 字段
    """
    try:
        # 解析请求参数
        request_data = json.loads(request)
        analysis_types = request_data.get("analysis_types", ["basic", "scene_detection"])
        
        # 保存上传的视频文件
        temp_dir = os.path.join(os.getcwd(), "temp")
        os.makedirs(temp_dir, exist_ok=True)
        
        # 确保 filename 不为 None
        filename = video.filename or f"uploaded_video_{hash(str(video))}.mp4"
        video_path = os.path.join(temp_dir, filename)
        with open(video_path, "wb") as f:
            f.write(await video.read())
        
        # 创建任务ID
        task_id = f"analyze_{os.path.basename(video_path)}_{hash(str(analysis_types))}"
        
        # 添加后台任务
        background_tasks.add_task(process_video_analysis, task_id, video_path, analysis_types)
        
        # 初始化任务状态
        task_storage[task_id] = {
            "status": "processing",
            "video_path": video_path,
            "analysis_types": analysis_types
        }
        
        return {
            "task_id": task_id,
            "status": "processing",
            "message": "视频分析任务已提交"
        }
    except Exception as e:
        logger.error(f"提交视频分析任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"提交视频分析任务失败: {e}")

@app.get("/analyze/video/status/{task_id}")
async def get_video_analysis_status(task_id: str):
    """
    获取视频分析任务状态
    
    - **task_id**: 任务ID
    """
    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
    
    task_status = task_storage[task_id]
    
    return {
        "task_id": task_id,
        "status": task_status.get("status", "unknown")
    }

@app.get("/analyze/video/result/{task_id}")
async def get_video_analysis_result(task_id: str):
    """
    获取视频分析任务结果
    
    - **task_id**: 任务ID
    """
    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
    
    task_status = task_storage[task_id]
    
    if task_status.get("status") != "completed":
        return {
            "task_id": task_id,
            "status": task_status.get("status", "unknown"),
            "message": "任务尚未完成"
        }
    
    return {
        "task_id": task_id,
        "status": "completed",
        "result": task_status.get("result", {})
    }

@app.post("/analyze/audio")
async def analyze_audio(
    background_tasks: BackgroundTasks,
    audio: UploadFile = File(...),
    request: str = Form(...),
):
    """
    分析音频内容
    
    - **audio**: 音频文件
    - **request**: JSON 格式的请求参数，包含 analysis_types 字段
    """
    try:
        # 解析请求参数
        request_data = json.loads(request)
        analysis_types = request_data.get("analysis_types", ["basic", "speech_to_text"])
        
        # 保存上传的音频文件
        temp_dir = os.path.join(os.getcwd(), "temp")
        os.makedirs(temp_dir, exist_ok=True)
        
        # 确保 filename 不为 None
        filename = audio.filename or f"uploaded_audio_{hash(str(audio))}.mp3"
        audio_path = os.path.join(temp_dir, filename)
        with open(audio_path, "wb") as f:
            f.write(await audio.read())
        
        # 分析音频
        result = audio_analyzer.analyze_audio(audio_path, analysis_types)
        
        return {
            "status": "completed",
            "result": result
        }
    except Exception as e:
        logger.error(f"音频分析失败: {e}")
        raise HTTPException(status_code=500, detail=f"音频分析失败: {e}")

@app.post("/edit/video")
async def edit_video(
    background_tasks: BackgroundTasks,
    video: UploadFile = File(...),
    request: str = Form(...),
):
    """
    编辑视频
    
    - **video**: 视频文件
    - **request**: JSON 格式的请求参数，包含编辑规则
    """
    try:
        # 解析请求参数
        request_data = json.loads(request)
        edit_request = EditRequest(**request_data)
        
        # 保存上传的视频文件
        temp_dir = os.path.join(os.getcwd(), "temp")
        os.makedirs(temp_dir, exist_ok=True)
        
        # 确保 filename 不为 None
        filename = video.filename or f"uploaded_video_{hash(str(video))}.mp4"
        video_path = os.path.join(temp_dir, filename)
        with open(video_path, "wb") as f:
            f.write(await video.read())
        
        # 创建任务ID
        task_id = f"edit_{os.path.basename(video_path)}_{hash(str(edit_request.model_dump()))}"
        
        # 添加后台任务
        background_tasks.add_task(process_video_editing, task_id, video_path, edit_request)
        
        # 初始化任务状态
        task_storage[task_id] = {
            "status": "processing",
            "video_path": video_path,
            "edit_request": edit_request.model_dump()
        }
        
        return {
            "task_id": task_id,
            "status": "processing",
            "message": "视频编辑任务已提交"
        }
    except Exception as e:
        logger.error(f"提交视频编辑任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"提交视频编辑任务失败: {e}")

@app.get("/edit/video/status/{task_id}")
async def get_video_editing_status(task_id: str):
    """
    获取视频编辑任务状态
    
    - **task_id**: 任务ID
    """
    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
    
    task_status = task_storage[task_id]
    
    return {
        "task_id": task_id,
        "status": task_status.get("status", "unknown")
    }

@app.get("/edit/video/result/{task_id}")
async def get_video_editing_result(task_id: str):
    """
    获取视频编辑任务结果
    
    - **task_id**: 任务ID
    """
    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
    
    task_status = task_storage[task_id]
    
    if task_status.get("status") != "completed":
        return {
            "task_id": task_id,
            "status": task_status.get("status", "unknown"),
            "message": "任务尚未完成"
        }
    
    return {
        "task_id": task_id,
        "status": "completed",
        "result": task_status.get("result", {})
    }

@app.get("/edit/video/download/{task_id}")
async def download_edited_video(task_id: str):
    """
    下载编辑后的视频
    
    - **task_id**: 任务ID
    """
    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
    
    task_status = task_storage[task_id]
    
    if task_status.get("status") != "completed":
        raise HTTPException(status_code=400, detail="任务尚未完成")
    
    result = task_status.get("result", {})
    output_path = result.get("output_path")
    
    if not output_path or not os.path.exists(output_path):
        raise HTTPException(status_code=404, detail="编辑后的视频文件不存在")
    
    return FileResponse(output_path, filename=os.path.basename(output_path))

@app.post("/publish/video")
async def publish_video(
    video: UploadFile = File(...),
    request: str = Form(...),
):
    """
    发布视频
    
    - **video**: 视频文件
    - **request**: JSON 格式的请求参数，包含发布信息
    """
    try:
        # 解析请求参数
        request_data = json.loads(request)
        publish_request = PublishRequest(**request_data)
        
        # 保存上传的视频文件
        temp_dir = os.path.join(os.getcwd(), "temp")
        os.makedirs(temp_dir, exist_ok=True)
        
        # 确保 filename 不为 None
        filename = video.filename or f"uploaded_video_{hash(str(video))}.mp4"
        video_path = os.path.join(temp_dir, filename)
        with open(video_path, "wb") as f:
            f.write(await video.read())
        
        # 准备元数据
        metadata = {
            "title": publish_request.title,
            "description": publish_request.description or f"由IntelliCutAgent自动发布的视频: {publish_request.title}",
            "tags": publish_request.tags or ["IntelliCutAgent", "自动发布"]
        }
        
        # 发布视频
        result = video_publisher.publish_video(
            video_path, 
            publish_request.platforms, 
            metadata, 
            publish_request.schedule_time
        )
        
        return result
    except Exception as e:
        logger.error(f"视频发布失败: {e}")
        raise HTTPException(status_code=500, detail=f"视频发布失败: {e}")

@app.get("/publish/platforms")
async def get_supported_platforms():
    """获取支持的发布平台"""
    return {
        "platforms": list(video_publisher.supported_platforms.keys()),
        "platform_names": video_publisher.supported_platforms
    }

@app.get("/publish/platform/{platform}/limits")
async def get_platform_limits(platform: str):
    """
    获取平台限制信息
    
    - **platform**: 平台名称
    """
    return video_publisher.get_platform_limits(platform)

@app.get("/publish/history")
async def get_publish_history(
    platform: Optional[str] = None,
    limit: int = Query(10, ge=1, le=100),
    offset: int = Query(0, ge=0),
):
    """
    获取发布历史记录
    
    - **platform**: 平台名称，可选
    - **limit**: 返回记录数量限制
    - **offset**: 记录偏移量
    """
    return video_publisher.get_publish_history(platform, limit, offset)

@app.post("/command")
async def process_command(command_request: CommandRequest):
    """
    处理命令
    
    - **command**: 命令字符串或JSON
    """
    try:
        # 解析命令
        result = input_parser.parse_user_command(command_request.command)
        
        # 如果是错误，直接返回
        if result.get("action") == "error":
            return result
        
        # 根据命令类型执行相应操作
        action = result.get("action")
        params = result.get("params", {})
        
        if action == "help":
            command = params.get("command")
            help_text = input_parser.get_command_help(command)
            return {
                "action": "help",
                "result": help_text
            }
        
        # 其他命令需要更多处理，这里只返回解析结果
        return {
            "action": action,
            "params": params,
            "message": "命令已解析，但尚未实现自动执行"
        }
    except Exception as e:
        logger.error(f"处理命令失败: {e}")
        raise HTTPException(status_code=500, detail=f"处理命令失败: {e}")

# 启动服务器
if __name__ == "__main__":
    uvicorn.run("api_server:app", host="0.0.0.0", port=8000, reload=True)