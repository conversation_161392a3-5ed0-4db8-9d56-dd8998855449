#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IntelliCutAgent 命令行界面
"""

import argparse
import json
import logging
import os
import sys

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# 导入模块
from backend.agent.agent_controller import AgentController
from backend.agent.perception_engine.audio_analyzer import AudioAnalyzer
from backend.agent.perception_engine.video_analyzer import VideoAnalyzer
from backend.agent.publisher.video_publisher import VideoPublisher
from backend.agent.smart_editor.smart_editor import SmartEditor

# 注释掉未使用的导入
# from backend.agent.learning_engine.trend_analyzer import TrendAnalyzer
# from backend.agent.learning_engine.feedback_processor import FeedbackProcessor
# from backend.agent.learning_engine.revenue_optimizer import RevenueOptimizer

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="IntelliCutAgent 命令行工具")

    # 创建子命令解析器
    subparsers = parser.add_subparsers(dest="command", help="子命令")

    # 分析视频命令
    analyze_parser = subparsers.add_parser("analyze", help="分析视频")
    analyze_parser.add_argument("video_path", help="视频文件路径")
    analyze_parser.add_argument(
        "--types",
        nargs="+",
        default=["basic", "scene_detection"],
        help="分析类型，可选值: basic, scene_detection, object_detection, face_detection, emotion_detection, action_recognition, text_detection, audio_analysis",
    )
    analyze_parser.add_argument("--output", help="输出文件路径")

    # 分析音频命令
    analyze_audio_parser = subparsers.add_parser("analyze-audio", help="分析音频")
    analyze_audio_parser.add_argument("audio_path", help="音频文件路径")
    analyze_audio_parser.add_argument(
        "--types",
        nargs="+",
        default=["basic", "speech_to_text"],
        help="分析类型，可选值: basic, speech_to_text, silence_detection, music_detection, emotion_analysis, speaker_diarization, audio_classification, beat_detection",
    )
    analyze_audio_parser.add_argument("--output", help="输出文件路径")

    # 编辑视频命令
    edit_parser = subparsers.add_parser("edit", help="编辑视频")
    edit_parser.add_argument("video_path", help="视频文件路径")
    edit_parser.add_argument("--output", help="输出文件路径")
    edit_parser.add_argument("--duration", default="60s", help="目标时长，例如: 60s, 5min")
    edit_parser.add_argument(
        "--style", default="standard", help="编辑风格，可选值: standard, action, emotional, documentary, vlog"
    )

    # 平台收益分析命令
    revenue_parser = subparsers.add_parser("revenue", help="平台收益分析")
    revenue_subparsers = revenue_parser.add_subparsers(dest="revenue_command", help="收益分析子命令")

    # 平台收益潜力分析
    platform_potential_parser = revenue_subparsers.add_parser("platform-potential", help="分析平台收益潜力")
    platform_potential_parser.add_argument(
        "--platforms",
        nargs="+",
        default=["youtube", "tiktok", "bilibili"],
        help="要分析的平台，可选值: youtube, tiktok, bilibili",
    )
    platform_potential_parser.add_argument(
        "--content-types",
        nargs="+",
        default=["tutorial", "entertainment", "gaming"],
        help="要分析的内容类型，可选值: tutorial, entertainment, gaming, news, lifestyle, technology, beauty, food, travel, fitness",
    )
    platform_potential_parser.add_argument(
        "--time-period", default="last_30_days", help="分析的时间周期，可选值: last_7_days, last_30_days, last_90_days"
    )
    platform_potential_parser.add_argument("--output", help="输出文件路径")

    # 视频收益分析
    video_revenue_parser = revenue_subparsers.add_parser("video-performance", help="分析视频收益表现")
    video_revenue_parser.add_argument("--video-id", required=True, help="视频ID")
    video_revenue_parser.add_argument(
        "--platform", required=True, choices=["youtube", "tiktok", "bilibili"], help="视频所在平台"
    )
    video_revenue_parser.add_argument("--content-type", default="general", help="内容类型")
    video_revenue_parser.add_argument("--metrics-file", required=True, help="视频指标数据文件路径（JSON格式）")
    video_revenue_parser.add_argument("--output", help="输出文件路径")

    # 收益优化策略生成
    optimization_strategy_parser = revenue_subparsers.add_parser("optimization-strategy", help="生成收益优化策略")
    optimization_strategy_parser.add_argument("--user-id", help="用户ID")
    optimization_strategy_parser.add_argument(
        "--platforms",
        nargs="+",
        default=["youtube", "tiktok", "bilibili"],
        help="目标平台，可选值: youtube, tiktok, bilibili",
    )
    optimization_strategy_parser.add_argument(
        "--content-preferences",
        nargs="+",
        default=["tutorial", "entertainment"],
        help="内容偏好，可选值: tutorial, entertainment, gaming, news, lifestyle, technology, beauty, food, travel, fitness",
    )
    optimization_strategy_parser.add_argument("--output", help="输出文件路径")

    # 收益趋势跟踪
    revenue_trends_parser = revenue_subparsers.add_parser("trends", help="跟踪收益趋势")
    revenue_trends_parser.add_argument("--user-id", help="用户ID")
    revenue_trends_parser.add_argument(
        "--time-periods",
        nargs="+",
        default=["last_7_days", "last_30_days", "last_90_days"],
        help="时间周期，可选值: last_7_days, last_30_days, last_90_days",
    )
    revenue_trends_parser.add_argument(
        "--platforms",
        nargs="+",
        default=["youtube", "tiktok", "bilibili"],
        help="平台，可选值: youtube, tiktok, bilibili",
    )
    revenue_trends_parser.add_argument("--output", help="输出文件路径")
    edit_parser.add_argument(
        "--transitions", nargs="+", default=["fade"], help="转场效果，可选值: fade, wipe, dissolve, slide, zoom, none"
    )
    edit_parser.add_argument(
        "--effects",
        nargs="+",
        default=[],
        help="视频效果，可选值: brightness, contrast, saturation, speed, blur, sharpen, none",
    )

    # 发布视频命令
    publish_parser = subparsers.add_parser("publish", help="发布视频")
    publish_parser.add_argument("video_path", help="视频文件路径")
    publish_parser.add_argument(
        "--platforms",
        nargs="+",
        required=True,
        help="目标平台，可选值: douyin, kuaishou, bilibili, youtube, weibo, xiaohongshu, wechat",
    )
    publish_parser.add_argument("--title", required=True, help="视频标题")
    publish_parser.add_argument("--description", help="视频描述")
    publish_parser.add_argument("--tags", nargs="+", help="视频标签")
    publish_parser.add_argument("--schedule", help="计划发布时间，格式: YYYY-MM-DD HH:MM:SS")

    # 分析市场命令
    market_parser = subparsers.add_parser("market", help="分析市场趋势和收益")
    market_parser.add_argument(
        "--platforms",
        nargs="+",
        help="目标平台，可选值: douyin, kuaishou, bilibili, youtube, weibo, xiaohongshu, wechat",
    )
    market_parser.add_argument("--categories", nargs="+", help="内容分类")
    market_parser.add_argument(
        "--time-range", default="week", choices=["day", "week", "month", "year"], help="时间范围"
    )
    market_parser.add_argument("--output", help="输出文件路径")

    # 优化内容命令
    optimize_parser = subparsers.add_parser("optimize", help="优化视频内容")
    optimize_parser.add_argument("video_data", help="视频数据文件路径 (JSON格式)")
    optimize_parser.add_argument(
        "--platforms",
        nargs="+",
        help="目标平台，可选值: douyin, kuaishou, bilibili, youtube, weibo, xiaohongshu, wechat",
    )
    optimize_parser.add_argument(
        "--level", default="standard", choices=["basic", "standard", "advanced", "professional"], help="优化级别"
    )
    optimize_parser.add_argument("--output", help="输出文件路径")

    # 分析收益命令
    revenue_parser = subparsers.add_parser("revenue", help="分析视频收益")
    revenue_subparsers = revenue_parser.add_subparsers(dest="revenue_command", help="收益分析子命令")

    # 分析单个视频收益
    analyze_revenue_parser = revenue_subparsers.add_parser("analyze", help="分析单个视频收益")
    analyze_revenue_parser.add_argument("platform", help="平台名称")
    analyze_revenue_parser.add_argument("video_id", help="视频ID")
    analyze_revenue_parser.add_argument("--output", help="输出文件路径")

    # 预测视频收益
    predict_revenue_parser = revenue_subparsers.add_parser("predict", help="预测视频收益")
    predict_revenue_parser.add_argument("platform", help="平台名称")
    predict_revenue_parser.add_argument("--content-type", help="内容类型")
    predict_revenue_parser.add_argument("--duration", type=float, help="视频时长 (秒)")
    predict_revenue_parser.add_argument("--expected-views", type=int, required=True, help="预期播放量")
    predict_revenue_parser.add_argument("--engagement-rate", type=float, default=0.05, help="预期互动率 (0-1)")
    predict_revenue_parser.add_argument("--output", help="输出文件路径")

    # 比较平台收益
    compare_platforms_parser = revenue_subparsers.add_parser("compare", help="比较平台收益")
    compare_platforms_parser.add_argument(
        "--platforms",
        nargs="+",
        help="目标平台，可选值: douyin, kuaishou, bilibili, youtube, weibo, xiaohongshu, wechat",
    )
    compare_platforms_parser.add_argument("--content-type", help="内容类型")
    compare_platforms_parser.add_argument("--duration", type=float, help="视频时长 (秒)")
    compare_platforms_parser.add_argument("--expected-views", type=int, required=True, help="预期播放量")
    compare_platforms_parser.add_argument("--output", help="输出文件路径")

    # 生成收益报告
    report_revenue_parser = revenue_subparsers.add_parser("report", help="生成收益报告")
    report_revenue_parser.add_argument("platform", help="平台名称")
    report_revenue_parser.add_argument("--start-date", help="开始日期，格式: YYYY-MM-DD")
    report_revenue_parser.add_argument("--end-date", help="结束日期，格式: YYYY-MM-DD")
    report_revenue_parser.add_argument("--format", default="json", choices=["json", "html", "pd"], help="输出格式")
    report_revenue_parser.add_argument("--output", help="输出文件路径")

    # 用户偏好设置命令
    preferences_parser = subparsers.add_parser("preferences", help="管理用户偏好设置")
    preferences_subparsers = preferences_parser.add_subparsers(dest="preferences_command", help="偏好设置子命令")

    # 获取用户偏好设置
    preferences_subparsers.add_parser("get", help="获取用户偏好设置")

    # 更新用户偏好设置
    update_preferences_parser = preferences_subparsers.add_parser("update", help="更新用户偏好设置")
    update_preferences_parser.add_argument("preferences_file", help="偏好设置文件路径 (JSON格式)")

    # 帮助命令
    help_parser = subparsers.add_parser("help", help="显示帮助信息")
    help_parser.add_argument("topic", nargs="?", help="帮助主题")

    return parser.parse_args()

def analyze_video(args):
    """分析视频"""
    logger.info("分析视频: {args.video_path}")

    # 检查视频文件是否存在
    if not os.path.exists(args.video_path):
        logger.error("视频文件不存在: {args.video_path}")
        return

    # 创建视频分析器
    analyzer = VideoAnalyzer()

    # 分析视频
    result = analyzer.analyze_video(args.video_path, args.types)

    # 打印分析结果
    print("\n视频分析结果:")
    print("  文件: {args.video_path}")
    print(f"  分析类型: {', '.join(args.types)}")

    # 打印基本信息
    if "duration" in result:
        print(f"  时长: {result['duration']}秒")
    if "resolution" in result:
        print(f"  分辨率: {result['resolution'][0]}x{result['resolution'][1]}")
    if "fps" in result:
        print(f"  帧率: {result['fps']}fps")

    # 打印场景信息
    if "scenes" in result:
        print(f"\n  检测到 {len(result['scenes'])} 个场景:")
        for i, scene in enumerate(result["scenes"][:5]):  # 只显示前5个场景
            print(
                f"    场景 {i+1}: {scene.get('start_time', 0):.2f}s - {scene.get('end_time', 0):.2f}s, 类型: {scene.get('type', '未知')}"
            )
        if len(result["scenes"]) > 5:
            print(f"    ... 还有 {len(result['scenes']) - 5} 个场景")

    # 打印物体信息
    if "objects" in result:
        print(f"\n  检测到 {len(result['objects'])} 个物体:")
        object_counts = {}
        for obj in result["objects"]:
            obj_class = obj.get("class", "未知")
            object_counts[obj_class] = object_counts.get(obj_class, 0) + 1

        for obj_class, count in sorted(object_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
            print("    {obj_class}: {count}个")
        if len(object_counts) > 10:
            print("    ... 还有 {len(object_counts) - 10} 种物体")

    # 打印人脸信息
    if "faces" in result:
        print(f"\n  检测到 {len(result['faces'])} 个人脸")

    # 打印情感信息
    if "emotions" in result:
        print(f"\n  检测到 {len(result['emotions'])} 个情感片段")

    # 保存分析结果
    if args.output:
        try:
            with open(args.output, "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print("\n分析结果已保存到: {args.output}")
        except Exception as e:
            logger.error("保存分析结果失败: {e}")

def analyze_audio(args):
    """分析音频"""
    logger.info("分析音频: {args.audio_path}")

    # 检查音频文件是否存在
    if not os.path.exists(args.audio_path):
        logger.error("音频文件不存在: {args.audio_path}")
        return

    # 创建音频分析器
    analyzer = AudioAnalyzer()

    # 分析音频
    result = analyzer.analyze_audio(args.audio_path, args.types)

    # 打印分析结果
    print("\n音频分析结果:")
    print("  文件: {args.audio_path}")
    print(f"  分析类型: {', '.join(args.types)}")

    # 打印基本信息
    if "duration_sec" in result:
        print(f"  时长: {result['duration_sec']}秒")
    if "sample_rate" in result:
        print(f"  采样率: {result['sample_rate']}Hz")
    if "channels" in result:
        print(f"  声道数: {result['channels']}")

    # 打印转录信息
    if "transcription" in result:
        print("\n  转录文本:")
        print(f"    {result['transcription'].get('full_text', '')[:200]}...")
        if len(result["transcription"].get("full_text", "")) > 200:
            print("    ... (已截断)")

    # 打印静音片段信息
    if "silence_segments" in result:
        print(f"\n  检测到 {len(result['silence_segments'])} 个静音片段")

    # 打印音乐片段信息
    if "music_segments" in result:
        print(f"\n  检测到 {len(result['music_segments'])} 个音乐片段")

    # 打印情感信息
    if "emotions" in result:
        print(f"\n  检测到 {len(result['emotions'])} 个情感片段")

    # 打印说话人信息
    if "speakers" in result:
        print(f"\n  检测到 {result['speakers'].get('num_speakers', 0)} 个说话人")

    # 保存分析结果
    if args.output:
        try:
            with open(args.output, "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print("\n分析结果已保存到: {args.output}")
        except Exception as e:
            logger.error("保存分析结果失败: {e}")

def edit_video(args):
    """编辑视频"""
    logger.info("编辑视频: {args.video_path}")

    # 检查视频文件是否存在
    if not os.path.exists(args.video_path):
        logger.error("视频文件不存在: {args.video_path}")
        return

    # 创建视频分析器和编辑器
    analyzer = VideoAnalyzer()
    editor = SmartEditor()

    # 分析视频
    print("正在分析视频...")
    analysis_result = analyzer.analyze_video(args.video_path, ["basic", "scene_detection"])

    # 准备编辑规则
    edit_rules = {
        "duration": args.duration,
        "style": args.style,
        "transitions": args.transitions,
        "effects": args.effects,
    }

    # 编辑视频
    print("正在编辑视频...")
    editor.auto_edit_video([args.video_path], analysis_result, edit_rules)

    print("\n视频编辑完成:")
    print("  输入文件: {args.video_path}")
    print("  输出文件: {output_path}")
    print("  编辑规则:")
    print("    时长: {args.duration}")
    print("    风格: {args.style}")
    print(f"    转场效果: {', '.join(args.transitions)}")
    print(f"    视频效果: {', '.join(args.effects) if args.effects else '无'}")

def publish_video(args):
    """发布视频"""
    logger.info("发布视频: {args.video_path}")

    # 检查视频文件是否存在
    if not os.path.exists(args.video_path):
        logger.error(f"视频文件不存在: {args.video_path}")
        return

    # 创建视频发布器
    publisher = VideoPublisher()

    # 准备元数据
    metadata = {
        "title": args.title,
        "description": args.description or "由IntelliCutAgent自动发布的视频: {args.title}",
        "tags": args.tags or ["IntelliCutAgent", "自动发布"],
    }

    # 发布视频
    print("正在发布视频...")
    result = publisher.publish_video(args.video_path, args.platforms, metadata, args.schedule)

    print("\n视频发布结果:")
    print(f"  状态: {result['status']}")
    print("  视频文件: {args.video_path}")
    print(f"  发布时间: {result['publish_time']}")
    if args.schedule:
        print("  计划发布时间: {args.schedule}")

    print("\n  平台发布状态:")
    for platform, platform_result in result.get("platforms", {}).items():
        print(f"    {platform}: {platform_result.get('status', '未知')}")
        if platform_result.get("status") == "success":
            print(f"      视频ID: {platform_result.get('video_id', '未知')}")
            print(f"      视频URL: {platform_result.get('video_url', '未知')}")

def show_help(args):
    """显示帮助信息"""
    if not args.topic:
        print(
            """
IntelliCutAgent 命令行工具帮助
==============================

可用命令:
  analyze       分析视频
  analyze-audio 分析音频
  edit          编辑视频
  publish       发布视频
  market        分析市场趋势和收益
  optimize      优化视频内容
  revenue       分析视频收益
  preferences   管理用户偏好设置
  help          显示帮助信息

使用 'help <命令>' 获取特定命令的详细帮助。
        """
        )
    elif args.topic == "analyze":
        print(
            """
分析视频命令
===========

用法: python cli.py analyze <video_path> [--types TYPE [TYPE ...]] [--output OUTPUT]

参数:
  video_path            视频文件路径
  --types TYPE [TYPE ...]
                        分析类型，可选值: basic, scene_detection, object_detection,
                        face_detection, emotion_detection, action_recognition,
                        text_detection, audio_analysis
  --output OUTPUT       输出文件路径

示例:
  python cli.py analyze video.mp4 --types basic scene_detection --output analysis.json
        """
        )
    elif args.topic == "analyze-audio":
        print(
            """
分析音频命令
===========

用法: python cli.py analyze-audio <audio_path> [--types TYPE [TYPE ...]] [--output OUTPUT]

参数:
  audio_path            音频文件路径
  --types TYPE [TYPE ...]
                        分析类型，可选值: basic, speech_to_text, silence_detection,
                        music_detection, emotion_analysis, speaker_diarization,
                        audio_classification, beat_detection
  --output OUTPUT       输出文件路径

示例:
  python cli.py analyze-audio audio.mp3 --types basic speech_to_text --output analysis.json
        """
        )
    elif args.topic == "edit":
        print(
            """
编辑视频命令
===========

用法: python cli.py edit <video_path> [--output OUTPUT] [--duration DURATION]
                        [--style STYLE] [--transitions TRANSITION [TRANSITION ...]]
                        [--effects EFFECT [EFFECT ...]]

参数:
  video_path            视频文件路径
  --output OUTPUT       输出文件路径
  --duration DURATION   目标时长，例如: 60s, 5min
  --style STYLE         编辑风格，可选值: standard, action, emotional, documentary, vlog
  --transitions TRANSITION [TRANSITION ...]
                        转场效果，可选值: fade, wipe, dissolve, slide, zoom, none
  --effects EFFECT [EFFECT ...]
                        视频效果，可选值: brightness, contrast, saturation, speed, blur, sharpen, none

示例:
  python cli.py edit video.mp4 --duration 30s --style action --transitions fade wipe --effects contrast speed
        """
        )
    elif args.topic == "publish":
        print(
            """
发布视频命令
===========

用法: python cli.py publish <video_path> --platforms PLATFORM [PLATFORM ...] --title TITLE
                           [--description DESCRIPTION] [--tags TAG [TAG ...]] [--schedule SCHEDULE]

参数:
  video_path            视频文件路径
  --platforms PLATFORM [PLATFORM ...]
                        目标平台，可选值: douyin, kuaishou, bilibili, youtube, weibo, xiaohongshu, wechat
  --title TITLE         视频标题
  --description DESCRIPTION
                        视频描述
  --tags TAG [TAG ...]  视频标签
  --schedule SCHEDULE   计划发布时间，格式: YYYY-MM-DD HH:MM:SS

示例:
  python cli.py publish video.mp4 --platforms douyin bilibili --title "测试视频" --tags 测试 IntelliCutAgent
        """
        )
    elif args.topic == "market":
        print(
            """
分析市场命令
===========

用法: python cli.py market [--platforms PLATFORM [PLATFORM ...]] [--categories CATEGORY [CATEGORY ...]]
                          [--time-range {day,week,month,year}] [--output OUTPUT]

参数:
  --platforms PLATFORM [PLATFORM ...]
                        目标平台，可选值: douyin, kuaishou, bilibili, youtube, weibo, xiaohongshu, wechat
  --categories CATEGORY [CATEGORY ...]
                        内容分类
  --time-range {day,week,month,year}
                        时间范围，默认: week
  --output OUTPUT       输出文件路径

示例:
  python cli.py market --platforms douyin bilibili --time-range month --output market_analysis.json
        """
        )
    elif args.topic == "optimize":
        print(
            """
优化内容命令
===========

用法: python cli.py optimize <video_data> [--platforms PLATFORM [PLATFORM ...]]
                            [--level {basic,standard,advanced,professional}] [--output OUTPUT]

参数:
  video_data            视频数据文件路径 (JSON格式)
  --platforms PLATFORM [PLATFORM ...]
                        目标平台，可选值: douyin, kuaishou, bilibili, youtube, weibo, xiaohongshu, wechat
  --level {basic,standard,advanced,professional}
                        优化级别，默认: standard
  --output OUTPUT       输出文件路径

示例:
  python cli.py optimize video_data.json --platforms douyin --level advanced --output optimized_data.json
        """
        )
    elif args.topic == "revenue":
        print(
            """
分析收益命令
===========

用法: python cli.py revenue <子命令> [参数]

子命令:
  analyze               分析单个视频收益
  predict               预测视频收益
  compare               比较平台收益
  report                生成收益报告

示例:
  python cli.py revenue analyze douyin video123 --output revenue_analysis.json
  python cli.py revenue predict douyin --content-type 舞蹈 --duration 60 --expected-views 10000
  python cli.py revenue compare --platforms douyin bilibili --expected-views 10000
  python cli.py revenue report douyin --format html
        """
        )
    elif args.topic == "revenue analyze":
        print(
            """
分析单个视频收益命令
=================

用法: python cli.py revenue analyze <platform> <video_id> [--output OUTPUT]

参数:
  platform              平台名称
  video_id              视频ID
  --output OUTPUT       输出文件路径

示例:
  python cli.py revenue analyze douyin video123 --output revenue_analysis.json
        """
        )
    elif args.topic == "revenue predict":
        print(
            """
预测视频收益命令
=============

用法: python cli.py revenue predict <platform> [--content-type CONTENT_TYPE] [--duration DURATION]
                                   --expected-views EXPECTED_VIEWS [--engagement-rate ENGAGEMENT_RATE]
                                   [--output OUTPUT]

参数:
  platform              平台名称
  --content-type CONTENT_TYPE
                        内容类型
  --duration DURATION   视频时长 (秒)
  --expected-views EXPECTED_VIEWS
                        预期播放量
  --engagement-rate ENGAGEMENT_RATE
                        预期互动率 (0-1)，默认: 0.05
  --output OUTPUT       输出文件路径

示例:
  python cli.py revenue predict douyin --content-type 舞蹈 --duration 60 --expected-views 10000
        """
        )
    elif args.topic == "revenue compare":
        print(
            """
比较平台收益命令
=============

用法: python cli.py revenue compare [--platforms PLATFORM [PLATFORM ...]] [--content-type CONTENT_TYPE]
                                   [--duration DURATION] --expected-views EXPECTED_VIEWS [--output OUTPUT]

参数:
  --platforms PLATFORM [PLATFORM ...]
                        目标平台，可选值: douyin, kuaishou, bilibili, youtube, weibo, xiaohongshu, wechat
  --content-type CONTENT_TYPE
                        内容类型
  --duration DURATION   视频时长 (秒)
  --expected-views EXPECTED_VIEWS
                        预期播放量
  --output OUTPUT       输出文件路径

示例:
  python cli.py revenue compare --platforms douyin bilibili --expected-views 10000
        """
        )
    elif args.topic == "revenue report":
        print(
            """
生成收益报告命令
=============

用法: python cli.py revenue report <platform> [--start-date START_DATE] [--end-date END_DATE]
                                  [--format {json,html,pdf}] [--output OUTPUT]

参数:
  platform              平台名称
  --start-date START_DATE
                        开始日期，格式: YYYY-MM-DD
  --end-date END_DATE   结束日期，格式: YYYY-MM-DD
  --format {json,html,pdf}
                        输出格式，默认: json
  --output OUTPUT       输出文件路径

示例:
  python cli.py revenue report douyin --start-date 2023-01-01 --end-date 2023-12-31 --format html
        """
        )
    elif args.topic == "preferences":
        print(
            """
管理用户偏好设置命令
=================

用法: python cli.py preferences <子命令> [参数]

子命令:
  get                   获取用户偏好设置
  update                更新用户偏好设置

示例:
  python cli.py preferences get
  python cli.py preferences update preferences.json
        """
        )
    else:
        print("未知的帮助主题: {args.topic}")

def main():
    """主函数"""
    args = parse_arguments()

    # 创建智能代理控制器
    agent = AgentController()

    try:
        if args.command == "analyze":
            # 分析视频
            command = {
                "action": "analyze_video",
                "params": {"video_path": args.video_path, "analysis_types": args.types, "output_path": args.output},
            }
            result = agent.process_command(json.dumps(command))
            print_result(result)

        elif args.command == "analyze-audio":
            # 分析音频
            command = {
                "action": "analyze_audio",
                "params": {"audio_path": args.audio_path, "analysis_types": args.types, "output_path": args.output},
            }
            result = agent.process_command(json.dumps(command))
            print_result(result)

        elif args.command == "edit":
            # 编辑视频
            command = {
                "action": "edit_video",
                "params": {
                    "video_path": args.video_path,
                    "output_path": args.output,
                    "edit_rules": {
                        "duration": args.duration,
                        "style": args.style,
                        "transitions": args.transitions,
                        "effects": args.effects,
                    },
                },
            }
            result = agent.process_command(json.dumps(command))
            print_result(result)

        elif args.command == "publish":
            # 发布视频
            command = {
                "action": "publish_video",
                "params": {
                    "video_path": args.video_path,
                    "platforms": args.platforms,
                    "metadata": {"title": args.title, "description": args.description, "tags": args.tags},
                    "schedule_time": args.schedule,
                },
            }
            result = agent.process_command(json.dumps(command))
            print_result(result)

        elif args.command == "market":
            # 分析市场
            command = {
                "action": "analyze_market",
                "params": {"platforms": args.platforms, "categories": args.categories, "time_range": args.time_range},
            }
            result = agent.process_command(json.dumps(command))
            print_result(result)

            # 如果指定了输出路径，保存结果
            if args.output and result.get("status") == "success":
                with open(args.output, "w", encoding="utf-8") as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                print("\n分析结果已保存到: {args.output}")

        elif args.command == "optimize":
            # 优化内容
            # 加载视频数据
            try:
                with open(args.video_data, "r", encoding="utf-8") as f:
                    video_data = json.load(f)
            except Exception as e:
                print(f"加载视频数据失败: {e}")
                return

            command = {
                "action": "optimize_content",
                "params": {
                    "video_data": video_data,
                    "target_platforms": args.platforms,
                    "optimization_level": args.level,
                },
            }
            result = agent.process_command(json.dumps(command))
            print_result(result)

            # 如果指定了输出路径，保存结果
            if args.output and result.get("status") == "success":
                with open(args.output, "w", encoding="utf-8") as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                print("\n优化结果已保存到: {args.output}")

        elif args.command == "revenue":
            if args.revenue_command == "analyze":
                # 分析视频收益
                command = {
                    "action": "analyze_revenue",
                    "params": {"platform": args.platform, "video_id": args.video_id},
                }
                result = agent.process_command(json.dumps(command))
                print_result(result)

                # 如果指定了输出路径，保存结果
                if args.output and result.get("status") == "success":
                    with open(args.output, "w", encoding="utf-8") as f:
                        json.dump(result, f, ensure_ascii=False, indent=2)
                    print("\n分析结果已保存到: {args.output}")

            elif args.revenue_command == "predict":
                # 预测视频收益
                command = {
                    "action": "predict_revenue",
                    "params": {
                        "platform": args.platform,
                        "video_features": {
                            "content_type": args.content_type,
                            "duration": args.duration,
                            "expected_views": args.expected_views,
                            "expected_engagement_rate": args.engagement_rate,
                        },
                    },
                }
                result = agent.process_command(json.dumps(command))
                print_result(result)

                # 如果指定了输出路径，保存结果
                if args.output and result.get("status") == "success":
                    with open(args.output, "w", encoding="utf-8") as f:
                        json.dump(result, f, ensure_ascii=False, indent=2)
                    print("\n预测结果已保存到: {args.output}")

            elif args.revenue_command == "compare":
                # 比较平台收益
                command = {
                    "action": "compare_platforms",
                    "params": {
                        "platforms": args.platforms,
                        "video_features": {
                            "content_type": args.content_type,
                            "duration": args.duration,
                            "expected_views": args.expected_views,
                        },
                    },
                }
                result = agent.process_command(json.dumps(command))
                print_result(result)

                # 如果指定了输出路径，保存结果
                if args.output and result.get("status") == "success":
                    with open(args.output, "w", encoding="utf-8") as f:
                        json.dump(result, f, ensure_ascii=False, indent=2)
                    print("\n比较结果已保存到: {args.output}")

            elif args.revenue_command == "report":
                # 生成收益报告
                command = {
                    "action": "generate_revenue_report",
                    "params": {
                        "platform": args.platform,
                        "start_date": args.start_date,
                        "end_date": args.end_date,
                        "output_format": args.format,
                    },
                }
                result = agent.process_command(json.dumps(command))
                print_result(result)

                # 如果指定了输出路径，保存结果
                if args.output and result.get("status") == "success":
                    with open(args.output, "w", encoding="utf-8") as f:
                        json.dump(result, f, ensure_ascii=False, indent=2)
                    print("\n报告已保存到: {args.output}")

            else:
                print("未知的收益分析命令: {args.revenue_command}")
                print("使用 'help revenue' 命令获取帮助。")

        elif args.command == "preferences":
            if args.preferences_command == "get":
                # 获取用户偏好设置
                result = agent.get_user_preferences()
                print_result(result)

            elif args.preferences_command == "update":
                # 更新用户偏好设置
                try:
                    with open(args.preferences_file, "r", encoding="utf-8") as f:
                        preferences = json.load(f)
                except Exception as e:
                    print("加载偏好设置文件失败: {e}")
                    return

                result = agent.update_user_preferences(preferences)
                print_result(result)

            else:
                print("未知的偏好设置命令: {args.preferences_command}")
                print("使用 'help preferences' 命令获取帮助。")

        elif args.command == "help" or args.command is None:
            show_help(args)

        else:
            print("未知命令: {args.command}")
            print("使用 'help' 命令获取帮助。")

    except Exception as e:
        print("执行命令时出错: {e}")
        import traceback

        traceback.print_exc()

def print_result(result):
    """打印结果"""
    if not result:
        print("没有结果")
        return

    if result.get("status") == "error":
        print(f"\n错误: {result.get('message', '未知错误')}")
        return

    print(f"\n{result.get('message', '操作成功')}")

    # 根据结果类型打印详细信息
    if "action" in result:
        action = result["action"]

        if action == "analyze_video":
            # 打印视频分析结果
            analysis_result = result.get("result", {})
            print("\n视频分析结果:")
            print(f"  文件: {result.get('video_path')}")
            print(f"  分析类型: {', '.join(result.get('analysis_types', []))}")

            # 打印基本信息
            if "duration" in analysis_result:
                print(f"  时长: {analysis_result['duration']}秒")
            if "resolution" in analysis_result:
                print(f"  分辨率: {analysis_result['resolution'][0]}x{analysis_result['resolution'][1]}")
            if "fps" in analysis_result:
                print(f"  帧率: {analysis_result['fps']}fps")

            # 打印场景信息
            if "scenes" in analysis_result:
                print(f"\n  检测到 {len(analysis_result['scenes'])} 个场景:")
                for i, scene in enumerate(analysis_result["scenes"][:5]):  # 只显示前5个场景
                    print(
                        f"    场景 {i+1}: {scene.get('start_time', 0):.2f}s - {scene.get('end_time', 0):.2f}s, 类型: {scene.get('type', '未知')}"
                    )
                if len(analysis_result["scenes"]) > 5:
                    print(f"    ... 还有 {len(analysis_result['scenes']) - 5} 个场景")

        elif action == "analyze_audio":
            # 打印音频分析结果
            analysis_result = result.get("result", {})
            print("\n音频分析结果:")
            print(f"  文件: {result.get('audio_path')}")
            print(f"  分析类型: {', '.join(result.get('analysis_types', []))}")

            # 打印基本信息
            if "duration_sec" in analysis_result:
                print(f"  时长: {analysis_result['duration_sec']}秒")
            if "sample_rate" in analysis_result:
                print(f"  采样率: {analysis_result['sample_rate']}Hz")
            if "channels" in analysis_result:
                print(f"  声道数: {analysis_result['channels']}")

            # 打印转录信息
            if "transcription" in analysis_result:
                print("\n  转录文本:")
                print(f"    {analysis_result['transcription'].get('full_text', '')[:200]}...")
                if len(analysis_result["transcription"].get("full_text", "")) > 200:
                    print("    ... (已截断)")

        elif action == "edit_video":
            # 打印视频编辑结果
            print("\n视频编辑完成:")
            print(f"  输入文件: {result.get('video_path')}")
            print(f"  输出文件: {result.get('edited_video_path')}")
            print("  编辑规则:")
            edit_rules = result.get("edit_rules", {})
            print(f"    时长: {edit_rules.get('duration', '未知')}")
            print(f"    风格: {edit_rules.get('style', '未知')}")
            print(f"    转场效果: {', '.join(edit_rules.get('transitions', []))}")
            print(f"    视频效果: {', '.join(edit_rules.get('effects', [])) if edit_rules.get('effects') else '无'}")

        elif action == "publish_video":
            # 打印视频发布结果
            publish_result = result.get("result", {})
            print("\n视频发布结果:")
            print(f"  状态: {publish_result.get('status', '未知')}")
            print(f"  视频文件: {result.get('video_path')}")
            print(f"  发布时间: {publish_result.get('publish_time', '未知')}")

            print("\n  平台发布状态:")
            for platform, platform_result in publish_result.get("platforms", {}).items():
                print(f"    {platform}: {platform_result.get('status', '未知')}")
                if platform_result.get("status") == "success":
                    print(f"      视频ID: {platform_result.get('video_id', '未知')}")
                    print(f"      视频URL: {platform_result.get('video_url', '未知')}")

        elif action == "analyze_market":
            # 打印市场分析结果
            print("\n市场分析完成:")
            print(f"  平台: {', '.join(result.get('platforms', []))}")

            # 打印趋势分析结果
            trend_analysis = result.get("trend_analysis", {})
            if trend_analysis:
                print("\n内容趋势分析:")
                print(f"  时间范围: {trend_analysis.get('time_range', '未知')}")

                # 打印热门内容类型
                trend_data = trend_analysis.get("trend_analysis", {})
                popular_types = trend_data.get("popular_types", [])
                if popular_types:
                    print("\n  热门内容类型:")
                    for content_type, count in popular_types[:5]:
                        print("    {content_type}: {count}次")

            # 打印收益分析结果
            revenue_analysis = result.get("revenue_analysis", {})
            if revenue_analysis:
                print("\n平台收益分析:")

                # 打印平台比较
                platform_comparison = revenue_analysis.get("platform_comparison", {})
                if platform_comparison:
                    print("\n  平台收益排名:")
                    platform_ranking = platform_comparison.get("platform_ranking", [])
                    for i, platform_data in enumerate(platform_ranking[:5]):
                        print(
                            f"    {i+1}. {platform_data.get('platform_name', platform_data.get('platform', '未知'))}: {platform_data.get('total_revenue', 0)}元"
                        )

            # 打印内容策略
            content_strategy = result.get("content_strategy", {})
            if content_strategy:
                strategy = content_strategy.get("strategy", {})
                if strategy:
                    print("\n内容策略建议:")

                    # 打印平台优先级
                    platform_priority = strategy.get("platform_priority", [])
                    if platform_priority:
                        print(f"\n  平台优先级: {', '.join(platform_priority)}")

                    # 打印一般建议
                    general_recommendations = strategy.get("general_recommendations", [])
                    if general_recommendations:
                        print("\n  一般建议:")
                        for i, recommendation in enumerate(general_recommendations):
                            print("    {i+1}. {recommendation}")

        elif action == "optimize_content":
            # 打印内容优化结果
            print("\n内容优化完成:")
            print(f"  视频ID: {result.get('video_id', '未知')}")

            # 打印优化建议
            optimization_suggestions = result.get("optimization_suggestions", {})
            if optimization_suggestions:
                print("\n优化建议:")

                # 打印平台建议
                platform_suggestions = optimization_suggestions.get("platform_suggestions", {})
                for platform, suggestions in platform_suggestions.items():
                    print("\n  {platform} 平台建议:")

                    # 打印内容建议
                    content_suggestions = suggestions.get("content_suggestions", {})
                    for feature, suggestion in content_suggestions.items():
                        if "suggestion" in suggestion:
                            print(f"    {feature}: {suggestion['suggestion']}")

        elif action in ["analyze_revenue", "predict_revenue", "compare_platforms", "generate_revenue_report"]:
            # 打印收益相关结果
            if action == "analyze_revenue":
                # 打印视频收益分析结果
                revenue_analysis = result.get("revenue_analysis", {})
                if revenue_analysis:
                    print("\n视频收益分析:")
                    print(f"  平台: {result.get('platform', '未知')}")
                    print(f"  视频ID: {result.get('video_id', '未知')}")

                    # 打印收益指标
                    metrics = revenue_analysis.get("revenue_analysis", {}).get("metrics", {})
                    if metrics:
                        print("\n  收益指标:")
                        print(f"    收益: {metrics.get('revenue', 0)}元")
                        print(f"    播放量: {metrics.get('views', 0)}次")
                        print(f"    RPM: {metrics.get('rpm', 0)}元/千次播放")
                        print(f"    互动率: {metrics.get('engagement_rate', 0)}%")

            elif action == "predict_revenue":
                # 打印收益预测结果
                revenue_prediction = result.get("revenue_prediction", {})
                if revenue_prediction:
                    prediction = revenue_prediction.get("prediction", {})
                    print("\n收益预测结果:")
                    print(f"  平台: {result.get('platform', '未知')}")
                    print(f"  预期播放量: {prediction.get('expected_views', 0)}次")
                    print(f"  预期RPM: {prediction.get('expected_rpm', 0)}元/千次播放")
                    print(f"  预期收益: {prediction.get('expected_revenue', 0)}元")

            elif action == "compare_platforms":
                # 打印平台比较结果
                comparison = result.get("platform_comparison", {})
                if comparison:
                    print("\n平台比较结果:")

                    # 打印最佳平台
                    comparison.get("comparison", {}).get("best_platform", "未知")
                    comparison.get("comparison", {}).get("best_platform_revenue", 0)
                    print("\n  最佳平台: {best_platform} (预期收益: {best_revenue}元)")

                    # 打印平台排名
                    platform_ranking = comparison.get("comparison", {}).get("platform_ranking", [])
                    if platform_ranking:
                        print("\n  平台收益排名:")
                        for i, platform_data in enumerate(platform_ranking):
                            print(
                                f"    {i+1}. {platform_data.get('platform_name', platform_data.get('platform', '未知'))}: {platform_data.get('expected_revenue', 0)}元"
                            )

            elif action == "generate_revenue_report":
                # 打印收益报告结果
                revenue_report = result.get("revenue_report", {})
                if revenue_report:
                    report = revenue_report.get("report", {})
                    print("\n收益报告:")
                    print(f"  平台: {report.get('platform_name', report.get('platform', '未知'))}")
                    print(f"  时间范围: {report.get('start_date', '全部')} 至 {report.get('end_date', '全部')}")
                    print(f"  视频数量: {report.get('video_count', 0)}个")
                    print(f"  总收益: {report.get('total_revenue', 0)}元")
                    print(f"  总播放量: {report.get('total_views', 0)}次")
                    print(f"  平均RPM: {report.get('avg_rpm', 0)}元/千次播放")

                    # 如果有报告路径，显示
                    if "report_path" in report:
                        print(f"\n  报告文件: {report['report_path']}")
    else:
        # 通用结果打印
        for key, value in result.items():
            if key not in ["status", "message", "task_id"]:
                print("  {key}: {value}")

if __name__ == "__main__":
    main()
