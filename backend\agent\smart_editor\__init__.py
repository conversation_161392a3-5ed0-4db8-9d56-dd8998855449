"""
智能视频编辑模块
"""
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

try:
    # 导入视频编辑器
    from .video_editor import VideoEditor
    
    # 导入视频摘要扩展
    from .video_editor_summary import add_summary_method_to_video_editor
    
    # 添加视频摘要方法
    add_summary_method_to_video_editor()
    
    logger.info("智能视频编辑模块初始化成功")
except ImportError as e:
    logger.warning(f"无法导入VideoEditor，将使用模拟实现: {e}")
    
    # 创建模拟实现
    class VideoEditor:
        """模拟的视频编辑器类"""
        def __init__(self, *args, **kwargs):
            logger.warning("使用的是模拟的VideoEditor实现")
            
        def __getattr__(self, name):
            def mock_method(*args, **kwargs):
                logger.warning(f"调用了模拟方法: {name}")
                return None
            return mock_method