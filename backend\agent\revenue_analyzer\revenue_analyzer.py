# backend.agent.revenue_analyzer.revenue_analyzer

import json
import logging
import os
from datetime import datetime
from typing import Any
from typing import Dict
from typing import List
from typing import Optional

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


class RevenueAnalyzer:
    """
    收益分析器：分析视频收益情况，提供收益优化策略
    """

    def __init__(self, config_dir: str = None, data_dir: str = None, output_dir: str = None):
        """
        初始化收益分析器

        Args:
            config_dir: 配置文件目录，默认为当前目录下的 'config'
            data_dir: 数据存储目录，默认为当前目录下的 'data/revenue'
            output_dir: 输出目录，默认为当前目录下的 'output/revenue'
        """
        self.config_dir = config_dir or os.path.join(os.getcwd(), "config")
        self.data_dir = data_dir or os.path.join(os.getcwd(), "data", "revenue")
        self.output_dir = output_dir or os.path.join(os.getcwd(), "output", "revenue")

        # 确保目录存在
        os.makedirs(self.config_dir, exist_ok=True)
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)

        # 支持的平台
        self.supported_platforms = {
            "douyin": "抖音",
            "kuaishou": "快手",
            "bilibili": "B站",
            "youtube": "YouTube",
            "weibo": "微博",
            "xiaohongshu": "小红书",
            "wechat": "微信视频号",
        }

        # 加载收益数据
        self.revenue_data = self._load_revenue_data()

        # 加载收益模型
        self.revenue_models = self._load_revenue_models()

        logger.info("RevenueAnalyzer 初始化完成")

    def _load_revenue_data(self) -> Dict[str, pd.DataFrame]:
        """
        加载收益数据

        Returns:
            收益数据字典，键为平台名称，值为DataFrame
        """
        revenue_data = {}

        for platform in self.supported_platforms:
            data_file = os.path.join(self.data_dir, "{platform}_revenue.csv")

            if os.path.exists(data_file):
                try:
                    df = pd.read_csv(data_file)
                    revenue_data[platform] = df
                    logger.info("已加载 {platform} 平台收益数据: {len(df)} 条记录")
                except Exception:
                    logger.error("操作失败")
                    revenue_data[platform] = pd.DataFrame()
            else:
                logger.info("{platform} 平台收益数据文件不存在")
                revenue_data[platform] = pd.DataFrame()

        return revenue_data

    def _save_revenue_data(self, platform: str, data: pd.DataFrame) -> None:
        """
        保存收益数据

        Args:
            platform: 平台名称
            data: 收益数据
        """
        if data.empty:
            return

        data_file = os.path.join(self.data_dir, "{platform}_revenue.csv")

        try:
            data.to_csv(data_file, index=False)
            logger.info("已保存 {platform} 平台收益数据: {len(data)} 条记录")
        except Exception:
            logger.error("操作失败")

    def _load_revenue_models(self) -> Dict[str, Any]:
        """
        加载收益模型

        Returns:
            收益模型字典，键为平台名称，值为模型
        """
        revenue_models = {}

        for platform in self.supported_platforms:
            model_file = os.path.join(self.data_dir, "{platform}_revenue_model.json")

            if os.path.exists(model_file):
                try:
                    with open(model_file, "r", encoding="utf-8") as f:
                        revenue_models[platform] = json.load(f)
                    logger.info("已加载 {platform} 平台收益模型")
                except Exception:
                    logger.error("操作失败")
                    revenue_models[platform] = self._get_default_revenue_model(platform)
            else:
                logger.info("{platform} 平台收益模型文件不存在，使用默认模型")
                revenue_models[platform] = self._get_default_revenue_model(platform)

        return revenue_models

    def _get_default_revenue_model(self, platform: str) -> Dict[str, Any]:
        """
        获取默认收益模型

        Args:
            platform: 平台名称

        Returns:
            默认收益模型
        """
        # 不同平台的默认收益模型
        default_models = {
            "douyin": {
                "base_rate": 0.01,  # 每千次播放的基础收益 (元)
                "engagement_multiplier": 1.5,  # 互动率对收益的影响系数
                "follower_multiplier": 1.2,  # 粉丝数对收益的影响系数
                "content_type_multipliers": {
                    "舞蹈": 1.2,
                    "搞笑": 1.3,
                    "美食": 1.1,
                    "旅行": 1.0,
                    "游戏": 0.9,
                    "知识": 1.4,
                    "美妆": 1.2,
                    "宠物": 1.1,
                    "音乐": 1.0,
                    "运动": 1.0,
                },
                "duration_multipliers": {"15s": 0.8, "30s": 1.0, "60s": 1.2, "90s": 1.1, "120s+": 1.0},
                "time_factors": {
                    "weekday": 1.0,
                    "weekend": 1.2,
                    "morning": 0.9,
                    "noon": 1.0,
                    "evening": 1.3,
                    "night": 1.1,
                },
            },
            "kuaishou": {
                "base_rate": 0.008,
                "engagement_multiplier": 1.4,
                "follower_multiplier": 1.3,
                "content_type_multipliers": {
                    "搞笑": 1.4,
                    "美食": 1.2,
                    "生活": 1.0,
                    "游戏": 0.9,
                    "音乐": 1.0,
                    "舞蹈": 1.1,
                    "宠物": 1.2,
                    "汽车": 1.3,
                    "时尚": 1.1,
                    "教育": 1.3,
                },
                "duration_multipliers": {"15s": 0.8, "30s": 1.0, "60s": 1.2, "90s": 1.1, "120s+": 1.0},
                "time_factors": {
                    "weekday": 1.0,
                    "weekend": 1.2,
                    "morning": 0.9,
                    "noon": 1.0,
                    "evening": 1.3,
                    "night": 1.1,
                },
            },
            "bilibili": {
                "base_rate": 0.006,
                "engagement_multiplier": 1.6,
                "follower_multiplier": 1.1,
                "content_type_multipliers": {
                    "游戏": 1.2,
                    "动画": 1.3,
                    "音乐": 1.1,
                    "舞蹈": 1.0,
                    "知识": 1.4,
                    "美食": 1.0,
                    "生活": 0.9,
                    "时尚": 0.8,
                    "科技": 1.3,
                    "电影": 1.2,
                },
                "duration_multipliers": {"60s": 0.8, "180s": 1.0, "300s": 1.2, "600s": 1.3, "900s+": 1.4},
                "time_factors": {
                    "weekday": 0.9,
                    "weekend": 1.3,
                    "morning": 0.8,
                    "noon": 0.9,
                    "evening": 1.4,
                    "night": 1.2,
                },
            },
        }

        # 返回平台特定的模型，如果没有则返回通用模型
        return default_models.get(
            platform,
            {
                "base_rate": 0.005,
                "engagement_multiplier": 1.2,
                "follower_multiplier": 1.0,
                "content_type_multipliers": {"娱乐": 1.0, "知识": 1.2, "生活": 0.9, "游戏": 0.8, "音乐": 0.9},
                "duration_multipliers": {"30s": 0.8, "60s": 1.0, "180s": 1.1, "300s+": 1.2},
                "time_factors": {
                    "weekday": 1.0,
                    "weekend": 1.1,
                    "morning": 0.9,
                    "noon": 1.0,
                    "evening": 1.2,
                    "night": 1.0,
                },
            },
        )

    def add_revenue_data(self, platform: str, video_id: str, revenue_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        添加收益数据

        Args:
            platform: 平台名称
            video_id: 视频ID
            revenue_data: 收益数据

        Returns:
            添加结果
        """
        logger.info("添加 {platform} 平台视频 {video_id} 的收益数据")

        # 检查平台是否支持
        if platform not in self.supported_platforms:
            logger.error(f"不支持的平台: {platform}")
            return {"status": "error", "message": f"不支持的平台: {platform}"}

        # 准备数据
        data = {"video_id": video_id, "platform": platform, "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

        # 添加收益数据
        for key, value in revenue_data.items():
            data[key] = value

        # 转换为DataFrame
        df = pd.DataFrame([data])

        # 如果已有数据，则合并
        if platform in self.revenue_data and not self.revenue_data[platform].empty:
            # 检查是否已存在该视频的数据
            existing_data = self.revenue_data[platform]
            existing_video = existing_data[existing_data["video_id"] == video_id]

            if not existing_video.empty:
                # 更新现有数据
                existing_data.loc[existing_data["video_id"] == video_id] = df.iloc[0]
                self.revenue_data[platform] = existing_data
            else:
                # 添加新数据
                self.revenue_data[platform] = pd.concat([existing_data, df], ignore_index=True)
        else:
            # 创建新数据
            self.revenue_data[platform] = df

        # 保存数据
        self._save_revenue_data(platform, self.revenue_data[platform])

        return {"status": "success", "message": "已添加 {platform} 平台视频 {video_id} 的收益数据"}

    def analyze_video_revenue(self, platform: str, video_id: str) -> Dict[str, Any]:
        """
        分析视频收益

        Args:
            platform: 平台名称
            video_id: 视频ID

        Returns:
            收益分析结果
        """
        logger.info("分析 {platform} 平台视频 {video_id} 的收益")

        # 检查平台是否支持
        if platform not in self.supported_platforms:
            logger.error(f"不支持的平台: {platform}")
            return {"status": "error", "message": "不支持的平台: {platform}"}

        # 获取平台数据
        platform_data = self.revenue_data.get(platform, pd.DataFrame())

        if platform_data.empty:
            logger.warning(f"没有 {platform} 平台的收益数据")
            return {"status": "error", "message": "没有 {platform} 平台的收益数据"}

        # 查找视频数据
        video_data = platform_data[platform_data["video_id"] == video_id]

        if video_data.empty:
            logger.warning(f"没有找到视频 {video_id} 的收益数据")
            return {"status": "error", "message": "没有找到视频 {video_id} 的收益数据"}

        # 提取收益数据
        revenue = video_data["revenue"].values[0] if "revenue" in video_data.columns else 0
        views = video_data["views"].values[0] if "views" in video_data.columns else 0
        likes = video_data["likes"].values[0] if "likes" in video_data.columns else 0
        comments = video_data["comments"].values[0] if "comments" in video_data.columns else 0
        shares = video_data["shares"].values[0] if "shares" in video_data.columns else 0

        # 计算每千次播放收益 (RPM)
        rpm = (revenue / views * 1000) if views > 0 else 0

        # 计算互动率
        engagement_rate = (likes + comments + shares) / views if views > 0 else 0

        # 与平台平均值比较
        avg_revenue = platform_data["revenue"].mean() if "revenue" in platform_data.columns else 0
        avg_views = platform_data["views"].mean() if "views" in platform_data.columns else 0
        avg_rpm = (
            platform_data["revenue"].sum() / platform_data["views"].sum() * 1000
            if "revenue" in platform_data.columns
            and "views" in platform_data.columns
            and platform_data["views"].sum() > 0
            else 0
        )

        # 计算收益效率
        revenue_efficiency = rpm / avg_rpm if avg_rpm > 0 else 0

        # 收益分析
        revenue_analysis = {
            "revenue": revenue,
            "views": views,
            "rpm": rpm,
            "engagement_rate": engagement_rate,
            "avg_platform_revenue": avg_revenue,
            "avg_platform_views": avg_views,
            "avg_platform_rpm": avg_rpm,
            "revenue_efficiency": revenue_efficiency,
            "revenue_percentile": (
                self._calculate_percentile(platform_data, "revenue", revenue)
                if "revenue" in platform_data.columns
                else None
            ),
            "views_percentile": (
                self._calculate_percentile(platform_data, "views", views) if "views" in platform_data.columns else None
            ),
            "rpm_percentile": (
                self._calculate_percentile(platform_data, "rpm", rpm) if "rpm" in platform_data.columns else None
            ),
        }

        # 收益因素分析
        revenue_factors = self._analyze_revenue_factors(platform, video_data)

        # 收益优化建议
        optimization_suggestions = self._generate_revenue_optimization_suggestions(
            platform, video_data, revenue_analysis, revenue_factors
        )

        return {
            "status": "success",
            "video_id": video_id,
            "platform": platform,
            "revenue_analysis": revenue_analysis,
            "revenue_factors": revenue_factors,
            "optimization_suggestions": optimization_suggestions,
        }

    def _calculate_percentile(self, data: pd.DataFrame, column: str, value: float) -> float:
        """
        计算百分位数

        Args:
            data: 数据
            column: 列名
            value: 值

        Returns:
            百分位数 (0-100)
        """
        if column not in data.columns:
            return None

        # 计算百分位数
        percentile = (data[column] < value).mean() * 100

        return round(percentile, 2)

    def _analyze_revenue_factors(self, platform: str, video_data: pd.DataFrame) -> Dict[str, Any]:
        """
        分析收益因素

        Args:
            platform: 平台名称
            video_data: 视频数据

        Returns:
            收益因素分析结果
        """
        # 获取收益模型
        revenue_model = self.revenue_models.get(platform, {})

        if not revenue_model:
            return {}

        # 提取视频特征
        content_type = video_data["content_type"].values[0] if "content_type" in video_data.columns else None
        duration = video_data["duration"].values[0] if "duration" in video_data.columns else None
        publish_time = video_data["publish_time"].values[0] if "publish_time" in video_data.columns else None

        # 分析内容类型对收益的影响
        content_type_impact = None

        if content_type and "content_type_multipliers" in revenue_model:
            content_multipliers = revenue_model["content_type_multipliers"]

            if content_type in content_multipliers:
                content_type_impact = content_multipliers[content_type]

        # 分析时长对收益的影响
        duration_impact = None

        if duration and "duration_multipliers" in revenue_model:
            duration_multipliers = revenue_model["duration_multipliers"]

            # 将时长映射到最接近的类别
            if duration <= 15:
                duration_category = "15s"
            elif duration <= 30:
                duration_category = "30s"
            elif duration <= 60:
                duration_category = "60s"
            elif duration <= 90:
                duration_category = "90s"
            elif duration <= 180:
                duration_category = "180s"
            elif duration <= 300:
                duration_category = "300s"
            elif duration <= 600:
                duration_category = "600s"
            elif duration <= 900:
                duration_category = "900s"
            else:
                duration_category = "900s+"

            if duration_category in duration_multipliers:
                duration_impact = duration_multipliers[duration_category]
            elif duration_category.replace("s", "s+") in duration_multipliers:
                duration_impact = duration_multipliers[duration_category.replace("s", "s+")]

        # 分析发布时间对收益的影响
        time_impact = None

        if publish_time and "time_factors" in revenue_model:
            time_factors = revenue_model["time_factors"]

            try:
                # 解析发布时间
                publish_datetime = datetime.strptime(publish_time, "%Y-%m-%d %H:%M:%S")

                # 判断是否为周末
                is_weekend = publish_datetime.weekday() >= 5  # 5和6是周六和周日

                # 判断时间段
                hour = publish_datetime.hour

                if 6 <= hour < 12:
                    time_of_day = "morning"
                elif 12 <= hour < 18:
                    time_of_day = "noon"
                elif 18 <= hour < 22:
                    time_of_day = "evening"
                else:
                    time_of_day = "night"

                # 计算时间因素
                day_factor = time_factors["weekend"] if is_weekend else time_factors["weekday"]
                time_factor = time_factors.get(time_of_day, 1.0)

                time_impact = day_factor * time_factor
            except Exception as e:
                logger.error(f"解析发布时间失败: {e}")

        # 构建收益因素分析结果
        return {
            "content_type": {"value": content_type, "impact": content_type_impact},
            "duration": {"value": duration, "impact": duration_impact},
            "publish_time": {"value": publish_time, "impact": time_impact},
        }

    def _generate_revenue_optimization_suggestions(
        self, platform: str, video_data: pd.DataFrame, revenue_analysis: Dict[str, Any], revenue_factors: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        生成收益优化建议

        Args:
            platform: 平台名称
            video_data: 视频数据
            revenue_analysis: 收益分析结果
            revenue_factors: 收益因素分析结果

        Returns:
            收益优化建议列表
        """
        suggestions = []

        # 获取收益模型
        revenue_model = self.revenue_models.get(platform, {})

        if not revenue_model:
            return suggestions

        # 分析RPM
        rpm = revenue_analysis.get("rpm", 0)
        avg_rpm = revenue_analysis.get("avg_platform_rpm", 0)

        if rpm < avg_rpm * 0.8:
            suggestions.append(
                {
                    "type": "rpm",
                    "priority": "high",
                    "suggestion": "视频的每千次播放收益 (RPM) 显著低于平台平均水平，建议优化内容类型和互动率",
                }
            )

        # 分析互动率
        engagement_rate = revenue_analysis.get("engagement_rate", 0)

        if engagement_rate < 0.05:  # 假设5%是一个合理的互动率阈值
            suggestions.append(
                {
                    "type": "engagement",
                    "priority": "high",
                    "suggestion": "视频互动率较低，建议增加互动元素，如提问、号召行动等",
                }
            )

        # 分析内容类型
        content_type_info = revenue_factors.get("content_type", {})
        content_type = content_type_info.get("value")
        content_impact = content_type_info.get("impact")

        if content_type and content_impact and content_impact < 1.0:
            # 找出收益倍率最高的内容类型
            if "content_type_multipliers" in revenue_model:
                content_multipliers = revenue_model["content_type_multipliers"]
                best_content_types = sorted(content_multipliers.items(), key=lambda x: x[1], reverse=True)[:3]

                suggestion_text = (
                    f"当前内容类型 '{content_type}' 的收益倍率较低 ({content_impact})，建议尝试以下高收益内容类型: "
                )
                suggestion_text += ", ".join([f"'{t}' ({m})" for t, m in best_content_types])

                suggestions.append({"type": "content_type", "priority": "medium", "suggestion": suggestion_text})

        # 分析视频时长
        duration_info = revenue_factors.get("duration", {})
        duration = duration_info.get("value")
        duration_impact = duration_info.get("impact")

        if duration and duration_impact and duration_impact < 1.0:
            # 找出收益倍率最高的时长范围
            if "duration_multipliers" in revenue_model:
                duration_multipliers = revenue_model["duration_multipliers"]
                best_durations = sorted(duration_multipliers.items(), key=lambda x: x[1], reverse=True)[:2]

                suggestion_text = (
                    "当前视频时长 ({duration}秒) 的收益倍率较低 ({duration_impact})，建议尝试以下高收益时长范围: "
                )
                suggestion_text += ", ".join([f"{d} ({m})" for d, m in best_durations])

                suggestions.append({"type": "duration", "priority": "medium", "suggestion": suggestion_text})

        # 分析发布时间
        time_info = revenue_factors.get("publish_time", {})
        publish_time = time_info.get("value")
        time_impact = time_info.get("impact")

        if publish_time and time_impact and time_impact < 1.0:
            # 找出收益倍率最高的发布时间
            if "time_factors" in revenue_model:
                time_factors = revenue_model["time_factors"]

                # 计算各时间组合的收益倍率
                time_combinations = []

                for day_type in ["weekday", "weekend"]:
                    for time_of_day in ["morning", "noon", "evening", "night"]:
                        if day_type in time_factors and time_of_day in time_factors:
                            combined_factor = time_factors[day_type] * time_factors[time_of_day]
                            time_combinations.append(("{day_type} {time_of_day}", combined_factor))

                best_times = sorted(time_combinations, key=lambda x: x[1], reverse=True)[:2]

                suggestion_text = "当前发布时间的收益倍率较低 ({time_impact})，建议尝试以下高收益发布时间: "
                suggestion_text += ", ".join([f"{t} ({m})" for t, m in best_times])

                suggestions.append({"type": "publish_time", "priority": "medium", "suggestion": suggestion_text})

        # 如果没有具体建议，添加一个通用建议
        if not suggestions:
            suggestions.append(
                {"type": "general", "priority": "low", "suggestion": "视频收益表现良好，继续保持当前内容策略"}
            )

        return suggestions

    def predict_revenue(self, platform: str, video_features: Dict[str, Any]) -> Dict[str, Any]:
        """
        预测视频收益

        Args:
            platform: 平台名称
            video_features: 视频特征

        Returns:
            收益预测结果
        """
        logger.info("预测 {platform} 平台视频的收益")

        # 检查平台是否支持
        if platform not in self.supported_platforms:
            logger.error(f"不支持的平台: {platform}")
            return {"status": "error", "message": f"不支持的平台: {platform}"}

        # 获取收益模型
        revenue_model = self.revenue_models.get(platform, {})

        if not revenue_model:
            logger.error(f"没有 {platform} 平台的收益模型")
            return {"status": "error", "message": "没有 {platform} 平台的收益模型"}

        # 提取视频特征
        content_type = video_features.get("content_type")
        duration = video_features.get("duration")
        publish_time = video_features.get("publish_time")
        expected_views = video_features.get("expected_views")
        expected_engagement_rate = video_features.get("expected_engagement_rate", 0.05)  # 默认5%
        follower_count = video_features.get("follower_count", 0)

        # 检查必要参数
        if not expected_views:
            logger.error("缺少预期播放量参数")
            return {"status": "error", "message": "缺少预期播放量参数"}

        # 基础收益率
        base_rate = revenue_model.get("base_rate", 0.005)  # 默认每千次播放0.005元

        # 计算内容类型倍率
        content_multiplier = 1.0

        if content_type and "content_type_multipliers" in revenue_model:
            content_multipliers = revenue_model["content_type_multipliers"]
            content_multiplier = content_multipliers.get(content_type, 1.0)

        # 计算时长倍率
        duration_multiplier = 1.0

        if duration and "duration_multipliers" in revenue_model:
            duration_multipliers = revenue_model["duration_multipliers"]

            # 将时长映射到最接近的类别
            if duration <= 15:
                duration_category = "15s"
            elif duration <= 30:
                duration_category = "30s"
            elif duration <= 60:
                duration_category = "60s"
            elif duration <= 90:
                duration_category = "90s"
            elif duration <= 180:
                duration_category = "180s"
            elif duration <= 300:
                duration_category = "300s"
            elif duration <= 600:
                duration_category = "600s"
            elif duration <= 900:
                duration_category = "900s"
            else:
                duration_category = "900s+"

            if duration_category in duration_multipliers:
                duration_multiplier = duration_multipliers[duration_category]
            elif duration_category.replace("s", "s+") in duration_multipliers:
                duration_multiplier = duration_multipliers[duration_category.replace("s", "s+")]

        # 计算时间倍率
        time_multiplier = 1.0

        if publish_time and "time_factors" in revenue_model:
            time_factors = revenue_model["time_factors"]

            try:
                # 解析发布时间
                publish_datetime = datetime.strptime(publish_time, "%Y-%m-%d %H:%M:%S")

                # 判断是否为周末
                is_weekend = publish_datetime.weekday() >= 5  # 5和6是周六和周日

                # 判断时间段
                hour = publish_datetime.hour

                if 6 <= hour < 12:
                    time_of_day = "morning"
                elif 12 <= hour < 18:
                    time_of_day = "noon"
                elif 18 <= hour < 22:
                    time_of_day = "evening"
                else:
                    time_of_day = "night"

                # 计算时间因素
                day_factor = time_factors["weekend"] if is_weekend else time_factors["weekday"]
                time_factor = time_factors.get(time_of_day, 1.0)

                time_multiplier = day_factor * time_factor
            except Exception:
                logger.error("操作失败")
        # 计算互动倍率
        engagement_multiplier = revenue_model.get("engagement_multiplier", 1.0)
        engagement_factor = 1.0 + (expected_engagement_rate - 0.05) * engagement_multiplier

        # 计算粉丝倍率
        follower_multiplier = revenue_model.get("follower_multiplier", 1.0)
        follower_factor = 1.0

        if follower_count > 0:
            # 简单的对数关系：ln(followers) / 10
            follower_factor = 1.0 + min(1.0, np.log(max(1, follower_count)) / 10) * (follower_multiplier - 1.0)

        # 计算总倍率
        total_multiplier = (
            content_multiplier * duration_multiplier * time_multiplier * engagement_factor * follower_factor
        )

        # 计算预期RPM
        expected_rpm = base_rate * total_multiplier

        # 计算预期收益
        expected_revenue = expected_views * expected_rpm / 1000

        # 构建预测结果
        prediction_result = {
            "expected_views": expected_views,
            "expected_rpm": round(expected_rpm, 4),
            "expected_revenue": round(expected_revenue, 2),
            "factors": {
                "base_rate": base_rate,
                "content_multiplier": round(content_multiplier, 2),
                "duration_multiplier": round(duration_multiplier, 2),
                "time_multiplier": round(time_multiplier, 2),
                "engagement_factor": round(engagement_factor, 2),
                "follower_factor": round(follower_factor, 2),
                "total_multiplier": round(total_multiplier, 2),
            },
        }

        return {"status": "success", "platform": platform, "prediction": prediction_result}

    def compare_platform_revenue(self, video_features: Dict[str, Any], platforms: List[str] = None) -> Dict[str, Any]:
        """
        比较不同平台的收益

        Args:
            video_features: 视频特征
            platforms: 平台列表，如果为None则比较所有支持的平台

        Returns:
            平台收益比较结果
        """
        logger.info("比较不同平台的收益")

        # 如果未指定平台，则使用所有支持的平台
        if not platforms:
            platforms = list(self.supported_platforms.keys())

        # 过滤不支持的平台
        platforms = [p for p in platforms if p in self.supported_platforms]

        if not platforms:
            logger.error("没有指定有效的平台")
            return {"status": "error", "message": "没有指定有效的平台"}

        # 比较结果
        comparison_results = {}

        # 对每个平台进行预测
        for platform in platforms:
            prediction_result = self.predict_revenue(platform, video_features)

            if prediction_result.get("status") == "success":
                comparison_results[platform] = prediction_result.get("prediction", {})

        # 如果没有有效的预测结果，返回错误
        if not comparison_results:
            logger.error("没有有效的预测结果")
            return {"status": "error", "message": "没有有效的预测结果"}

        # 按预期收益排序
        sorted_platforms = sorted(
            comparison_results.items(), key=lambda x: x[1].get("expected_revenue", 0), reverse=True
        )

        # 构建比较结果
        comparison_result = {
            "best_platform": sorted_platforms[0][0],
            "best_platform_revenue": sorted_platforms[0][1].get("expected_revenue", 0),
            "platform_ranking": [
                {
                    "platform": platform,
                    "platform_name": self.supported_platforms.get(platform, platform),
                    "expected_revenue": data.get("expected_revenue", 0),
                    "expected_rpm": data.get("expected_rpm", 0),
                    "total_multiplier": data.get("factors", {}).get("total_multiplier", 1.0),
                }
                for platform, data in sorted_platforms
            ],
        }

        return {"status": "success", "video_features": video_features, "comparison": comparison_result}

    def generate_revenue_report(
        self, platform: str, start_date: str = None, end_date: str = None, output_format: str = "json"
    ) -> Dict[str, Any]:
        """
        生成收益报告

        Args:
            platform: 平台名称
            start_date: 开始日期，格式: YYYY-MM-DD
            end_date: 结束日期，格式: YYYY-MM-DD
            output_format: 输出格式，可选值: 'json', 'html', 'pdf'

        Returns:
            收益报告
        """
        logger.info("生成 {platform} 平台的收益报告")

        # 检查平台是否支持
        if platform not in self.supported_platforms:
            logger.error(f"不支持的平台: {platform}")
            return {"status": "error", "message": "不支持的平台: {platform}"}

        # 获取平台数据
        platform_data = self.revenue_data.get(platform, pd.DataFrame())

        if platform_data.empty:
            logger.warning(f"没有 {platform} 平台的收益数据")
            return {"status": "error", "message": "没有 {platform} 平台的收益数据"}

        # 如果指定了日期范围，筛选数据
        if start_date or end_date:
            # 确保有发布时间列
            if "publish_time" not in platform_data.columns:
                logger.warning(f"{platform} 平台的数据没有发布时间列")
                return {"status": "error", "message": "{platform} 平台的数据没有发布时间列"}

            # 筛选数据
            if start_date:
                platform_data = platform_data[platform_data["publish_time"] >= start_date]

            if end_date:
                platform_data = platform_data[platform_data["publish_time"] <= end_date]

        # 如果筛选后没有数据，返回错误
        if platform_data.empty:
            logger.warning(f"指定日期范围内没有 {platform} 平台的收益数据")
            return {"status": "error", "message": "指定日期范围内没有 {platform} 平台的收益数据"}

        # 计算总收益
        total_revenue = platform_data["revenue"].sum() if "revenue" in platform_data.columns else 0

        # 计算总播放量
        total_views = platform_data["views"].sum() if "views" in platform_data.columns else 0

        # 计算平均RPM
        avg_rpm = (total_revenue / total_views * 1000) if total_views > 0 else 0

        # 计算平均互动率
        total_likes = platform_data["likes"].sum() if "likes" in platform_data.columns else 0
        total_comments = platform_data["comments"].sum() if "comments" in platform_data.columns else 0
        total_shares = platform_data["shares"].sum() if "shares" in platform_data.columns else 0

        avg_engagement_rate = (total_likes + total_comments + total_shares) / total_views if total_views > 0 else 0

        # 按内容类型分组
        content_type_revenue = {}

        if "content_type" in platform_data.columns and "revenue" in platform_data.columns:
            for content_type, group in platform_data.groupby("content_type"):
                content_type_revenue[content_type] = {
                    "revenue": group["revenue"].sum(),
                    "views": group["views"].sum() if "views" in group.columns else 0,
                    "video_count": len(group),
                }

        # 按时长分组
        duration_revenue = {}

        if "duration" in platform_data.columns and "revenue" in platform_data.columns:
            # 定义时长范围
            duration_ranges = [
                (0, 15, "0-15s"),
                (15, 30, "15-30s"),
                (30, 60, "30-60s"),
                (60, 120, "1-2min"),
                (120, 300, "2-5min"),
                (300, 600, "5-10min"),
                (600, float("in"), "10min+"),
            ]

            for min_duration, max_duration, label in duration_ranges:
                group = platform_data[
                    (platform_data["duration"] >= min_duration) & (platform_data["duration"] < max_duration)
                ]

                if not group.empty:
                    duration_revenue[label] = {
                        "revenue": group["revenue"].sum(),
                        "views": group["views"].sum() if "views" in group.columns else 0,
                        "video_count": len(group),
                    }

        # 按发布时间分组
        time_revenue = {}

        if "publish_time" in platform_data.columns and "revenue" in platform_data.columns:
            # 解析发布时间
            try:
                platform_data["publish_datetime"] = pd.to_datetime(platform_data["publish_time"])

                # 按星期几分组
                weekday_groups = platform_data.groupby(platform_data["publish_datetime"].dt.weekday)

                weekday_names = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]

                for weekday, group in weekday_groups:
                    time_revenue[weekday_names[weekday]] = {
                        "revenue": group["revenue"].sum(),
                        "views": group["views"].sum() if "views" in group.columns else 0,
                        "video_count": len(group),
                    }

                # 按时间段分组
                hour_groups = platform_data.groupby(platform_data["publish_datetime"].dt.hour)

                time_periods = {
                    "morning": list(range(6, 12)),
                    "noon": list(range(12, 18)),
                    "evening": list(range(18, 22)),
                    "night": list(range(22, 24)) + list(range(0, 6)),
                }

                period_revenue = {period: 0 for period in time_periods}
                period_views = {period: 0 for period in time_periods}
                period_count = {period: 0 for period in time_periods}

                for hour, group in hour_groups:
                    for period, hours in time_periods.items():
                        if hour in hours:
                            period_revenue[period] += group["revenue"].sum()
                            period_views[period] += group["views"].sum() if "views" in group.columns else 0
                            period_count[period] += len(group)

                for period in time_periods:
                    time_revenue[period] = {
                        "revenue": period_revenue[period],
                        "views": period_views[period],
                        "video_count": period_count[period],
                    }
            except Exception as e:
                logger.error(f"解析发布时间失败: {e}")

        # 构建报告数据
        report_data = {
            "platform": platform,
            "platform_name": self.supported_platforms.get(platform, platform),
            "start_date": start_date,
            "end_date": end_date,
            "video_count": len(platform_data),
            "total_revenue": total_revenue,
            "total_views": total_views,
            "avg_rpm": avg_rpm,
            "avg_engagement_rate": avg_engagement_rate,
            "content_type_revenue": content_type_revenue,
            "duration_revenue": duration_revenue,
            "time_revenue": time_revenue,
        }

        # 如果需要生成图表，创建可视化
        if output_format in ["html", "pd"]:
            report_path = self._generate_report_visualization(platform, report_data, output_format)

            if report_path:
                report_data["report_path"] = report_path

        return {"status": "success", "report": report_data}

    def _generate_report_visualization(
        self, platform: str, report_data: Dict[str, Any], output_format: str
    ) -> Optional[str]:
        """
        生成报告可视化

        Args:
            platform: 平台名称
            report_data: 报告数据
            output_format: 输出格式

        Returns:
            报告文件路径
        """
        try:
            # 设置样式
            plt.style.use("seaborn-v0_8-whitegrid")

            # 创建图表
            fig = plt.figure(figsize=(12, 18))

            # 设置标题
            fig.suptitle(f"{report_data['platform_name']} 平台收益报告", fontsize=16)

            # 创建子图
            gs = fig.add_gridspec(6, 2)

            # 1. 内容类型收益分布
            ax1 = fig.add_subplot(gs[0, 0])
            content_type_data = report_data["content_type_revenue"]

            if content_type_data:
                content_types = list(content_type_data.keys())
                revenues = [data["revenue"] for data in content_type_data.values()]

                ax1.bar(content_types, revenues)
                ax1.set_title("内容类型收益分布")
                ax1.set_xlabel("内容类型")
                ax1.set_ylabel("收益 (元)")
                ax1.tick_params(axis="x", rotation=45)
            else:
                ax1.text(0.5, 0.5, "没有内容类型数据", ha="center", va="center")

            # 2. 时长收益分布
            ax2 = fig.add_subplot(gs[0, 1])
            duration_data = report_data["duration_revenue"]

            if duration_data:
                durations = list(duration_data.keys())
                revenues = [data["revenue"] for data in duration_data.values()]

                ax2.bar(durations, revenues)
                ax2.set_title("视频时长收益分布")
                ax2.set_xlabel("视频时长")
                ax2.set_ylabel("收益 (元)")
                ax2.tick_params(axis="x", rotation=45)
            else:
                ax2.text(0.5, 0.5, "没有时长数据", ha="center", va="center")

            # 3. 星期几收益分布
            ax3 = fig.add_subplot(gs[1, 0])
            time_data = report_data["time_revenue"]

            weekday_names = ["周一", "周二", "周三", "周四", "周五", "周六", "周日"]
            weekday_revenues = []

            for day in weekday_names:
                if day in time_data:
                    weekday_revenues.append(time_data[day]["revenue"])
                else:
                    weekday_revenues.append(0)

            if any(weekday_revenues):
                ax3.bar(weekday_names, weekday_revenues)
                ax3.set_title("星期几收益分布")
                ax3.set_xlabel("星期几")
                ax3.set_ylabel("收益 (元)")
            else:
                ax3.text(0.5, 0.5, "没有星期几数据", ha="center", va="center")

            # 4. 时间段收益分布
            ax4 = fig.add_subplot(gs[1, 1])
            period_names = ["morning", "noon", "evening", "night"]
            period_labels = ["早上 (6-12点)", "下午 (12-18点)", "晚上 (18-22点)", "深夜 (22-6点)"]
            period_revenues = []

            for period in period_names:
                if period in time_data:
                    period_revenues.append(time_data[period]["revenue"])
                else:
                    period_revenues.append(0)

            if any(period_revenues):
                ax4.bar(period_labels, period_revenues)
                ax4.set_title("时间段收益分布")
                ax4.set_xlabel("时间段")
                ax4.set_ylabel("收益 (元)")
                ax4.tick_params(axis="x", rotation=45)
            else:
                ax4.text(0.5, 0.5, "没有时间段数据", ha="center", va="center")

            # 5. 内容类型RPM比较
            ax5 = fig.add_subplot(gs[2, 0])

            if content_type_data:
                content_types = list(content_type_data.keys())
                rpms = []

                for data in content_type_data.values():
                    views = data["views"]
                    revenue = data["revenue"]
                    rpm = (revenue / views * 1000) if views > 0 else 0
                    rpms.append(rpm)

                ax5.bar(content_types, rpms)
                ax5.set_title("内容类型RPM比较")
                ax5.set_xlabel("内容类型")
                ax5.set_ylabel("RPM (元/千次播放)")
                ax5.tick_params(axis="x", rotation=45)
            else:
                ax5.text(0.5, 0.5, "没有内容类型数据", ha="center", va="center")

            # 6. 时长RPM比较
            ax6 = fig.add_subplot(gs[2, 1])

            if duration_data:
                durations = list(duration_data.keys())
                rpms = []

                for data in duration_data.values():
                    views = data["views"]
                    revenue = data["revenue"]
                    rpm = (revenue / views * 1000) if views > 0 else 0
                    rpms.append(rpm)

                ax6.bar(durations, rpms)
                ax6.set_title("视频时长RPM比较")
                ax6.set_xlabel("视频时长")
                ax6.set_ylabel("RPM (元/千次播放)")
                ax6.tick_params(axis="x", rotation=45)
            else:
                ax6.text(0.5, 0.5, "没有时长数据", ha="center", va="center")

            # 7. 内容类型视频数量
            ax7 = fig.add_subplot(gs[3, 0])

            if content_type_data:
                content_types = list(content_type_data.keys())
                video_counts = [data["video_count"] for data in content_type_data.values()]

                ax7.bar(content_types, video_counts)
                ax7.set_title("内容类型视频数量")
                ax7.set_xlabel("内容类型")
                ax7.set_ylabel("视频数量")
                ax7.tick_params(axis="x", rotation=45)
            else:
                ax7.text(0.5, 0.5, "没有内容类型数据", ha="center", va="center")

            # 8. 时长视频数量
            ax8 = fig.add_subplot(gs[3, 1])

            if duration_data:
                durations = list(duration_data.keys())
                video_counts = [data["video_count"] for data in duration_data.values()]

                ax8.bar(durations, video_counts)
                ax8.set_title("视频时长分布")
                ax8.set_xlabel("视频时长")
                ax8.set_ylabel("视频数量")
                ax8.tick_params(axis="x", rotation=45)
            else:
                ax8.text(0.5, 0.5, "没有时长数据", ha="center", va="center")

            # 9. 内容类型平均收益
            ax9 = fig.add_subplot(gs[4, 0])

            if content_type_data:
                content_types = list(content_type_data.keys())
                avg_revenues = []

                for data in content_type_data.values():
                    video_count = data["video_count"]
                    revenue = data["revenue"]
                    avg_revenue = revenue / video_count if video_count > 0 else 0
                    avg_revenues.append(avg_revenue)

                ax9.bar(content_types, avg_revenues)
                ax9.set_title("内容类型平均收益")
                ax9.set_xlabel("内容类型")
                ax9.set_ylabel("平均收益 (元/视频)")
                ax9.tick_params(axis="x", rotation=45)
            else:
                ax9.text(0.5, 0.5, "没有内容类型数据", ha="center", va="center")

            # 10. 时长平均收益
            ax10 = fig.add_subplot(gs[4, 1])

            if duration_data:
                durations = list(duration_data.keys())
                avg_revenues = []

                for data in duration_data.values():
                    video_count = data["video_count"]
                    revenue = data["revenue"]
                    avg_revenue = revenue / video_count if video_count > 0 else 0
                    avg_revenues.append(avg_revenue)

                ax10.bar(durations, avg_revenues)
                ax10.set_title("视频时长平均收益")
                ax10.set_xlabel("视频时长")
                ax10.set_ylabel("平均收益 (元/视频)")
                ax10.tick_params(axis="x", rotation=45)
            else:
                ax10.text(0.5, 0.5, "没有时长数据", ha="center", va="center")

            # 11. 收益概览
            ax11 = fig.add_subplot(gs[5, :])

            overview_text = """
            收益概览:
            - 总收益: {report_data['total_revenue']:.2f} 元
            - 总播放量: {report_data['total_views']} 次
            - 平均RPM: {report_data['avg_rpm']:.4f} 元/千次播放
            - 平均互动率: {report_data['avg_engagement_rate']*100:.2f}%
            - 视频数量: {report_data['video_count']} 个
            """

            ax11.text(0.5, 0.5, overview_text, ha="center", va="center", fontsize=12)
            ax11.axis("o")

            # 调整布局
            plt.tight_layout(rect=[0, 0, 1, 0.97])

            # 保存图表
            datetime.now().strftime("%Y%m%d%H%M%S")
            report_filename = "{platform}_revenue_report_{timestamp}.{output_format}"
            report_path = os.path.join(self.output_dir, report_filename)

            plt.savefig(report_path)
            plt.close(fig)

            logger.info("已生成报告: {report_path}")

            return report_path
        except Exception:
            logger.error("操作失败")
            return None
