# backend.agent.knowledge_base.media_feature_store

import json
import os
from typing import Any
from typing import Dict
from typing import List
from typing import Optional

# 假设媒体特征存储在 memory_manager 管理的路径下
DEFAULT_KB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..", "..", "data", "kb")
MEDIA_FEATURES_FILE_NAME = "media_features.json"

class MediaFeatureStore:
    """存储和管理媒体素材的分析结果（特征）"""

    def __init__(self, knowledge_base_path: str = DEFAULT_KB_PATH):
        """
        初始化 MediaFeatureStore。

        Args:
            knowledge_base_path (str): 知识库数据存储的根路径。
        """
        self.features_file_path = os.path.join(knowledge_base_path, MEDIA_FEATURES_FILE_NAME)
        self._ensure_kb_directory(knowledge_base_path)
        # 数据结构: {media_id: {feature_name: value, ...}, ...}
        self.media_features: Dict[str, Dict[str, Any]] = self._load_features()

    def _ensure_kb_directory(self, kb_path: str):
        """确保知识库目录存在"""
        if not os.path.exists(kb_path):
            os.makedirs(kb_path)
            print("知识库目录已创建: {kb_path}")

    def _load_features(self) -> Dict[str, Dict[str, Any]]:
        """
        从JSON文件加载媒体特征数据。

        Returns:
            Dict[str, Dict[str, Any]]: 加载的媒体特征字典，键为媒体ID，值为特征内容。
                                      如果文件不存在或解析失败则返回空字典。
        """
        if os.path.exists(self.features_file_path):
            try:
                with open(self.features_file_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            except json.JSONDecodeError:
                print("错误: 无法解析媒体特征文件 {self.features_file_path}。将使用空特征集。")
            except Exception as e:
                print(f"加载媒体特征文件 {self.features_file_path} 时出错: {e}")
        return {}

    def _save_features(self):
        """将当前媒体特征保存到JSON文件"""
        try:
            with open(self.features_file_path, "w", encoding="utf-8") as f:
                json.dump(self.media_features, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print("保存媒体特征文件 {self.features_file_path} 时出错: {e}")

    def add_or_update_media_features(self, media_id: str, features_data: Dict[str, Any]) -> bool:
        """
        添加或更新一个媒体素材的特征信息。
        如果媒体ID已存在，则合并新特征（新特征会覆盖同名旧特征）。

        Args:
            media_id (str): 媒体素材的唯一标识符 (例如文件路径的哈希值或唯一文件名)。
            features_data (Dict[str, Any]): 素材的特征数据
                                         (例如: {'duration': 120.5, 'scenes': [...], 'dominant_colors': [...]})。

        Returns:
            bool: 操作成功返回True。
        """
        if media_id in self.media_features:
            self.media_features[media_id].update(features_data)
            action = "更新"
        else:
            self.media_features[media_id] = features_data
            action = "添加"

        self._save_features()
        print(f"媒体 '{media_id}' 的特征已{action}。")
        return True

    def get_media_features(self, media_id: str) -> Optional[Dict[str, Any]]:
        """
        根据媒体ID获取其所有特征。

        Args:
            media_id (str): 媒体素材的唯一标识符。

        Returns:
            Optional[Dict[str, Any]]: 媒体特征数据字典，如果未找到则返回None。
        """
        return self.media_features.get(media_id)

    def get_specific_feature(self, media_id: str, feature_name: str, default: Optional[Any] = None) -> Optional[Any]:
        """
        获取媒体素材的特定特征值。

        Args:
            media_id (str): 媒体素材的唯一标识符。
            feature_name (str): 要获取的特征名称 (e.g., 'duration', 'avg_motion_level')。
            default (Optional[Any], optional): 如果特征未找到或媒体ID不存在，返回的默认值。Defaults to None.

        Returns:
            Optional[Any]: 特征的值。
        """
        media_data = self.get_media_features(media_id)
        if media_data:
            return media_data.get(feature_name, default)
        return default

    def get_all_media_ids(self) -> List[str]:
        """获取所有已存储特征的媒体ID列表"""
        return list(self.media_features.keys())

    def delete_media_features(self, media_id: str) -> bool:
        """
        删除一个媒体素材的所有特征信息。

        Args:
            media_id (str): 要删除特征的媒体ID。

        Returns:
            bool: 如果特征成功删除，则返回True，否则返回False (例如媒体ID不存在)。
        """
        if media_id in self.media_features:
            del self.media_features[media_id]
            self._save_features()
            print(f"媒体 '{media_id}' 的特征已删除。")
            return True
        print(f"媒体特征删除失败: 媒体ID '{media_id}' 未找到。")
        return False

    # 未来可以添加更高级的查询功能，例如基于特征值的相似性搜索 (可能需要集成向量数据库)
    # def find_similar_media(self, target_features: Dict[str, Any], top_n: int = 5) -> List[str]:
    #     """查找与目标特征相似的媒体 (占位符)"""
    #     print("查找与 {target_features} 相似的媒体 (此功能待实现)")
    #     return []

if __name__ == "__main__":
    # 测试 MediaFeatureStore
    test_kb_path = os.path.join(os.getcwd(), "test_kb_data_media_features")
    if not os.path.exists(test_kb_path):
        os.makedirs(test_kb_path)

    feature_store = MediaFeatureStore(knowledge_base_path=test_kb_path)

    # 添加媒体特征
    feature_store.add_or_update_media_features(
        "video001.mp4",
        {
            "duration": 185.3,
            "resolution": "1920x1080",
            "fps": 29.97,
            "scenes": [
                {"start": 0, "end": 10.5, "tags": ["intro"]},
                {"start": 10.5, "end": 60.1, "tags": ["dialogue"]},
            ],
        },
    )
    feature_store.add_or_update_media_features(
        "audio_bgm002.mp3", {"duration": 240.0, "bpm": 120, "genre": "electronic", "mood": "uplifting"}
    )

    print("所有媒体ID:", feature_store.get_all_media_ids())
    print("video001.mp4的特征:", feature_store.get_media_features("video001.mp4"))

    # 获取特定特征
    duration_video1 = feature_store.get_specific_feature("video001.mp4", "duration")
    print("video001.mp4的时长: {duration_video1}")
    mood_audio2 = feature_store.get_specific_feature("audio_bgm002.mp3", "mood", default="unknown")
    print("audio_bgm002.mp3的氛围: {mood_audio2}")

    # 更新特征
    feature_store.add_or_update_media_features("video001.mp4", {"avg_motion_level": 0.75, "has_speech": True})
    print("更新后的video001.mp4特征:", feature_store.get_media_features("video001.mp4"))

    # 删除特征
    feature_store.delete_media_features("audio_bgm002.mp3")
    print("删除audio_bgm002.mp3后所有媒体ID:", feature_store.get_all_media_ids())

    # 清理测试文件
    # features_file = os.path.join(test_kb_path, MEDIA_FEATURES_FILE_NAME)
    # if os.path.exists(features_file):
    #     os.remove(features_file)
    # if os.path.exists(test_kb_path):
    #     os.rmdir(test_kb_path)
    # print("测试文件和目录已清理 (如果存在且为空)")
