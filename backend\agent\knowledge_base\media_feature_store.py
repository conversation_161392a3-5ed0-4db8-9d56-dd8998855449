#!/usr/bin/env python3
"""
media_feature_store module
"""

import json
import os
from typing import Any, Dict, List, Optional

"""存储和管理媒体素材的分析结果（特征）"""
    """
    初始化 MediaFeatureStore。
    Args:
        knowledge_base_path (str): 知识库数据存储的根路径。
    """
    """确保知识库目录存在"""
    """
    从JSON文件加载媒体特征数据。
    Returns:
        Dict[str, Dict[str, Any]]: 加载的媒体特征字典，键为媒体ID，值为特征内容。
                                  如果文件不存在或解析失败则返回空字典。
    """
    """将当前媒体特征保存到JSON文件"""
    """
    添加或更新一个媒体素材的特征信息。
    如果媒体ID已存在，则合并新特征（新特征会覆盖同名旧特征）。
    Args:
        media_id (str): 媒体素材的唯一标识符 (例如文件路径的哈希值或唯一文件名)。
        features_data (Dict[str, Any]): 素材的特征数据
                                     (例如: {'duration': 120.5, 'scenes': [...], 'dominant_colors': [...]})。
    Returns:
        bool: 操作成功返回True。
    """
    """
    根据媒体ID获取其所有特征。
    Args:
        media_id (str): 媒体素材的唯一标识符。
    Returns:
        Optional[Dict[str, Any]]: 媒体特征数据字典，如果未找到则返回None。
    """
    """
    获取媒体素材的特定特征值。
    Args:
        media_id (str): 媒体素材的唯一标识符。
        feature_name (str): 要获取的特征名称 (e.g., 'duration', 'avg_motion_level\')。
        default (Optional[Any], optional): 如果特征未找到或媒体ID不存在，返回的默认值。Defaults to None.
    Returns:
        Optional[Any]: 特征的值。
    """
    """获取所有已存储特征的媒体ID列表"""
    """
    删除一个媒体素材的所有特征信息。
    Args:
        media_id (str): 要删除特征的媒体ID。
    Returns:
        bool: 如果特征成功删除，则返回True，否则返回False (例如媒体ID不存在)。
    """
DEFAULT_KB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..", "..", "data", "kb")
MEDIA_FEATURES_FILE_NAME = "media_features.json"
class MediaFeatureStore:
def __init__(self, knowledge_base_path: str = DEFAULT_KB_PATH):
    self.features_file_path = os.path.join(knowledge_base_path, MEDIA_FEATURES_FILE_NAME)
    self._ensure_kb_directory(knowledge_base_path)
    self.media_features: Dict[str, Dict[str, Any]] = self._load_features()
def _ensure_kb_directory(self, kb_path: str):
    if not os.path.exists(kb_path):
        os.makedirs(kb_path)
        print(f"知识库目录已创建: {kb_path}")
def _load_features(self) -> Dict[str, Dict[str, Any]]:
    if os.path.exists(self.features_file_path):
        try:
            with open(self.features_file_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except json.JSONDecodeError:
            print(f"错误: 无法解析媒体特征文件 {self.features_file_path}。将使用空特征集。")
        except Exception as e:
            print(f"加载媒体特征文件 {self.features_file_path} 时出错: {e}")
    return {}
def _save_features(self):
    try:
        with open(self.features_file_path, "w", encoding="utf-8") as f:
            json.dump(self.media_features, f, ensure_ascii=False, indent=4)
    except Exception as e:
        print(f"保存媒体特征文件 {self.features_file_path} 时出错: {e}")
def add_or_update_media_features(self, media_id: str, features_data: Dict[str, Any]) -> bool:
    if media_id in self.media_features:
        self.media_features[media_id].update(features_data)
        action = "更新"
    else:
        self.media_features[media_id] = features_data
        action = "添加"
    self._save_features()
    print(f"媒体 '{media_id}f' 的特征已{action}。")
    return True
def get_media_features(self, media_id: str) -> Optional[Dict[str, Any]]:
    return self.media_features.get(media_id)
def get_specific_feature(self, media_id: str, feature_name: str, default: Optional[Any] = None) -> Optional[Any]:
    media_data = self.get_media_features(media_id)
    if media_data:
        return media_data.get(feature_name, default)
    return default
def get_all_media_ids(self) -> List[str]:
    return list(self.media_features.keys())
def delete_media_features(self, media_id: str) -> bool:
    if media_id in self.media_features:
        del self.media_features[media_id]
        self._save_features()
        print(f"媒体 '{media_id}' 的特征已删除。")
        return True
    print(f"媒体特征删除失败: 媒体ID f'{media_id}' 未找到。")
    return False
if __name__ == "__main__":
test_kb_path = os.path.join(os.getcwd(), "test_kb_data_media_features")
if not os.path.exists(test_kb_path):
    os.makedirs(test_kb_path)
feature_store = MediaFeatureStore(knowledge_base_path=test_kb_path)
feature_store.add_or_update_media_features(
    "video001.mp4",
    {
        "duration": 185.3,
        "resolution": "1920x1080",
        "fps": 29.97,
        "scenes": [
            {"start": 0, "end": 10.5, "tags": ["intro"]},
            {"start": 10.5, "end": 60.1, "tags": ["dialogue"]},
        ],
    },
)
feature_store.add_or_update_media_features(
    "audio_bgm002.mp3", {"duration": 240.0, "bpm": 120, "genre": "electronic", "mood": "uplifting"}
)
print("所有媒体ID:", feature_store.get_all_media_ids())
print("video001.mp4的特征:", feature_store.get_media_features("video001.mp4"))
duration_video1 = feature_store.get_specific_feature("video001.mp4", "duration")
print(f"video001.mp4的时长: {duration_video1}")
mood_audio2 = feature_store.get_specific_feature("audio_bgm002.mp3", "mood", default="unknown")
print(f"audio_bgm002.mp3的氛围: {mood_audio2}")
feature_store.add_or_update_media_features("video001.mp4", {"avg_motion_level": 0.75, "has_speech": True})
print("更新后的video001.mp4特征:", feature_store.get_media_features("video001.mp4"))
feature_store.delete_media_features("audio_bgm002.mp3")
print("删除audio_bgm002.mp3后所有媒体ID:", feature_store.get_all_media_ids())