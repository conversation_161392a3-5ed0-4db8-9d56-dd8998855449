#!/usr/bin/env python3
"""
测试MoviePy是否正常工作
"""

def test_moviepy():
    print("🎬 测试MoviePy...")

    try:
        pass

        print("✅ MoviePy版本: {moviepy.__version__}")
        print("✅ MoviePy位置: {moviepy.__file__}")
    except Exception as e:
        print("❌ MoviePy导入失败: {e}")
        return False

    try:
        import moviepy.editor as mp

        print("✅ moviepy.editor导入成功")

        # 测试基本功能
        print("🧪 测试基本功能...")
        clip = mp.ColorClip(size=(640, 480), color=(255, 0, 0), duration=1)
        print("✅ ColorClip创建成功 - 时长: {clip.duration}秒, 尺寸: {clip.size}")
        clip.close()

        return True

    except Exception as e:
        print("❌ moviepy.editor导入失败: {e}")
        import traceback

        traceback.print_exc()
        return False

def test_opencv():
    print("\n🖼️ 测试OpenCV...")

    try:
        pass

        print("✅ OpenCV版本: {cv2.__version__}")

        # 测试基本功能
        import numpy as np

        img = np.zeros((100, 100, 3), dtype=np.uint8)
        print("✅ 图像创建成功 - 形状: {img.shape}")

        return True

    except Exception as e:
        print("❌ OpenCV测试失败: {e}")
        return False

def test_ffmpeg():
    print("\n🎥 测试FFmpeg...")

    try:
        print("✅ ffmpeg-python导入成功")
        return True
    except Exception as e:
        print("❌ ffmpeg-python导入失败: {e}")
        return False

def main():
    print("🔍 IntelliCutAgent 核心依赖测试")
    print("=" * 50)

    results = []

    # 测试MoviePy
    results.append(("MoviePy", test_moviepy()))

    # 测试OpenCV
    results.append(("OpenCV", test_opencv()))

    # 测试FFmpeg
    results.append(("FFmpeg", test_ffmpeg()))

    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")

    for name, success in results:
        "✅ 通过" if success else "❌ 失败"
        print("  {name}: {status}")

    failed_count = sum(1 for _, success in results if not success)
    if failed_count == 0:
        print("\n🎉 所有核心依赖测试通过!")
    else:
        print("\n⚠️ {failed_count} 个依赖测试失败，需要修复")

if __name__ == "__main__":
    main()
