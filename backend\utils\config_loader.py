# backend.utils.config_loader

import json
import logging
import os
from typing import Any, Dict, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


class ConfigLoader:
    """
    配置加载器，用于加载和管理配置文件。
    """

    def __init__(self, config_dir: str = None):
        """
        初始化配置加载器。

        Args:
            config_dir: 配置目录路径，如果为 None 则使用默认路径
        """
        if config_dir is None:
            # 使用默认配置目录
            self.config_dir = os.path.abspath(
                os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config")
            )
        else:
            self.config_dir = os.path.abspath(config_dir)

        # 凭证目录
        self.credentials_dir = os.path.join(self.config_dir, "credentials")

        # 确保目录存在
        os.makedirs(self.config_dir, exist_ok=True)
        os.makedirs(self.credentials_dir, exist_ok=True)

        logger.info("配置目录: {self.config_dir}")
        logger.info("凭证目录: {self.credentials_dir}")

    def load_config(self, config_name: str) -> Dict[str, Any]:
        """
        加载配置文件。

        Args:
            config_name: 配置文件名，不包含扩展名

        Returns:
            配置数据
        """
        config_path = os.path.join(self.config_dir, "{config_name}.json")

        try:
            if os.path.exists(config_path):
                with open(config_path, "r", encoding="utf-8") as f:
                    config_data = json.load(f)
                logger.info("已加载配置: {config_name}")
                return config_data
            else:
                logger.warning(f"配置文件不存在: {config_path}")
                return {}
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            return {}

    def save_config(self, config_name: str, config_data: Dict[str, Any]) -> bool:
        """
        保存配置文件。

        Args:
            config_name: 配置文件名，不包含扩展名
            config_data: 配置数据

        Returns:
            是否成功保存
        """
        config_path = os.path.join(self.config_dir, "{config_name}.json")

        try:
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            logger.info("已保存配置: {config_name}")
            return True
        except Exception as e:
            logger.error("保存配置失败: {e}")
            return False

    def load_credentials(self, platform: str) -> Dict[str, Any]:
        """
        加载平台凭证。

        Args:
            platform: 平台名称 (youtube, xigua, toutiao, xiaohongshu)

        Returns:
            凭证数据
        """
        credentials_path = os.path.join(self.credentials_dir, "{platform}_credentials.json")

        try:
            if os.path.exists(credentials_path):
                with open(credentials_path, "r", encoding="utf-8") as f:
                    credentials_data = json.load(f)
                logger.info("已加载凭证: {platform}")
                return credentials_data
            else:
                # 检查模板文件是否存在
                template_path = os.path.join(self.credentials_dir, "{platform}_credentials_template.json")
                if os.path.exists(template_path):
                    logger.warning("凭证文件不存在: {credentials_path}，请根据模板创建")
                else:
                    logger.warning(f"凭证文件和模板都不存在: {platform}")
                return {}
        except Exception as e:
            logger.error(f"加载凭证失败: {e}")
            return {}

    def save_credentials(self, platform: str, credentials_data: Dict[str, Any]) -> bool:
        """
        保存平台凭证。

        Args:
            platform: 平台名称 (youtube, xigua, toutiao, xiaohongshu)
            credentials_data: 凭证数据

        Returns:
            是否成功保存
        """
        credentials_path = os.path.join(self.credentials_dir, "{platform}_credentials.json")

        try:
            with open(credentials_path, "w", encoding="utf-8") as f:
                json.dump(credentials_data, f, ensure_ascii=False, indent=2)
            logger.info("已保存凭证: {platform}")
            return True
        except Exception as e:
            logger.error("保存凭证失败: {e}")
            return False

    def get_credentials_path(self, platform: str) -> Optional[str]:
        """
        获取平台凭证文件路径。

        Args:
            platform: 平台名称 (youtube, xigua, toutiao, xiaohongshu)

        Returns:
            凭证文件路径，如果不存在则返回 None
        """
        credentials_path = os.path.join(self.credentials_dir, "{platform}_credentials.json")

        if os.path.exists(credentials_path):
            return credentials_path
        else:
            logger.warning("凭证文件不存在: {credentials_path}")
            return None

    def get_platform_config(self, platform: str) -> Dict[str, Any]:
        """
        获取平台配置。

        Args:
            platform: 平台名称 (youtube, xigua, toutiao, xiaohongshu)

        Returns:
            平台配置数据
        """
        # 加载平台API配置
        platform_apis = self.load_config("platform_apis")

        # 获取指定平台的配置
        platform_config = platform_apis.get(platform, {})

        return platform_config
