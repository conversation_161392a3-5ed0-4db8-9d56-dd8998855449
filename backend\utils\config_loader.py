#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置加载器
用于加载和管理配置文件
"""

import json
import logging
import os
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)


class ConfigLoader:
    """
    配置加载器类
    
    功能：
    1. 加载配置文件
    2. 保存配置文件
    3. 管理凭证文件
    4. 提供默认配置
    """

    def __init__(self, config_dir: Optional[str] = None):
        """
        初始化配置加载器
        
        Args:
            config_dir: 配置目录路径，如果为 None 则使用默认路径
        """
        if config_dir is None:
            self.config_dir = os.path.abspath()
                os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config")
            )
        else:
            self.config_dir = os.path.abspath(config_dir)
        
        self.credentials_dir = os.path.join(self.config_dir, "credentials")
        
        # 确保目录存在
        os.makedirs(self.config_dir, exist_ok=True)
        os.makedirs(self.credentials_dir, exist_ok=True)
        
        logger.info(f"配置目录: {self.config_dir}")
        logger.info(f"凭证目录: {self.credentials_dir}")

    def load_config(self, config_name: str) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_name: 配置文件名（不包含扩展名）
            
        Returns:
            配置数据
        """
        config_path = os.path.join(self.config_dir, f"{config_name}.json")
        
        try:
            if os.path.exists(config_path):
                with open(config_path, "r", encoding="utf-8") as f:
                    config_data = json.load(f)
                    logger.info(f"成功加载配置文件: {config_name}")
                    return config_data
            else:
                logger.warning(f"配置文件不存在: {config_path}")
                return self._get_default_config(config_name)
        except Exception as e:
            logger.error(f"加载配置文件失败: {config_path}, 错误: {e}")
            return self._get_default_config(config_name)

    def save_config(self, config_name: str, config_data: Dict[str, Any]) -> bool:
        """
        保存配置文件
        
        Args:
            config_name: 配置文件名（不包含扩展名）
            config_data: 配置数据
            
        Returns:
            是否保存成功
        """
        config_path = os.path.join(self.config_dir, f"{config_name}.json")
        
        try:
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            logger.info(f"成功保存配置文件: {config_name}")
            return True
        except Exception as e:
            logger.error(f"保存配置文件失败: {config_path}, 错误: {e}")
            return False

    def load_credentials(self, platform: str) -> Dict[str, Any]:
        """
        加载平台凭证
        
        Args:
            platform: 平台名称
            
        Returns:
            凭证数据
        """
        credentials_path = os.path.join(self.credentials_dir, f"{platform}_credentials.json")
        
        try:
            if os.path.exists(credentials_path):
                with open(credentials_path, "r", encoding="utf-8") as f:
                    credentials_data = json.load(f)
                    logger.info(f"成功加载 {platform} 平台凭证")
                    return credentials_data
            else:
                logger.warning(f"{platform} 平台凭证文件不存在: {credentials_path}")
                return {}
        except Exception as e:
            logger.error(f"加载 {platform} 平台凭证失败: {credentials_path}, 错误: {e}")
            return {}

    def save_credentials(self, platform: str, credentials_data: Dict[str, Any]) -> bool:
        """
        保存平台凭证
        
        Args:
            platform: 平台名称
            credentials_data: 凭证数据
            
        Returns:
            是否保存成功
        """
        credentials_path = os.path.join(self.credentials_dir, f"{platform}_credentials.json")
        
        try:
            with open(credentials_path, "w", encoding="utf-8") as f:
                json.dump(credentials_data, f, ensure_ascii=False, indent=2)
            logger.info(f"成功保存 {platform} 平台凭证")
            return True
        except Exception as e:
            logger.error(f"保存 {platform} 平台凭证失败: {credentials_path}, 错误: {e}")
            return False

    def get_platform_config(self, platform: str) -> Dict[str, Any]:
        """
        获取平台配置
        
        Args:
            platform: 平台名称
            
        Returns:
            平台配置数据
        """
        platform_config = self.load_config(f"platform_{platform}")
        
        if not platform_config:
            platform_config = self._get_default_platform_config(platform)
            self.save_config(f"platform_{platform}", platform_config)
        
        return platform_config

    def _get_default_config(self, config_name: str) -> Dict[str, Any]:
        """
        获取默认配置
        
        Args:
            config_name: 配置名称
            
        Returns:
            默认配置数据
        """
        default_configs = {}
            "user_config": {}
                "preferred_platforms": ["douyin", "bilibili"],
                "default_edit_rules": {}
                    "duration": "30s",
                    "style": "standard",
                    "transitions": ["fade"],
                    "effects": []
                },
                "default_metadata": {}
                    "title": "AI生成视频",
                    "description": "由IntelliCutAgent自动生成",
                    "tags": ["AI", "自动生成"]
                },
                "output_settings": {}
                    "video_quality": "high",
                    "audio_quality": "high",
                    "format": "mp4"
                }
            },
            "system_config": {}
                "max_concurrent_tasks": 3,
                "temp_dir_cleanup": True,
                "log_level": "INFO",
                "cache_enabled": True,
                "cache_size_mb": 1024
            },
            "editor_config": {}
                "default_resolution": "1920x1080",
                "default_fps": 30,
                "default_bitrate": "5000k",
                "enable_gpu_acceleration": True,
                "temp_file_cleanup": True
            }
        }
        
        return default_configs.get(config_name, {})

    def _get_default_platform_config(self, platform: str) -> Dict[str, Any]:
        """
        获取默认平台配置
        
        Args:
            platform: 平台名称
            
        Returns:
            默认平台配置
        """
        default_platform_configs = {}
            "douyin": {}
                "name": "抖音",
                "video_specs": {}
                    "max_duration": 60,
                    "aspect_ratio": "9:16",
                    "resolution": "1080x1920",
                    "max_file_size_mb": 100
                },
                "upload_settings": {}
                    "auto_publish": False,
                    "enable_comments": True,
                    "enable_duet": True,
                    "enable_download": False
                }
            },
            "bilibili": {}
                "name": "哔哩哔哩",
                "video_specs": {}
                    "max_duration": 3600,
                    "aspect_ratio": "16:9",
                    "resolution": "1920x1080",
                    "max_file_size_mb": 8192
                },
                "upload_settings": {}
                    "auto_publish": False,
                    "enable_comments": True,
                    "enable_danmaku": True,
                    "category": "生活"
                }
            },
            "youtube": {}
                "name": "YouTube",
                "video_specs": {}
                    "max_duration": 43200,
                    "aspect_ratio": "16:9",
                    "resolution": "1920x1080",
                    "max_file_size_mb": 128000
                },
                "upload_settings": {}
                    "privacy": "private",
                    "enable_comments": True,
                    "enable_ratings": True,
                    "category": "22"
                }
            }
        }
        
        return default_platform_configs.get(platform, {}
            "name": platform.title(),
            "video_specs": {}
                "max_duration": 300,
                "aspect_ratio": "16:9",
                "resolution": "1920x1080",
                "max_file_size_mb": 100
            },
            "upload_settings": {}
                "auto_publish": False,
                "enable_comments": True
            }
        })

    def list_configs(self) -> list[str]:
        """
        列出所有配置文件
        
        Returns:
            配置文件名列表
        """
        try:
            config_files = []
            for filename in os.listdir(self.config_dir):
                if filename.endswith(".json"):
                    config_name = filename[:-5]  # 移除 .json 扩展名
                    config_files.append(config_name)
            return config_files
        except Exception as e:
            logger.error(f"列出配置文件失败: {e}")
            return []

    def list_credentials(self) -> list[str]:
        """
        列出所有凭证文件
        
        Returns:
            平台名列表
        """
        try:
            platforms = []
            for filename in os.listdir(self.credentials_dir):
                if filename.endswith("_credentials.json"):
                    platform = filename.replace("_credentials.json", "")
                    platforms.append(platform)
            return platforms
        except Exception as e:
            logger.error(f"列出凭证文件失败: {e}")
            return []

    def delete_config(self, config_name: str) -> bool:
        """
        删除配置文件
        
        Args:
            config_name: 配置文件名
            
        Returns:
            是否删除成功
        """
        config_path = os.path.join(self.config_dir, f"{config_name}.json")
        
        try:
            if os.path.exists(config_path):
                os.remove(config_path)
                logger.info(f"成功删除配置文件: {config_name}")
                return True
            else:
                logger.warning(f"配置文件不存在: {config_name}")
                return False
        except Exception as e:
            logger.error(f"删除配置文件失败: {config_name}, 错误: {e}")
            return False

    def delete_credentials(self, platform: str) -> bool:
        """
        删除平台凭证
        
        Args:
            platform: 平台名称
            
        Returns:
            是否删除成功
        """
        credentials_path = os.path.join(self.credentials_dir, f"{platform}_credentials.json")
        
        try:
            if os.path.exists(credentials_path):
                os.remove(credentials_path)
                logger.info(f"成功删除 {platform} 平台凭证")
                return True
            else:
                logger.warning(f"{platform} 平台凭证不存在")
                return False
        except Exception as e:
            logger.error(f"删除 {platform} 平台凭证失败: {e}")
            return False


# 演示函数
    def main():
        """演示配置加载器功能"""
        loader = ConfigLoader()
    
        print("=== 配置加载器演示 ===")
    
    # 加载用户配置
        user_config = loader.load_config("user_config")
        print("用户配置:", json.dumps(user_config, ensure_ascii=False, indent=2))
    
    # 获取平台配置
        douyin_config = loader.get_platform_config("douyin")
        print("抖音平台配置:", json.dumps(douyin_config, ensure_ascii=False, indent=2))
    
    # 列出配置文件
        configs = loader.list_configs()
        print("配置文件列表:", configs)
    
    # 列出凭证文件
        credentials = loader.list_credentials()
        print("凭证文件列表:", credentials)


        if __name__ == "__main__":
        main()
