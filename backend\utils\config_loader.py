#!/usr/bin/env python3
"""
config_loader module
"""

import json
import logging
import os
from typing import Any, Dict, Optional

"""
配置加载器，用于加载和管理配置文件。
"""
    """
    初始化配置加载器。
    Args:
        config_dir: 配置目录路径，如果为 None 则使用默认路径
    """
    """
    加载配置文件。
    Args:
        config_name: 配置文件名，不包含扩展名
    Returns:
        配置数据
    """
    """
    保存配置文件。
    Args:
        config_name: 配置文件名，不包含扩展名
        config_data: 配置数据
    Returns:
        是否成功保存
    """
    """
    加载平台凭证。
    Args:
        platform: 平台名称 (youtube, xigua, toutiao, xiaohongshu)
    Returns:
        凭证数据
    """
    """
    保存平台凭证。
    Args:
        platform: 平台名称 (youtube, xigua, toutiao, xiaohongshu)
        credentials_data: 凭证数据
    Returns:
        是否成功保存
    """
    """
    获取平台凭证文件路径。
    Args:
        platform: 平台名称 (youtube, xigua, toutiao, xia<PERSON><PERSON>shu)
    Returns:
        凭证文件路径，如果不存在则返回 None
    """
    """
    获取平台配置。
    Args:
        platform: 平台名称 (youtube, xigua, toutiao, xiaohongshu)
    Returns:
        平台配置数据
    """
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)
class ConfigLoader:
def __init__(self, config_dir: Optional[str] = None):
    if config_dir is None:
        self.config_dir = os.path.abspath(
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config")
        )
    else:
        self.config_dir = os.path.abspath(config_dir)
    self.credentials_dir = os.path.join(self.config_dir, "credentials")
    os.makedirs(self.config_dir, exist_ok=True)
    os.makedirs(self.credentials_dir, exist_ok=True)
    logger.info(f"配置目录: {self.config_dir}")
    logger.info(f"凭证目录: {self.credentials_dir}")
def load_config(self, config_name: str) -> Dict[str, Any]:
    config_path = os.path.join(self.config_dir, f"{config_name}.json")
    try:
        if os.path.exists(config_path):
            with open(config_path, "r", encoding="utf-8") as f:
                config_data = json.load(f)
            logger.info(f"已加载配置: {config_name}")
            return config_data
        else:
            logger.warning(f"配置文件不存在: {config_path}")
            return {}
    except Exception as e:
        logger.error(f"加载配置失败: {e}")
        return {}
def save_config(self, config_name: str, config_data: Dict[str, Any]) -> bool:
    config_path = os.path.join(self.config_dir, f"{config_name}.json")
    try:
        with open(config_path, "w", encoding="utf-8") as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        logger.info(f"已保存配置: {config_name}")
        return True
    except Exception:
        logger.error("操作失败")
        return False
def load_credentials(self, platform: str) -> Dict[str, Any]:
    credentials_path = os.path.join(self.credentials_dir, f"{platform}_credentials.json")
    try:
        if os.path.exists(credentials_path):
            with open(credentials_path, "r", encoding="utf-8") as f:
                credentials_data = json.load(f)
            logger.info(f"已加载凭证: {platform}")
            return credentials_data
        else:
            template_path = os.path.join(self.credentials_dir, f"{platform}_credentials_template.json")
            if os.path.exists(template_path):
                logger.warning(f"凭证文件不存在: {credentials_path}，请根据模板创建")
            else:
                logger.warning(f"凭证文件和模板都不存在: {platform}")
            return {}
    except Exception as e:
        logger.error(f"加载凭证失败: {e}")
        return {}
def save_credentials(self, platform: str, credentials_data: Dict[str, Any]) -> bool:
    credentials_path = os.path.join(self.credentials_dir, f"{platform}_credentials.json")
    try:
        with open(credentials_path, "w", encoding="utf-8") as f:
            json.dump(credentials_data, f, ensure_ascii=False, indent=2)
        logger.info(f"已保存凭证: {platform}")
        return True
    except Exception:
        logger.error("操作失败")
        return False
def get_credentials_path(self, platform: str) -> Optional[str]:
    credentials_path = os.path.join(self.credentials_dir, f"{platform}_credentials.json")
    if os.path.exists(credentials_path):
        return credentials_path
    else:
        logger.warning(f"凭证文件不存在: {credentials_path}")
        return None
def get_platform_config(self, platform: str) -> Dict[str, Any]:
    platform_apis = self.load_config("platform_apis")
    platform_config = platform_apis.get(platform, {})
    return platform_config