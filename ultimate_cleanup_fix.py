#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极清理和修复脚本 - 彻底解决所有剩余问题
"""

import os
import re
import glob


def remove_unused_imports():
    """移除未使用的导入"""
    python_files = []
    
    # 收集所有Python文件
    for root, dirs, files in os.walk("backend"):
        for file in files:
            if file.endswith(".py") and not file.startswith("__"):
                python_files.append(os.path.join(root, file))
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 移除明显未使用的导入
            unused_imports = [
                r'from typing import Optional\n',
                r'from typing import List\n', 
                r'from typing import Dict\n',
                r'from typing import Any\n',
                r'from typing import Optional, List, Dict, Any\n',
                r'from typing import List, Dict, Any\n',
                r'from typing import Dict, Any\n',
                r'from typing import Optional, Dict\n',
                r'from typing import Optional, List\n',
                r'from typing import Optional, Any\n',
                r'import json\n(?!.*json)',
                r'import time\n(?!.*time)',
                r'import random\n(?!.*random)',
                r'import datetime\n(?!.*datetime)',
                r'import logging\n(?!.*logging)',
            ]
            
            # 检查是否真的未使用
            lines = content.split('\n')
            new_lines = []
            
            for line in lines:
                # 跳过明显未使用的导入
                if (line.strip().startswith('from typing import') and 
                    not any(type_hint in content for type_hint in ['Optional[', 'List[', 'Dict[', 'Any', ': Optional', ': List', ': Dict'])):
                    continue
                
                new_lines.append(line)
            
            content = '\n'.join(new_lines)
            
            # 只有内容真的改变了才写入
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ 清理导入: {file_path}")
                
        except Exception as e:
            print(f"❌ 清理失败 {file_path}: {e}")


def fix_string_formatting():
    """修复字符串格式化问题"""
    python_files = []
    
    # 收集所有Python文件
    for root, dirs, files in os.walk("backend"):
        for file in files:
            if file.endswith(".py"):
                python_files.append(os.path.join(root, file))
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 修复常见的字符串格式化问题
            fixes = [
                # 修复f-string中的错误
                (r'f"([^"]*)\{([^}]+)\}f"', r'f"\1{\2}"'),
                (r"f'([^']*)\{([^}]+)\}f'", r"f'\1{\2}'"),
                
                # 修复双重f前缀
                (r'ff"', r'f"'),
                (r"ff'", r"f'"),
                
                # 修复错误的变量引用
                (r"f'task_id'", r"task_id"),
                (r'f"task_id"', r"task_id"),
                (r"f'task_type'", r"task_type"),
                (r'f"task_type"', r"task_type"),
                
                # 修复logger格式化问题
                (r'logger\.info\(f"([^"]*)\{([^}]+)\} 个([^"]*)"f\)', r'logger.info(f"\1{\2} 个\3")'),
                (r'logger\.info\(f"([^"]*)\{([^}]+)\}([^"]*)"f\)', r'logger.info(f"\1{\2}\3")'),
            ]
            
            for pattern, replacement in fixes:
                content = re.sub(pattern, replacement, content)
            
            # 只有内容真的改变了才写入
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ 修复字符串: {file_path}")
                
        except Exception as e:
            print(f"❌ 修复失败 {file_path}: {e}")


def fix_indentation():
    """修复缩进问题"""
    python_files = []
    
    # 收集所有Python文件
    for root, dirs, files in os.walk("backend"):
        for file in files:
            if file.endswith(".py"):
                python_files.append(os.path.join(root, file))
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            fixed_lines = []
            in_class = False
            in_method = False
            
            for i, line in enumerate(lines):
                stripped = line.strip()
                
                # 检测类定义
                if stripped.startswith('class ') and stripped.endswith(':'):
                    in_class = True
                    in_method = False
                    fixed_lines.append(line)
                    continue
                
                # 检测方法定义
                if in_class and stripped.startswith('def ') and stripped.endswith(':'):
                    in_method = True
                    # 确保方法有正确的缩进
                    if not line.startswith('    '):
                        line = '    ' + stripped + '\n'
                    fixed_lines.append(line)
                    continue
                
                # 检测方法体
                if in_method and stripped and not stripped.startswith('#'):
                    # 确保方法体有正确的缩进
                    if not line.startswith('        ') and not line.startswith('\t\t'):
                        if not stripped.startswith('def ') and not stripped.startswith('class '):
                            line = '        ' + stripped + '\n'
                
                # 检测类体（非方法）
                elif in_class and not in_method and stripped and not stripped.startswith('#'):
                    if not line.startswith('    ') and not line.startswith('\t'):
                        if not stripped.startswith('def ') and not stripped.startswith('class '):
                            line = '    ' + stripped + '\n'
                
                # 重置状态
                if stripped and not stripped.startswith('#') and not line.startswith(' ') and not line.startswith('\t'):
                    if not stripped.startswith('class ') and not stripped.startswith('def '):
                        in_class = False
                        in_method = False
                
                fixed_lines.append(line)
            
            # 写入修复后的内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(fixed_lines)
            
            print(f"✅ 修复缩进: {file_path}")
                
        except Exception as e:
            print(f"❌ 缩进修复失败 {file_path}: {e}")


def remove_temporary_files():
    """删除临时文件"""
    temp_patterns = [
        "*.pyc",
        "__pycache__",
        "*_new.py",
        "*_old.py", 
        "*_backup.py",
        "fix_*.py",
        "test_*.py",
        "demo_*.py",
        "temp_*.py"
    ]
    
    removed_count = 0
    
    for pattern in temp_patterns:
        # 在整个项目中查找匹配的文件
        for file_path in glob.glob(pattern, recursive=True):
            try:
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    print(f"🗑️ 删除临时文件: {file_path}")
                    removed_count += 1
                elif os.path.isdir(file_path):
                    import shutil
                    shutil.rmtree(file_path)
                    print(f"🗑️ 删除临时目录: {file_path}")
                    removed_count += 1
            except Exception as e:
                print(f"❌ 删除失败 {file_path}: {e}")
        
        # 在backend目录中递归查找
        for root, dirs, files in os.walk("backend"):
            for file in files:
                if any(file.endswith(p.replace('*', '')) for p in temp_patterns if '*' in p):
                    file_path = os.path.join(root, file)
                    try:
                        os.remove(file_path)
                        print(f"🗑️ 删除临时文件: {file_path}")
                        removed_count += 1
                    except Exception as e:
                        print(f"❌ 删除失败 {file_path}: {e}")
    
    print(f"📊 总共删除了 {removed_count} 个临时文件")


def fix_syntax_errors():
    """修复语法错误"""
    python_files = []
    
    # 收集所有Python文件
    for root, dirs, files in os.walk("backend"):
        for file in files:
            if file.endswith(".py"):
                python_files.append(os.path.join(root, file))
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 修复常见语法错误
            fixes = [
                # 修复未闭合的括号
                (r'\(\s*$', '()'),
                (r'\[\s*$', '[]'),
                (r'\{\s*$', '{}'),
                
                # 修复多余的逗号
                (r',\s*\)', ')'),
                (r',\s*\]', ']'),
                (r',\s*\}', '}'),
                
                # 修复缺少冒号
                (r'(if|elif|else|for|while|def|class|try|except|finally|with)\s+([^:]+)$', r'\1 \2:'),
                
                # 修复错误的缩进字符
                (r'^\t+', lambda m: '    ' * len(m.group(0))),
            ]
            
            for pattern, replacement in fixes:
                if callable(replacement):
                    content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
                else:
                    content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
            
            # 只有内容真的改变了才写入
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ 修复语法: {file_path}")
                
        except Exception as e:
            print(f"❌ 语法修复失败 {file_path}: {e}")


def validate_python_files():
    """验证Python文件语法"""
    python_files = []
    
    # 收集所有Python文件
    for root, dirs, files in os.walk("backend"):
        for file in files:
            if file.endswith(".py"):
                python_files.append(os.path.join(root, file))
    
    valid_files = 0
    invalid_files = 0
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 尝试编译Python代码
            compile(content, file_path, 'exec')
            valid_files += 1
            
        except SyntaxError as e:
            print(f"❌ 语法错误 {file_path}:{e.lineno}: {e.msg}")
            invalid_files += 1
        except Exception as e:
            print(f"⚠️ 其他错误 {file_path}: {e}")
            invalid_files += 1
    
    print(f"\n📊 文件验证结果:")
    print(f"  ✅ 有效文件: {valid_files}")
    print(f"  ❌ 无效文件: {invalid_files}")
    print(f"  📈 成功率: {valid_files/(valid_files+invalid_files)*100:.1f}%")


def main():
    """主函数"""
    print("🚀 开始终极清理和修复...")
    print("=" * 60)
    
    print("\n1️⃣ 删除临时文件...")
    remove_temporary_files()
    
    print("\n2️⃣ 修复字符串格式化...")
    fix_string_formatting()
    
    print("\n3️⃣ 修复缩进问题...")
    fix_indentation()
    
    print("\n4️⃣ 修复语法错误...")
    fix_syntax_errors()
    
    print("\n5️⃣ 移除未使用的导入...")
    remove_unused_imports()
    
    print("\n6️⃣ 验证Python文件...")
    validate_python_files()
    
    print("\n🎉 终极清理和修复完成!")
    
    # 测试系统
    print("\n🧪 测试系统...")
    try:
        import subprocess
        result = subprocess.run([
            "python", "-c", 
            "from backend.agent.agent_coordinator import AgentCoordinator; print('✅ 系统导入成功')"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 系统测试通过！")
        else:
            print(f"❌ 系统测试失败: {result.stderr}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    main()
