# backend.agent.user_interface

"""
用户交互接口 (User Interface Layer / API Endpoints)

该模块负责处理来自用户的输入 (例如通过API、命令行或GUI)，
并将智能体的输出呈现给用户。

对于基于API的系统，这里可能包含:
- API路由定义 (e.g., using FastAPI, Flask)
- 请求/响应模型 (e.g., Pydantic models)
- 用户认证与授权逻辑

对于GUI或CLI，这里可能包含:
- GUI事件处理器
- CLI命令解析器
- 状态管理与视图更新逻辑

核心组件 (示例，具体取决于接口类型):
- APIServer (if web-based): 接收HTTP请求，调用Agent核心逻辑，返回HTTP响应。
- CommandHandler (if CLI-based): 解析命令行参数，执行相应操作。
- UIEventsBridge (if GUI-based): 连接GUI事件与Agent动作。
"""

# 导入接口模块
try:
    from .cli_interface import run_cli_interface
    from .api_server import run_api_server
except ImportError as e:
    print(f"Warning: Could not import some user interface modules: {e}")

__all__ = [
    "run_cli_interface",
    "run_api_server"
]

print("Backend Agent: User Interface Layer initialized.")
