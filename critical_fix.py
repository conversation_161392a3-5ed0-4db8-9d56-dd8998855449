#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键修复脚本 - 修复最严重的语法错误
"""

import os
import re


def fix_critical_syntax_errors(file_path: str) -> int:
    """修复关键语法错误"""
    fixes_count = 0
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复最严重的语法错误
        patterns = [
            # 修复未终止的字符串
            (r"\.wmv\\'\}", r".wmv'}"),
            (r"\.gi\\'\}", r".gif'}"),
            (r"'generic\\'\)", r"'generic')"),
            (r"'\.py\\'\)", r"'.py')"),
            (r"'output_path\\'\]", r"'output_path']"),
            (r"'status\\'\)", r"'status')"),
            (r"'unknown\\'\)", r"'unknown')"),
            (r"'\.png\\'\]", r"'.png']"),
            
            # 修复错误的f-string
            (r'""f"', r'"""'),
            (r"''f'", r"'''"),
            (r'"""([^"]*?)""f"', r'"""\1"""'),
            (r"'''([^']*?)''f'", r"'''\1'''"),
            
            # 修复缩进问题 - 在方法定义后添加缩进
            (r'^(\s*)def ([^:]+):\n([^\s])', r'\1def \2:\n\1    \3'),
            
            # 修复User-Agent字符串
            (r'Mozilla/5\.0 \(\s*Windows NT 10\.0; Win64; x64\) AppleWebKit/537\.36 \(KHTML,\s*like Gecko\) Chrome/91\.0\.4472\.124 Safari/537\.36"\s*,', 
             r'"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",'),
            
            # 修复application/json
            (r'"application/json"', r'"application/json"'),
        ]
        
        for pattern, replacement in patterns:
            new_content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
            if new_content != content:
                fixes_count += len(re.findall(pattern, content, flags=re.MULTILINE))
                content = new_content
        
        # 只有在内容发生变化时才写入文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ 修复 {file_path}: {fixes_count} 个关键语法错误")
        
        return fixes_count
        
    except Exception as e:
        print(f"✗ 修复 {file_path} 失败: {e}")
        return 0


def main():
    """主函数"""
    print("🔧 关键语法错误修复")
    print("=" * 30)
    
    # 获取项目根目录
    project_root = os.path.dirname(os.path.abspath(__file__))
    
    # 需要修复的文件列表
    critical_files = [
        "backend/agent/content_analyzer.py",
        "backend/agent/content_analyzer/content_analyzer.py", 
        "backend/agent/action_execution_engine/task_executor.py",
        "backend/agent/agent_controller.py",
        "backend/agent/content_optimizer/content_optimizer.py",
        "backend/agent/tools/api_clients/xigua_api.py",
        "backend/agent/tools/api_clients/youtube_api.py",
        "backend/agent/user_interface/cli_interface.py",
        "backend/agent/user_interface/api_server.py",
        "backend/utils/config_loader.py",
        "fix_project.py",
    ]
    
    total_fixes = 0
    
    # 修复每个文件
    for relative_path in critical_files:
        file_path = os.path.join(project_root, relative_path)
        
        if os.path.exists(file_path):
            fixes = fix_critical_syntax_errors(file_path)
            total_fixes += fixes
        else:
            print(f"⚠️ 文件不存在: {relative_path}")
    
    print(f"\n🎉 修复完成！总共修复了 {total_fixes} 个关键语法错误")


if __name__ == "__main__":
    main()
