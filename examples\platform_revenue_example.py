#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
平台收益数据获取示例脚本

这个脚本展示了如何使用各平台的API客户端获取收益数据。
"""

import datetime
import json
import logging
import os
import sys
from typing import Any
from typing import Dict
from typing import List

# 添加项目根目录到 Python 路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# 导入 API 客户端
from backend.agent.tools.api_clients import ToutiaoAPI
from backend.agent.tools.api_clients import XiaohongshuAPI
from backend.agent.tools.api_clients import XiguaAPI
from backend.agent.tools.api_clients import YouTubeAPI

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

def get_platform_revenue(platform: str, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
    """
    获取指定平台的收益数据

    Args:
        platform: 平台名称 (youtube, xigua, toutiao, xiaohongshu)
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)

    Returns:
        收益数据
    """
    # 设置默认日期范围
    if end_date is None:
        end_date = datetime.datetime.now().strftime("%Y-%m-%d")
    if start_date is None:
        # 默认获取最近 30 天的数据
        start_date = (datetime.datetime.now() - datetime.timedelta(days=30)).strftime("%Y-%m-%d")

    # 根据平台选择对应的 API 客户端
    if platform.lower() == "youtube":
        # 从环境变量或配置文件获取凭证
        credentials_path = os.environ.get("YOUTUBE_CREDENTIALS_PATH", "config/credentials/youtube.json")
        api_key = os.environ.get("YOUTUBE_API_KEY")

        # 创建 API 客户端
        api = YouTubeAPI(credentials_path=credentials_path, api_key=api_key)

        # 获取收益数据
        # 注意：YouTube API 没有直接的收益数据接口，这里使用分析数据作为示例
        channel_id = os.environ.get("YOUTUBE_CHANNEL_ID", "UC1234567890abcdefghij")
        analytics = api.get_channel_analytics(
            channel_id=channel_id,
            start_date=start_date,
            end_date=end_date,
            metrics=["views", "estimatedRevenue", "estimatedAdRevenue", "estimatedRedPartnerRevenue"],
        )

        # 构建收益数据结构
        revenue_data = {
            "platform": "YouTube",
            "start_date": start_date,
            "end_date": end_date,
            "data": analytics.get("data", []),
            "summary": {
                "total_revenue": analytics.get("totals", {}).get("estimatedRevenue", 0),
                "ad_revenue": analytics.get("totals", {}).get("estimatedAdRevenue", 0),
                "subscription_revenue": analytics.get("totals", {}).get("estimatedRedPartnerRevenue", 0),
                "total_views": analytics.get("totals", {}).get("views", 0),
            },
        }

    elif platform.lower() == "xigua":
        # 从环境变量或配置文件获取凭证
        credentials_path = os.environ.get("XIGUA_CREDENTIALS_PATH")
        cookie = os.environ.get("XIGUA_COOKIE")

        # 创建 API 客户端
        api = XiguaAPI(credentials_path=credentials_path, cookie=cookie)

        # 获取收益数据
        revenue_data = api.get_revenue_data(start_date=start_date, end_date=end_date)

        # 添加平台信息
        revenue_data["platform"] = "西瓜视频"

    elif platform.lower() == "toutiao":
        # 从环境变量或配置文件获取凭证
        credentials_path = os.environ.get("TOUTIAO_CREDENTIALS_PATH")
        cookie = os.environ.get("TOUTIAO_COOKIE")

        # 创建 API 客户端
        api = ToutiaoAPI(credentials_path=credentials_path, cookie=cookie)

        # 获取收益数据
        revenue_data = api.get_revenue_data(start_date=start_date, end_date=end_date)

        # 添加平台信息
        revenue_data["platform"] = "今日头条"

    elif platform.lower() == "xiaohongshu":
        # 从环境变量或配置文件获取凭证
        credentials_path = os.environ.get("XIAOHONGSHU_CREDENTIALS_PATH")
        cookie = os.environ.get("XIAOHONGSHU_COOKIE")

        # 创建 API 客户端
        api = XiaohongshuAPI(credentials_path=credentials_path, cookie=cookie)

        # 获取收益数据
        revenue_data = api.get_revenue_data(start_date=start_date, end_date=end_date)

        # 添加平台信息
        revenue_data["platform"] = "小红书"

    else:
        raise ValueError("不支持的平台: {platform}")

    return revenue_data

def get_all_platforms_revenue(
    platforms: List[str] = None, start_date: str = None, end_date: str = None
) -> Dict[str, Any]:
    """
    获取所有平台的收益数据

    Args:
        platforms: 平台列表，如果为 None 则获取所有支持的平台
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)

    Returns:
        所有平台的收益数据
    """
    # 设置默认平台列表
    if platforms is None:
        platforms = ["youtube", "xigua", "toutiao", "xiaohongshu"]

    # 获取每个平台的收益数据
    all_revenue_data = {}
    total_revenue = 0

    for platform in platforms:
        try:
            revenue_data = get_platform_revenue(platform, start_date, end_date)
            all_revenue_data[platform] = revenue_data

            # 累加总收益
            platform_total = revenue_data.get("summary", {}).get("total_revenue", 0)
            total_revenue += platform_total

            logger.info("获取 {platform} 收益数据成功，总收益: {platform_total:.2f}")
        except Exception as e:
            logger.error(f"获取 {platform} 收益数据失败: {e}")

    # 构建汇总结果
    result = {
        "start_date": start_date,
        "end_date": end_date,
        "platforms": all_revenue_data,
        "total_revenue": total_revenue,
        "timestamp": datetime.datetime.now().isoformat(),
    }

    return result

def save_revenue_data(data: Dict[str, Any], output_path: str) -> None:
    """
    保存收益数据到文件

    Args:
        data: 收益数据
        output_path: 输出文件路径
    """
    try:
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 保存数据到文件
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        logger.info("收益数据已保存到: {output_path}")
    except Exception as e:
        logger.error("保存收益数据失败: {e}")

def main():
    """主函数"""
    import argparse

    # 解析命令行参数
    parser = argparse.ArgumentParser(description="获取平台收益数据")
    parser.add_argument("--platforms", nargs="+", help="平台列表，用空格分隔")
    parser.add_argument("--start-date", help="开始日期 (YYYY-MM-DD)")
    parser.add_argument("--end-date", help="结束日期 (YYYY-MM-DD)")
    parser.add_argument("--output", default="data/revenue_data.json", help="输出文件路径")

    args = parser.parse_args()

    # 获取收益数据
    revenue_data = get_all_platforms_revenue(
        platforms=args.platforms, start_date=args.start_date, end_date=args.end_date
    )

    # 打印总收益
    print(f"\n总收益: {revenue_data['total_revenue']:.2f}")

    # 打印各平台收益
    print("\n各平台收益:")
    for platform, data in revenue_data["platforms"].items():
        data.get("platform", platform)
        data.get("summary", {}).get("total_revenue", 0)
        print("  {platform_name}: {platform_revenue:.2f}")

    # 保存收益数据
    save_revenue_data(revenue_data, args.output)

if __name__ == "__main__":
    main()
