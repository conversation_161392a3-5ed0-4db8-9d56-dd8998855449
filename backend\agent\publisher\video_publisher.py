# backend.agent.publisher.video_publisher

import json
import logging
import os
import random
import time
from typing import Any, Dict, List, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


class VideoPublisher:
    """
    视频发布器：负责将编辑好的视频发布到各个平台，管理发布状态和数据统计。
    """

    def __init__(self, config_dir: str = None, credentials_dir: str = None):
        """
        初始化视频发布器。

        Args:
            config_dir: 配置文件目录，默认为当前目录下的 'config'
            credentials_dir: 凭证文件目录，默认为当前目录下的 'config/credentials'
        """
        self.config_dir = config_dir or os.path.join(os.getcwd(), "config")
        self.credentials_dir = credentials_dir or os.path.join(self.config_dir, "credentials")

        # 确保目录存在
        os.makedirs(self.config_dir, exist_ok=True)
        os.makedirs(self.credentials_dir, exist_ok=True)

        # 支持的平台
        self.supported_platforms = {
            "douyin": "抖音",
            "kuaishou": "快手",
            "bilibili": "B站",
            "youtube": "YouTube",
            "weibo": "微博",
            "xiaohongshu": "小红书",
            "wechat": "微信视频号",
        }

        # 平台凭证
        self.platform_credentials = self._load_credentials()

        # 发布历史记录
        self.publish_history = self._load_publish_history()

        logger.info(f"VideoPublisher 初始化完成。支持的平台: {', '.join(self.supported_platforms.keys())}")

    def _load_credentials(self) -> Dict[str, Dict[str, Any]]:
        """
        加载平台凭证。

        Returns:
            平台凭证字典
        """
        credentials = {}

        # 尝试从凭证目录加载各平台的凭证文件
        for platform in self.supported_platforms:
            credential_path = os.path.join(self.credentials_dir, "{platform}_credentials.json")

            if os.path.exists(credential_path):
                try:
                    with open(credential_path, "r", encoding="utf-8") as f:
                        credentials[platform] = json.load(f)
                    logger.info("已加载 {platform} 平台凭证")
                except Exception as e:
                    logger.error("加载 {platform} 平台凭证失败: {e}")
            else:
                logger.warning("{platform} 平台凭证文件不存在: {credential_path}")

        return credentials

    def _load_publish_history(self) -> List[Dict[str, Any]]:
        """
        加载发布历史记录。

        Returns:
            发布历史记录列表
        """
        history_path = os.path.join(self.config_dir, "publish_history.json")

        if os.path.exists(history_path):
            try:
                with open(history_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                logger.error("加载发布历史记录失败: {e}")
                return []
        else:
            logger.info("发布历史记录文件不存在，将创建新的记录")
            return []

    def _save_publish_history(self) -> None:
        """保存发布历史记录"""
        history_path = os.path.join(self.config_dir, "publish_history.json")

        try:
            with open(history_path, "w", encoding="utf-8") as f:
                json.dump(self.publish_history, f, ensure_ascii=False, indent=2)
            logger.info("发布历史记录已保存到: {history_path}")
        except Exception as e:
            logger.error("保存发布历史记录失败: {e}")

    def publish_video(
        self, video_path: str, platforms: List[str], metadata: Dict[str, Any], schedule_time: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        发布视频到指定平台。

        Args:
            video_path: 视频文件路径
            platforms: 目标平台列表
            metadata: 视频元数据，包括标题、描述、标签等
            schedule_time: 计划发布时间，如果为None则立即发布

        Returns:
            发布结果字典
        """
        logger.info(f"开始发布视频: {video_path} 到平台: {', '.join(platforms)}")

        # 检查视频文件是否存在
        if not os.path.exists(video_path):
            logger.error(f"视频文件不存在: {video_path}")
            return {"status": "error", "message": "视频文件不存在: {video_path}", "platforms": {}}

        # 检查平台是否支持
        unsupported_platforms = [p for p in platforms if p not in self.supported_platforms]
        if unsupported_platforms:
            logger.warning(f"不支持的平台: {', '.join(unsupported_platforms)}")

        # 过滤出支持的平台
        supported_platforms = [p for p in platforms if p in self.supported_platforms]

        # 检查是否有凭证
        platforms_with_credentials = [p for p in supported_platforms if p in self.platform_credentials]
        platforms_without_credentials = [p for p in supported_platforms if p not in self.platform_credentials]

        if platforms_without_credentials:
            logger.warning(f"以下平台缺少凭证: {', '.join(platforms_without_credentials)}")

        if not platforms_with_credentials:
            logger.error("没有可用的平台凭证，无法发布视频")
            return {"status": "error", "message": "没有可用的平台凭证，无法发布视频", "platforms": {}}

        # 准备发布结果
        publish_results = {
            "status": "success",
            "video_path": video_path,
            "publish_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "scheduled_time": schedule_time,
            "platforms": {},
        }

        # 为每个平台发布视频
        for platform in platforms_with_credentials:
            try:
                # 模拟发布过程
                logger.info("模拟发布视频到 {platform}...")

                # 根据平台调整元数据
                platform_metadata = self._adapt_metadata_for_platform(metadata, platform)

                # 模拟上传和发布
                result = self._simulate_publish_to_platform(video_path, platform, platform_metadata, schedule_time)

                publish_results["platforms"][platform] = result

                logger.info(f"视频已成功发布到 {platform}, 视频ID: {result.get('video_id')}")
            except Exception as e:
                logger.error("发布视频到 {platform} 失败: {e}")
                publish_results["platforms"][platform] = {"status": "error", "message": f"发布失败: {e}"}

        # 更新发布历史记录
        self.publish_history.append(
            {
                "video_path": video_path,
                "publish_time": publish_results["publish_time"],
                "scheduled_time": schedule_time,
                "platforms": publish_results["platforms"],
                "metadata": metadata,
            }
        )

        # 保存发布历史记录
        self._save_publish_history()

        return publish_results

    def _adapt_metadata_for_platform(self, metadata: Dict[str, Any], platform: str) -> Dict[str, Any]:
        """
        根据平台调整元数据。

        Args:
            metadata: 原始元数据
            platform: 目标平台

        Returns:
            调整后的元数据
        """
        # 复制原始元数据
        adapted_metadata = metadata.copy()

        # 根据平台特性调整
        if platform == "douyin":
            # 抖音标题长度限制
            if "title" in adapted_metadata and len(adapted_metadata["title"]) > 30:
                adapted_metadata["title"] = adapted_metadata["title"][:27] + "..."

            # 抖音标签格式
            if "tags" in adapted_metadata:
                adapted_metadata["tags"] = ["#" + tag for tag in adapted_metadata["tags"]]

        elif platform == "bilibili":
            # B站支持更长的标题
            if "title" in adapted_metadata and len(adapted_metadata["title"]) > 80:
                adapted_metadata["title"] = adapted_metadata["title"][:77] + "..."

            # B站分区
            if "category" not in adapted_metadata:
                adapted_metadata["category"] = "生活"

        elif platform == "youtube":
            # YouTube标签格式
            if "tags" in adapted_metadata:
                # 确保标签不超过500个字符
                tags = adapted_metadata["tags"]
                total_length = sum(len(tag) for tag in tags) + len(tags) - 1
                if total_length > 500:
                    # 移除一些标签以适应限制
                    while total_length > 500 and tags:
                        total_length -= len(tags[-1]) + 1
                        tags.pop()
                adapted_metadata["tags"] = tags

        return adapted_metadata

    def _simulate_publish_to_platform(
        self, video_path: str, platform: str, metadata: Dict[str, Any], schedule_time: Optional[str]
    ) -> Dict[str, Any]:
        """
        模拟发布视频到平台。

        Args:
            video_path: 视频文件路径
            platform: 目标平台
            metadata: 调整后的元数据
            schedule_time: 计划发布时间

        Returns:
            发布结果
        """
        # 模拟上传过程
        logger.info("模拟上传视频到 {platform}...")
        time.sleep(0.5)  # 模拟上传延迟

        # 生成随机视频ID
        video_id = "{platform}_{int(time.time())}_{random.randint(1000, 9999)}"

        # 生成随机URL
        video_url = f"https://{platform}.com/video/{video_id}"

        # 返回模拟结果
        return {
            "status": "success",
            "video_id": video_id,
            "video_url": video_url,
            "publish_time": time.strftime("%Y-%m-%d %H:%M:%S") if not schedule_time else None,
            "scheduled_time": schedule_time,
            "metadata": metadata,
        }

    def get_publish_status(self, video_id: str, platform: str) -> Dict[str, Any]:
        """
        获取视频发布状态。

        Args:
            video_id: 视频ID
            platform: 平台

        Returns:
            发布状态
        """
        logger.info("获取视频发布状态: {video_id} 在 {platform}")

        # 模拟获取发布状态
        # 实际应用中，这里会调用平台API获取真实状态

        # 随机状态
        statuses = ["published", "processing", "failed", "scheduled"]
        status = random.choice(statuses)

        result = {
            "video_id": video_id,
            "platform": platform,
            "status": status,
            "check_time": time.strftime("%Y-%m-%d %H:%M:%S"),
        }

        if status == "published":
            result["publish_time"] = time.strftime(
                "%Y-%m-%d %H:%M:%S", time.localtime(time.time() - random.randint(60, 3600))
            )
            result["views"] = random.randint(10, 10000)
            result["likes"] = random.randint(1, result["views"] // 10)
            result["comments"] = random.randint(0, result["likes"] // 5)
            result["shares"] = random.randint(0, result["likes"] // 10)
        elif status == "processing":
            result["progress"] = random.randint(10, 90)
            result["estimated_completion"] = time.strftime(
                "%Y-%m-%d %H:%M:%S", time.localtime(time.time() + random.randint(60, 600))
            )
        elif status == "failed":
            errors = ["上传失败", "处理失败", "内容审核未通过", "格式不支持"]
            result["error"] = random.choice(errors)
            result["can_retry"] = random.choice([True, False])
        elif status == "scheduled":
            result["scheduled_time"] = time.strftime(
                "%Y-%m-%d %H:%M:%S", time.localtime(time.time() + random.randint(3600, 86400))
            )

        return result

    def get_video_analytics(self, video_id: str, platform: str) -> Dict[str, Any]:
        """
        获取视频分析数据。

        Args:
            video_id: 视频ID
            platform: 平台

        Returns:
            视频分析数据
        """
        logger.info(f"获取视频分析数据: {video_id} 在 {platform}")

        # 模拟获取视频分析数据
        # 实际应用中，这里会调用平台API获取真实数据

        # 基本数据
        views = random.randint(100, 100000)
        watch_time = views * random.uniform(0.1, 0.8)  # 平均观看时长比例

        analytics = {
            "video_id": video_id,
            "platform": platform,
            "data_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "basic_metrics": {
                "views": views,
                "likes": random.randint(views // 100, views // 10),
                "comments": random.randint(views // 1000, views // 100),
                "shares": random.randint(views // 500, views // 50),
                "watch_time_hours": round(watch_time / 3600, 2),
                "avg_watch_percentage": round(random.uniform(0.2, 0.8), 2),
                "unique_viewers": random.randint(views // 2, views),
            },
            "audience": {
                "gender_ratio": {
                    "male": round(random.uniform(0.3, 0.7), 2),
                    "female": round(random.uniform(0.3, 0.7), 2),
                },
                "age_groups": {
                    "13-17": round(random.uniform(0.05, 0.2), 2),
                    "18-24": round(random.uniform(0.2, 0.4), 2),
                    "25-34": round(random.uniform(0.2, 0.4), 2),
                    "35-44": round(random.uniform(0.1, 0.3), 2),
                    "45+": round(random.uniform(0.05, 0.2), 2),
                },
                "top_regions": [
                    {"name": "北京", "percentage": round(random.uniform(0.05, 0.2), 2)},
                    {"name": "上海", "percentage": round(random.uniform(0.05, 0.2), 2)},
                    {"name": "广东", "percentage": round(random.uniform(0.05, 0.2), 2)},
                    {"name": "江苏", "percentage": round(random.uniform(0.05, 0.15), 2)},
                    {"name": "浙江", "percentage": round(random.uniform(0.05, 0.15), 2)},
                ],
            },
            "engagement": {
                "engagement_rate": round(random.uniform(0.01, 0.1), 3),
                "peak_times": [
                    {"hour": 12, "views_percentage": round(random.uniform(0.05, 0.15), 2)},
                    {"hour": 18, "views_percentage": round(random.uniform(0.1, 0.25), 2)},
                    {"hour": 21, "views_percentage": round(random.uniform(0.15, 0.3), 2)},
                ],
                "traffic_sources": [
                    {"source": "推荐页", "percentage": round(random.uniform(0.4, 0.7), 2)},
                    {"source": "搜索", "percentage": round(random.uniform(0.1, 0.3), 2)},
                    {"source": "个人主页", "percentage": round(random.uniform(0.05, 0.2), 2)},
                    {"source": "分享", "percentage": round(random.uniform(0.05, 0.15), 2)},
                ],
            },
        }

        # 根据平台添加特定数据
        if platform == "douyin":
            analytics["platform_specific"] = {
                "duet_count": random.randint(0, views // 1000),
                "stitch_count": random.randint(0, views // 1000),
                "hashtag_performance": [
                    {"tag": "#" + tag, "views": random.randint(views // 10, views // 2)}
                    for tag in ["热门", "推荐", "生活"]
                ],
            }
        elif platform == "bilibili":
            analytics["platform_specific"] = {
                "coin_count": random.randint(views // 200, views // 20),
                "favorite_count": random.randint(views // 100, views // 10),
                "danmaku_count": random.randint(views // 50, views // 5),
                "ranking_position": random.randint(1, 1000) if random.random() > 0.7 else None,
            }
        elif platform == "youtube":
            analytics["platform_specific"] = {
                "subscribers_gained": random.randint(0, views // 100),
                "subscribers_lost": random.randint(0, views // 500),
                "estimated_revenue": {"currency": "USD", "amount": round(views / 1000 * random.uniform(1, 5), 2)},
                "card_clicks": random.randint(0, views // 200),
                "end_screen_clicks": random.randint(0, views // 150),
            }

        return analytics

    def delete_video(self, video_id: str, platform: str) -> Dict[str, Any]:
        """
        从平台删除视频。

        Args:
            video_id: 视频ID
            platform: 平台

        Returns:
            删除结果
        """
        logger.info(f"删除视频: {video_id} 从 {platform}")

        # 模拟删除视频
        # 实际应用中，这里会调用平台API删除视频

        # 随机成功或失败
        success = random.random() > 0.1

        if success:
            result = {
                "status": "success",
                "video_id": video_id,
                "platform": platform,
                "delete_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "message": "视频已成功删除",
            }

            # 更新发布历史记录
            for record in self.publish_history:
                if platform in record["platforms"] and record["platforms"][platform].get("video_id") == video_id:
                    record["platforms"][platform]["deleted"] = True
                    record["platforms"][platform]["delete_time"] = result["delete_time"]

            # 保存发布历史记录
            self._save_publish_history()
        else:
            result = {
                "status": "error",
                "video_id": video_id,
                "platform": platform,
                "message": "删除视频失败，请稍后重试",
            }

        return result

    def get_platform_limits(self, platform: str) -> Dict[str, Any]:
        """
        获取平台限制信息。

        Args:
            platform: 平台名称

        Returns:
            平台限制信息
        """
        logger.info(f"获取平台限制信息: {platform}")

        # 平台限制信息
        platform_limits = {
            "douyin": {
                "max_video_length": 600,  # 10分钟
                "max_file_size": 500,  # MB
                "supported_formats": ["mp4", "mov"],
                "max_title_length": 30,
                "max_description_length": 2000,
                "max_tags": 20,
                "daily_upload_limit": 50,
            },
            "kuaishou": {
                "max_video_length": 900,  # 15分钟
                "max_file_size": 1000,  # MB
                "supported_formats": ["mp4", "mov", "avi"],
                "max_title_length": 40,
                "max_description_length": 1000,
                "max_tags": 10,
                "daily_upload_limit": 30,
            },
            "bilibili": {
                "max_video_length": 3600,  # 60分钟
                "max_file_size": 8000,  # MB
                "supported_formats": ["mp4", "flv", "mov", "wmv", "avi"],
                "max_title_length": 80,
                "max_description_length": 5000,
                "max_tags": 12,
                "daily_upload_limit": 10,
            },
            "youtube": {
                "max_video_length": 43200,  # 12小时
                "max_file_size": 128000,  # MB
                "supported_formats": ["mp4", "mov", "avi", "wmv", "flv", "3gp", "webm"],
                "max_title_length": 100,
                "max_description_length": 5000,
                "max_tags": 500,  # 字符总数
                "daily_upload_limit": 100,
            },
            "weibo": {
                "max_video_length": 900,  # 15分钟
                "max_file_size": 1000,  # MB
                "supported_formats": ["mp4", "mov"],
                "max_title_length": 140,
                "max_description_length": 2000,
                "max_tags": 10,
                "daily_upload_limit": 20,
            },
            "xiaohongshu": {
                "max_video_length": 900,  # 15分钟
                "max_file_size": 1000,  # MB
                "supported_formats": ["mp4", "mov"],
                "max_title_length": 60,
                "max_description_length": 1000,
                "max_tags": 20,
                "daily_upload_limit": 10,
            },
            "wechat": {
                "max_video_length": 600,  # 10分钟
                "max_file_size": 1000,  # MB
                "supported_formats": ["mp4"],
                "max_title_length": 40,
                "max_description_length": 200,
                "max_tags": 0,  # 不支持标签
                "daily_upload_limit": 10,
            },
        }

        if platform in platform_limits:
            return {
                "platform": platform,
                "platform_name": self.supported_platforms.get(platform, platform),
                "limits": platform_limits[platform],
            }
        else:
            return {"status": "error", "message": "不支持的平台: {platform}"}

    def get_publish_history(self, platform: Optional[str] = None, limit: int = 10, offset: int = 0) -> Dict[str, Any]:
        """
        获取发布历史记录。

        Args:
            platform: 平台名称，如果为None则获取所有平台
            limit: 返回记录数量限制
            offset: 记录偏移量

        Returns:
            发布历史记录
        """
        logger.info("获取发布历史记录: platform={platform}, limit={limit}, offset={offset}")

        # 过滤记录
        if platform:
            filtered_history = [record for record in self.publish_history if platform in record.get("platforms", {})]
        else:
            filtered_history = self.publish_history

        # 排序 (按发布时间倒序)
        sorted_history = sorted(filtered_history, key=lambda x: x.get("publish_time", ""), reverse=True)

        # 分页
        paginated_history = sorted_history[offset : offset + limit]

        return {
            "total": len(filtered_history),
            "limit": limit,
            "offset": offset,
            "platform": platform,
            "records": paginated_history,
        }
