import json
import logging
import os
from typing import Any, Dict, List, Optional

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


class PlatformAdapter:
    """
    平台适配器：负责将生成的内容适配到不同社交媒体或视频平台的发布要求，包括格式转换、分辨率调整、元数据填充等。
    """

    def __init__(self, config_dir: str = None, temp_dir: str = None):
        """
        初始化平台适配器。

        Args:
            config_dir: 配置文件目录，默认为当前目录下的 'config'
            temp_dir: 临时文件目录，默认为当前目录下的 'temp'
        """
        self.config_dir = config_dir or os.path.join(os.getcwd(), "config")
        self.temp_dir = temp_dir or os.path.join(os.getcwd(), "temp")

        # 确保目录存在
        os.makedirs(self.config_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)

        # 支持的平台列表
        self.supported_platforms = {
            "douyin": {
                "display_name": "抖音",
                "max_duration": 180,  # 秒
                "preferred_aspect_ratios": ["9:16", "1:1"],
                "max_file_size": 500,  # MB
                "supported_formats": ["mp4"],
                "max_resolution": "1080p",
            },
            "kuaishou": {
                "display_name": "快手",
                "max_duration": 57,  # 秒
                "preferred_aspect_ratios": ["9:16", "1:1"],
                "max_file_size": 500,  # MB
                "supported_formats": ["mp4"],
                "max_resolution": "1080p",
            },
            "bilibili": {
                "display_name": "哔哩哔哩",
                "max_duration": 1800,  # 秒
                "preferred_aspect_ratios": ["16:9"],
                "max_file_size": 8000,  # MB
                "supported_formats": ["mp4", "flv"],
                "max_resolution": "4K",
            },
            "weibo": {
                "display_name": "微博",
                "max_duration": 60,  # 秒
                "preferred_aspect_ratios": ["16:9", "1:1"],
                "max_file_size": 500,  # MB
                "supported_formats": ["mp4"],
                "max_resolution": "1080p",
            },
            "wechat": {
                "display_name": "微信视频号",
                "max_duration": 60,  # 秒
                "preferred_aspect_ratios": ["9:16"],
                "max_file_size": 1000,  # MB
                "supported_formats": ["mp4"],
                "max_resolution": "1080p",
            },
            "xiaohongshu": {
                "display_name": "小红书",
                "max_duration": 60,  # 秒
                "preferred_aspect_ratios": ["9:16", "1:1"],
                "max_file_size": 500,  # MB
                "supported_formats": ["mp4"],
                "max_resolution": "1080p",
            },
            "youtube": {
                "display_name": "YouTube",
                "max_duration": 43200,  # 秒 (12小时)
                "preferred_aspect_ratios": ["16:9"],
                "max_file_size": 128000,  # MB (128GB)
                "supported_formats": ["mp4", "mov", "avi"],
                "max_resolution": "8K",
            },
        }

        # 加载平台配置
        self._load_platform_configs()

        logger.info(f"PlatformAdapter 初始化完成。支持的平台: {', '.join(self.supported_platforms.keys())}")

    def _load_platform_configs(self):
        """加载平台配置文件"""
        config_path = os.path.join(self.config_dir, "platform_configs.json")
        if os.path.exists(config_path):
            try:
                with open(config_path, "r", encoding="utf-8") as f:
                    custom_configs = json.load(f)
                    # 更新默认配置
                    for platform, config in custom_configs.items():
                        if platform in self.supported_platforms:
                            self.supported_platforms[platform].update(config)
                        else:
                            self.supported_platforms[platform] = config
                logger.info("已加载自定义平台配置: {config_path}")
            except Exception as e:
                logger.warning("加载平台配置文件失败: {e}")

    def get_supported_platforms(self) -> List[str]:
        """
        获取支持的平台列表。

        Returns:
            支持的平台名称列表
        """
        return list(self.supported_platforms.keys())

    def get_platform_requirements(self, platform: str) -> Dict[str, Any]:
        """
        获取特定平台的要求。

        Args:
            platform: 平台名称

        Returns:
            平台要求的字典
        """
        if platform not in self.supported_platforms:
            logger.warning(f"不支持的平台: {platform}")
            return {}

        return self.supported_platforms[platform]

    def check_video_compatibility(self, video_path: str, platform: str) -> Dict[str, Any]:
        """
        检查视频是否符合平台要求。

        Args:
            video_path: 视频文件路径
            platform: 目标平台

        Returns:
            兼容性检查结果字典
        """
        if platform not in self.supported_platforms:
            logger.warning(f"不支持的平台: {platform}")
            return {"compatible": False, "issues": ["不支持的平台"]}

        if not os.path.exists(video_path):
            logger.warning(f"视频文件不存在: {video_path}")
            return {"compatible": False, "issues": ["视频文件不存在"]}

        # 获取平台要求
        platform_reqs = self.supported_platforms[platform]

        # 获取视频信息 (实际应用中使用FFmpeg或其他工具)
        # 这里使用模拟数据
        video_info = self._get_video_info(video_path)

        # 检查兼容性
        issues = []

        # 检查文件格式
        file_ext = os.path.splitext(video_path)[1][1:].lower()
        if file_ext not in platform_reqs["supported_formats"]:
            issues.append(f"文件格式不支持: {file_ext}，支持的格式: {platform_reqs['supported_formats']}")

        # 检查时长
        if video_info["duration_seconds"] > platform_reqs["max_duration"]:
            issues.append(
                f"视频时长超过限制: {video_info['duration_seconds']}秒，最大允许: {platform_reqs['max_duration']}秒"
            )

        # 检查文件大小
        file_size_mb = os.path.getsize(video_path) / (1024 * 1024)
        if file_size_mb > platform_reqs["max_file_size"]:
            issues.append(f"文件大小超过限制: {file_size_mb:.2f}MB，最大允许: {platform_reqs['max_file_size']}MB")

        # 检查分辨率
        width, height = video_info["width"], video_info["height"]
        aspect_ratio = self._calculate_aspect_ratio(width, height)
        if aspect_ratio not in platform_reqs["preferred_aspect_ratios"]:
            issues.append(f"宽高比不是首选: {aspect_ratio}，首选比例: {platform_reqs['preferred_aspect_ratios']}")

        # 检查分辨率是否超过平台支持的最大值
        max_res = platform_reqs["max_resolution"]
        if max_res == "1080p" and (width > 1920 or height > 1080):
            issues.append("分辨率超过平台支持的最大值: {width}x{height}，最大支持: 1920x1080")
        elif max_res == "4K" and (width > 3840 or height > 2160):
            issues.append("分辨率超过平台支持的最大值: {width}x{height}，最大支持: 3840x2160")
        elif max_res == "8K" and (width > 7680 or height > 4320):
            issues.append(f"分辨率超过平台支持的最大值: {width}x{height}，最大支持: 7680x4320")

        return {
            "compatible": len(issues) == 0,
            "issues": issues,
            "video_info": video_info,
            "platform_requirements": platform_reqs,
        }

    def _get_video_info(self, video_path: str) -> Dict[str, Any]:
        """
        获取视频信息。

        Args:
            video_path: 视频文件路径

        Returns:
            视频信息字典
        """
        # 实际应用中，这里会使用FFmpeg或其他工具获取视频信息
        # 这里使用模拟数据
        file_ext = os.path.splitext(video_path)[1][1:].lower()
        file_size = os.path.getsize(video_path)

        # 模拟视频信息
        return {
            "path": video_path,
            "format": file_ext,
            "duration_seconds": 45,  # 模拟45秒
            "width": 1920,
            "height": 1080,
            "fps": 30,
            "bitrate": "5000k",
            "file_size_bytes": file_size,
            "file_size_mb": file_size / (1024 * 1024),
        }

    def _calculate_aspect_ratio(self, width: int, height: int) -> str:
        """
        计算宽高比。

        Args:
            width: 宽度
            height: 高度

        Returns:
            宽高比字符串 (例如 "16:9")
        """

        def gcd(a, b):
            """计算最大公约数"""
            while b:
                a, b = b, a % b
            return a

        # 计算最大公约数
        g = gcd(width, height)

        # 简化比例
        w = width // g
        h = height // g

        # 检查是否接近标准比例
        standard_ratios = {"16:9": (16, 9), "9:16": (9, 16), "4:3": (4, 3), "1:1": (1, 1), "21:9": (21, 9)}

        # 检查是否接近标准比例 (允许5%的误差)
        for ratio_name, (std_w, std_h) in standard_ratios.items():
            actual_ratio = width / height
            std_ratio = std_w / std_h

            if abs(actual_ratio - std_ratio) / std_ratio < 0.05:
                return ratio_name

        # 如果不接近任何标准比例，返回简化后的比例
        return "{w}:{h}"

    def adapt_for_platform(self, video_path: str, platform: str, output_path: Optional[str] = None) -> Dict[str, Any]:
        """
        根据平台要求适配视频。

        Args:
            video_path: 视频文件路径
            platform: 目标平台
            output_path: 输出文件路径，如果为None则自动生成

        Returns:
            适配结果字典，包含适配后的视频路径和适配信息
        """
        if platform not in self.supported_platforms:
            logger.warning(f"不支持的平台: {platform}")
            return {"success": False, "message": "不支持的平台: {platform}"}

        if not os.path.exists(video_path):
            logger.warning(f"视频文件不存在: {video_path}")
            return {"success": False, "message": "视频文件不存在: {video_path}"}

        # 检查兼容性
        compatibility = self.check_video_compatibility(video_path, platform)

        # 如果已经兼容，可以直接返回
        if compatibility["compatible"]:
            logger.info(f"视频已经兼容 {platform} 平台，无需适配")
            return {
                "success": True,
                "adapted": False,
                "message": "视频已经兼容平台要求，无需适配",
                "original_path": video_path,
                "adapted_path": video_path,
                "compatibility": compatibility,
            }

        # 生成输出路径
        if output_path is None:
            filename = os.path.basename(video_path)
            base_name, ext = os.path.splitext(filename)
            platform_reqs = self.supported_platforms[platform]
            preferred_format = platform_reqs["supported_formats"][0]
            output_path = os.path.join(self.temp_dir, "{base_name}_{platform}_adapted.{preferred_format}")

        # 获取需要适配的问题
        issues = compatibility["issues"]
        video_info = compatibility["video_info"]
        platform_reqs = compatibility["platform_requirements"]

        # 模拟适配过程
        logger.info("开始为 {platform} 平台适配视频: {video_path}")
        logger.info("需要解决的问题: {issues}")

        # 实际应用中，这里会使用FFmpeg或其他工具进行视频转码和处理
        # 这里只是模拟适配过程

        # 创建一个空文件模拟适配结果
        with open(output_path, "w") as f:
            f.write("")

        # 返回适配结果
        return {
            "success": True,
            "adapted": True,
            "message": "视频已适配为 {platform} 平台要求",
            "original_path": video_path,
            "adapted_path": output_path,
            "original_info": video_info,
            "adaptation_details": {
                "issues_resolved": issues,
                "platform": platform,
                "platform_requirements": platform_reqs,
            },
        }

    def generate_metadata(self, video_info: Dict[str, Any], platform: str) -> Dict[str, Any]:
        """
        为特定平台生成元数据。

        Args:
            video_info: 视频信息字典，包含标题、描述、标签等
            platform: 目标平台

        Returns:
            平台特定的元数据字典
        """
        if platform not in self.supported_platforms:
            logger.warning(f"不支持的平台: {platform}")
            return {}

        logger.info("为 {platform} 平台生成元数据")

        # 获取基本信息
        title = video_info.get("title", "")
        description = video_info.get("description", "")
        tags = video_info.get("tags", [])

        # 根据平台生成特定的元数据
        metadata = {"platform": platform}

        if platform == "douyin":
            # 抖音元数据
            metadata["title"] = self._truncate_text(title, 80)  # 抖音标题限制
            metadata["description"] = self._truncate_text(description, 2000)  # 抖音描述限制
            metadata["tags"] = self._process_tags(tags, platform, max_tags=20)
            metadata["allow_comments"] = video_info.get("allow_comments", True)
            metadata["allow_duet"] = video_info.get("allow_duet", True)
            metadata["allow_stitch"] = video_info.get("allow_stitch", True)
            metadata["privacy_level"] = video_info.get("privacy_level", "public")

        elif platform == "kuaishou":
            # 快手元数据
            metadata["caption"] = self._truncate_text(title + "\n" + description, 1000)  # 快手标题和描述合并为caption
            metadata["topics"] = self._process_tags(tags, platform, max_tags=10)
            metadata["location"] = video_info.get("location", "")
            metadata["allow_comments"] = video_info.get("allow_comments", True)
            metadata["privacy_level"] = video_info.get("privacy_level", "public")

        elif platform == "bilibili":
            # B站元数据
            metadata["title"] = self._truncate_text(title, 80)  # B站标题限制
            metadata["description"] = self._truncate_text(description, 2000)  # B站描述限制
            metadata["tags"] = self._process_tags(tags, platform, max_tags=12)
            metadata["copyright"] = video_info.get("copyright", 1)  # 1表示原创，2表示转载
            metadata["source"] = video_info.get("source", "") if metadata["copyright"] == 2 else ""
            metadata["tid"] = video_info.get("category_id", 0)  # 分区ID
            metadata["cover"] = video_info.get("cover_path", "")  # 封面图片路径

        elif platform == "weibo":
            # 微博元数据
            metadata["text"] = self._truncate_text(title + "\n" + description, 2000)  # 微博正文
            metadata["visible"] = video_info.get("privacy_level", 0)  # 0公开，1私密，3仅关注可见
            metadata["topics"] = self._process_tags(tags, platform, prefix="#", suffix="#", max_tags=5)
            metadata["location"] = video_info.get("location", "")

        elif platform == "wechat":
            # 微信视频号元数据
            metadata["title"] = self._truncate_text(title, 100)
            metadata["allow_comments"] = video_info.get("allow_comments", True)
            metadata["cover"] = video_info.get("cover_path", "")

        elif platform == "xiaohongshu":
            # 小红书元数据
            metadata["title"] = self._truncate_text(title, 100)
            metadata["description"] = self._truncate_text(description, 1000)
            metadata["topics"] = self._process_tags(tags, platform, prefix="#", suffix="#", max_tags=20)
            metadata["location"] = video_info.get("location", "")
            metadata["cover"] = video_info.get("cover_path", "")

        elif platform == "youtube":
            # YouTube元数据
            metadata["title"] = self._truncate_text(title, 100)  # YouTube标题限制
            metadata["description"] = self._truncate_text(description, 5000)  # YouTube描述限制
            metadata["tags"] = self._process_tags(tags, platform, max_tags=500, max_tag_length=30)
            metadata["category_id"] = video_info.get("category_id", 22)  # 默认为"People & Blogs"
            metadata["privacy_status"] = video_info.get("privacy_level", "public")  # public, unlisted, private
            metadata["made_for_kids"] = video_info.get("made_for_kids", False)
            metadata["thumbnail"] = video_info.get("cover_path", "")
            metadata["playlist_ids"] = video_info.get("playlist_ids", [])

        # 添加通用元数据
        metadata["original_title"] = title
        metadata["original_description"] = description
        metadata["original_tags"] = tags
        metadata["generation_time"] = "模拟时间戳"  # 实际应用中使用真实时间戳

        logger.info("已为 {platform} 平台生成元数据")
        return metadata

    def _truncate_text(self, text: str, max_length: int) -> str:
        """
        截断文本到指定长度。

        Args:
            text: 原始文本
            max_length: 最大长度

        Returns:
            截断后的文本
        """
        if len(text) <= max_length:
            return text
        return text[: max_length - 3] + "..."

    def _process_tags(
        self,
        tags: List[str],
        platform: str,
        prefix: str = "",
        suffix: str = "",
        max_tags: int = 10,
        max_tag_length: int = 50,
    ) -> List[str]:
        """
        处理标签列表，适应平台要求。

        Args:
            tags: 原始标签列表
            platform: 目标平台
            prefix: 标签前缀
            suffix: 标签后缀
            max_tags: 最大标签数量
            max_tag_length: 单个标签最大长度

        Returns:
            处理后的标签列表
        """
        # 添加平台特定的标签
        platform_tags = {
            "douyin": ["抖音创作", "抖音视频"],
            "kuaishou": ["快手创作", "快手视频"],
            "bilibili": ["哔哩哔哩", "B站创作"],
            "weibo": ["微博视频"],
            "wechat": ["视频号"],
            "xiaohongshu": ["小红书", "种草"],
            "youtube": ["YouTube"],
        }

        # 合并标签
        all_tags = tags.copy()
        if platform in platform_tags:
            all_tags.extend(platform_tags[platform])

        # 去重
        all_tags = list(set(all_tags))

        # 截断标签数量
        all_tags = all_tags[:max_tags]

        # 处理每个标签
        processed_tags = []
        for tag in all_tags:
            # 截断标签长度
            if len(tag) > max_tag_length:
                tag = tag[:max_tag_length]

            # 添加前缀和后缀
            processed_tag = "{prefix}{tag}{suffix}"
            processed_tags.append(processed_tag)

        return processed_tags

    def generate_cover_image(self, video_path: str, output_path: Optional[str] = None) -> str:
        """
        从视频中生成封面图片。

        Args:
            video_path: 视频文件路径
            output_path: 输出图片路径，如果为None则自动生成

        Returns:
            生成的封面图片路径
        """
        if not os.path.exists(video_path):
            logger.warning("视频文件不存在: {video_path}")
            return ""

        # 生成输出路径
        if output_path is None:
            filename = os.path.basename(video_path)
            base_name = os.path.splitext(filename)[0]
            output_path = os.path.join(self.temp_dir, "{base_name}_cover.jpg")

        logger.info("从视频生成封面图片: {video_path} -> {output_path}")

        # 实际应用中，这里会使用FFmpeg提取视频的关键帧作为封面
        # 这里只是模拟生成过程

        # 创建一个空文件模拟封面图片
        with open(output_path, "w") as f:
            f.write("")

        logger.info("封面图片生成完成: {output_path}")
        return output_path

    def watermark_video(
        self, video_path: str, watermark_text: str, position: str = "bottom_right", output_path: Optional[str] = None
    ) -> str:
        """
        为视频添加水印。

        Args:
            video_path: 视频文件路径
            watermark_text: 水印文本
            position: 水印位置，可选 'top_left', 'top_right', 'bottom_left', 'bottom_right', 'center'
            output_path: 输出视频路径，如果为None则自动生成

        Returns:
            添加水印后的视频路径
        """
        if not os.path.exists(video_path):
            logger.warning("视频文件不存在: {video_path}")
            return ""

        # 生成输出路径
        if output_path is None:
            filename = os.path.basename(video_path)
            base_name, ext = os.path.splitext(filename)
            output_path = os.path.join(self.temp_dir, "{base_name}_watermarked{ext}")

        logger.info("为视频添加水印: {video_path} -> {output_path}")
        logger.info("水印文本: {watermark_text}, 位置: {position}")

        # 实际应用中，这里会使用FFmpeg添加水印
        # 这里只是模拟添加过程

        # 创建一个空文件模拟添加水印后的视频
        with open(output_path, "w") as f:
            f.write("")

        logger.info("水印添加完成: {output_path}")
        return output_path

    def split_video_for_platform(self, video_path: str, platform: str, output_dir: Optional[str] = None) -> List[str]:
        """
        将长视频分割为符合平台时长限制的多个片段。

        Args:
            video_path: 视频文件路径
            platform: 目标平台
            output_dir: 输出目录，如果为None则使用临时目录

        Returns:
            分割后的视频片段路径列表
        """
        if platform not in self.supported_platforms:
            logger.warning("不支持的平台: {platform}")
            return []

        if not os.path.exists(video_path):
            logger.warning("视频文件不存在: {video_path}")
            return []

        # 获取平台时长限制
        platform_reqs = self.supported_platforms[platform]
        max_duration = platform_reqs["max_duration"]

        # 获取视频信息
        video_info = self._get_video_info(video_path)
        duration = video_info["duration_seconds"]

        # 如果视频时长已经符合要求，直接返回
        if duration <= max_duration:
            logger.info("视频时长已符合 {platform} 平台要求，无需分割")
            return [video_path]

        # 设置输出目录
        if output_dir is None:
            output_dir = self.temp_dir

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 计算需要分割的片段数
        num_segments = (duration + max_duration - 1) // max_duration  # 向上取整

        logger.info("将视频分割为 {num_segments} 个片段，每个片段最长 {max_duration} 秒")

        # 生成分割后的视频路径
        filename = os.path.basename(video_path)
        base_name, ext = os.path.splitext(filename)

        # 实际应用中，这里会使用FFmpeg分割视频
        # 这里只是模拟分割过程
        segment_paths = []
        for i in range(num_segments):
            segment_path = os.path.join(output_dir, "{base_name}_part{i+1}{ext}")

            # 创建一个空文件模拟分割后的视频片段
            with open(segment_path, "w") as f:
                f.write("")

            segment_paths.append(segment_path)

        logger.info("视频分割完成，共 {len(segment_paths)} 个片段")
        return segment_paths
