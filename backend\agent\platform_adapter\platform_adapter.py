#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PlatformAdapter - 平台适配器
负责将生成的内容适配到不同社交媒体或视频平台的发布要求
"""

import asyncio
import json
import logging
import os
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class PlatformAdapter:
    """平台适配器：将内容适配到不同平台的发布要求"""

    def __init__(self, config_dir: Optional[str] = None, temp_dir: Optional[str] = None):
        """
        初始化平台适配器

        Args:
            config_dir: 配置文件目录
            temp_dir: 临时文件目录
        """
        logger.info("初始化 PlatformAdapter...")

        self.config_dir = config_dir or os.path.join(os.getcwd(), "config")
        self.temp_dir = temp_dir or os.path.join(os.getcwd(), "temp")

        # 创建必要的目录
        os.makedirs(self.config_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)

        # 支持的平台配置
        self.supported_platforms = {}
            "douyin": {}
                "display_name": "抖音",
                "max_duration": 180,  # 秒
                "preferred_aspect_ratios": ["9:16", "1:1"],
                "max_file_size": 500,  # MB
                "supported_formats": ["mp4"],
                "max_resolution": "1080p"},
            "kuaishou": {}
                "display_name": "快手",
                "max_duration": 57,  # 秒
                "preferred_aspect_ratios": ["9:16", "1:1"],
                "max_file_size": 500,  # MB
                "supported_formats": ["mp4"],
                "max_resolution": "1080p"},
            "bilibili": {}
                "display_name": "哔哩哔哩",
                "max_duration": 1800,  # 秒
                "preferred_aspect_ratios": ["16:9"],
                "max_file_size": 8000,  # MB
                "supported_formats": ["mp4", "flv"],
                "max_resolution": "4K"},
            "weibo": {}
                "display_name": "微博",
                "max_duration": 60,  # 秒
                "preferred_aspect_ratios": ["16:9", "1:1"],
                "max_file_size": 500,  # MB
                "supported_formats": ["mp4"],
                "max_resolution": "1080p"},
            "youtube": {}
                "display_name": "YouTube",
                "max_duration": 43200,  # 秒 (12小时)
                "preferred_aspect_ratios": ["16:9"],
                "max_file_size": 128000,  # MB (128GB)
                "supported_formats": ["mp4", "mov", "avi"],
                "max_resolution": "8K"}}

        self._load_platform_configs()
        logger.info(f"PlatformAdapter 初始化完成。支持的平台: {', f'.join(self.supported_platforms.keys())}")

    def _load_platform_configs(self):
        """加载平台配置文件"""
        config_path = os.path.join(self.config_dir, "platform_configs.json")
        if os.path.exists(config_path):
            try:
                with open(config_path, "r", encoding="utf-8") as f:
                    custom_configs = json.load(f)
                    for platform, config in custom_configs.items():
                        if platform in self.supported_platforms:
                            self.supported_platforms[platform].update(config)
                        else:
                            self.supported_platforms[platform] = config
                logger.info(f"已加载自定义平台配置: {config_path}")
            except Exception as e:
                logger.warning(f"加载平台配置失败: {e}")

        async def adapt_for_platform(self, video_path: str, platform: str) -> Dict[str, Any]:
        """
        为特定平台适配视频

        Args:
            video_path: 视频文件路径
            platform: 目标平台

        Returns:
            适配结果
        """
        logger.info(f"开始为 {platform} 平台适配视频: {video_path}")

        try:
            if platform not in self.supported_platforms:
                return {}
                    "status": "error",
                    "message": f"不支持的平台: {platform}"
                }

            if not os.path.exists(video_path):
                return {}
                    "status": "error",
                    "message": f"视频文件不存在: {video_path}"
                }

            # 检查视频兼容性
            compatibility = await self._check_video_compatibility(video_path, platform)

            if compatibility["compatible"]:
                logger.info(f"视频已经兼容 {platform} 平台，无需适配")
                return {}
                    "status": "success",
                    "message": "视频已经兼容平台要求",
                    "output_path": video_path,
                    "adapted": False
                }

            # 执行适配
            adapted_path = await self._perform_adaptation(video_path, platform, compatibility)

            return {}
                "status": "success",
                "message": f"视频已适配为 {platform} 平台要求",
                "output_path": adapted_path,
                "adapted": True,
                "issues_resolved": compatibility["issues"]
            }

        except Exception as e:
            logger.error(f"平台适配时发生错误: {e}")
            return {}
                "status": "error",
                "message": f"平台适配时发生错误: {str(e)}"
            }

        async def _check_video_compatibility(self, video_path: str, platform: str) -> Dict[str, Any]:
        """检查视频与平台的兼容性"""
        platform_reqs = self.supported_platforms[platform]
        video_info = await self._get_video_info(video_path)

        issues = []

        # 检查文件格式
        file_ext = os.path.splitext(video_path)[1][1:].lower()
        if file_ext not in platform_reqs["supported_formats"]:
            issues.append(f"文件格式不支持: {file_ext}")

        # 检查时长
        if video_info["duration_seconds"] > platform_reqs["max_duration"]:
            issues.append(f"视频时长超过限制: {video_info['duration_seconds']}秒")

        # 检查文件大小
        file_size_mb = os.path.getsize(video_path) / (1024 * 1024)
        if file_size_mb > platform_reqs["max_file_size"]:
            issues.append(f"文件大小超过限制: {file_size_mb:.2f}MB")

        # 检查分辨率和宽高比
        width, height = video_info["width"], video_info["height"]
        aspect_ratio = self._calculate_aspect_ratio(width, height)
        if aspect_ratio not in platform_reqs["preferred_aspect_ratios"]:
            issues.append(f"宽高比不是首选: {aspect_ratio}")

        return {}
            "compatible": len(issues) == 0,
            "issues": issues,
            "video_info": video_info,
            "platform_requirements": platform_reqs
        }

        async def _get_video_info(self, video_path: str) -> Dict[str, Any]:
        """获取视频信息"""
        # 模拟获取视频信息
        file_size = os.path.getsize(video_path)
        file_ext = os.path.splitext(video_path)[1][1:].lower()

        return {}
            "path": video_path,
            "format": file_ext,
            "duration_seconds": 45,  # 模拟45秒
            "width": 1920,
            "height": 1080,
            "fps": 30,
            "bitrate": "5000k",
            "file_size_bytes": file_size,
            "file_size_mb": file_size / (1024 * 1024)}

    def _calculate_aspect_ratio(self, width: int, height: int) -> str:
        """计算宽高比"""
        def gcd(a, b):
            while b:
                a, b = b, a % b
            return a

        g = gcd(width, height)
        ratio_w = width // g
        ratio_h = height // g

        # 标准宽高比映射
        standard_ratios = {}
            (16, 9): "16:9",
            (9, 16): "9:16",
            (4, 3): "4:3",
            (1, 1): "1:1",
            (21, 9): "21:9"
        }

        return standard_ratios.get((ratio_w, ratio_h), f"{ratio_w}:{ratio_h}")

        async def _perform_adaptation()
        self,:
        video_path: str,
        platform: str,
        compatibility: Dict[str, Any]
        ) -> str:
        """执行视频适配"""
        logger.info(f"执行 {platform} 平台适配")

        # 生成输出文件路径
        filename = os.path.basename(video_path)
        base_name, ext = os.path.splitext(filename)
        platform_reqs = self.supported_platforms[platform]
        preferred_format = platform_reqs["supported_formats"][0]
        output_path = os.path.join(self.temp_dir, f"{base_name}_{platform}_adapted.{preferred_format}")

        issues = compatibility["issues"]
        logger.info(f"需要解决的问题: {issues}")

        # 模拟适配过程
        logger.info("模拟视频适配过程...")
        for issue in issues:
            if "文件格式" in issue:
                logger.info("- 转换文件格式")
            elif "时长超过" in issue:
                logger.info("- 裁剪视频时长")
            elif "文件大小" in issue:
                logger.info("- 压缩视频文件")
            elif "宽高比" in issue:
                logger.info("- 调整视频宽高比")

        # 创建输出文件（模拟）
        with open(output_path, "w") as f:
            f.write("")

        logger.info(f"视频适配完成: {output_path}")
        return output_path

        async def generate_metadata(self, video_info: Dict[str, Any], platform: str) -> Dict[str, Any]:
        """
        为特定平台生成元数据

        Args:
            video_info: 视频信息
            platform: 目标平台

        Returns:
            平台特定的元数据
        """
        logger.info(f"为 {platform} 平台生成元数据")

        if platform not in self.supported_platforms:
            return {}

        title = video_info.get("title", "")
        description = video_info.get("description", "")
        tags = video_info.get("tags", [])

        metadata = {"platform": platform}

        if platform == "douyin":
            metadata.update({}
                "title": self._truncate_text(title, 80),
                "description": self._truncate_text(description, 2000),
                "tags": self._process_tags(tags, platform, max_tags=20),
                "allow_comments": video_info.get("allow_comments", True),
                "privacy_level": video_info.get("privacy_level", "public")
            })
        elif platform == "bilibili":
            metadata.update({}
                "title": self._truncate_text(title, 80),
                "description": self._truncate_text(description, 2000),
                "tags": self._process_tags(tags, platform, max_tags=12),
                "copyright": video_info.get("copyright", 1),
                "tid": video_info.get("category_id", 0)
            })
        elif platform == "youtube":
            metadata.update({}
                "title": self._truncate_text(title, 100),
                "description": self._truncate_text(description, 5000),
                "tags": self._process_tags(tags, platform, max_tags=500),
                "category_id": video_info.get("category_id", 22),
                "privacy_status": video_info.get("privacy_level", "public")
            })

        return metadata

    def _truncate_text(self, text: str, max_length: int) -> str:
        """截断文本到指定长度"""
        if len(text) <= max_length:
            return text
        return text[:max_length - 3] + "..."

    def _process_tags()
        self,:
        tags: List[str],
        platform: str,
        max_tags: int = 10,
        max_tag_length: int = 50
        ) -> List[str]:
        """处理标签列表"""
        # 添加平台特定标签
        platform_tags = {}
            "douyin": ["抖音创作"],
            "bilibili": ["哔哩哔哩"],
            "youtube": ["YouTube"]
        }

        all_tags = tags.copy()
        if platform in platform_tags:
            all_tags.extend(platform_tags[platform])

        # 去重并限制数量
        all_tags = list(set(all_tags))[:max_tags]

        # 限制标签长度
        processed_tags = []
        for tag in all_tags:
            if len(tag) > max_tag_length:
                tag = tag[:max_tag_length]
            processed_tags.append(tag)

        return processed_tags


        if __name__ == "__main__":
        async def test():
        adapter = PlatformAdapter()
        result = await adapter.adapt_for_platform("test_video.mp4", "douyin")
        print(f"测试结果: {result}")

        asyncio.run(test())
