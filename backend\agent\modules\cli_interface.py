#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
命令行界面模块
提供用户与系统交互的命令行接口
"""

import os
import sys
from typing import Any, Dict, List

# 添加项目根目录到路径
current_dir = os.path.dirname(__file__)
project_root = os.path.join(current_dir, "..", "..", "..")
sys.path.insert(0, os.path.abspath(project_root))

from backend.agent.core.coordinator import AgentCoordinator


class CLIInterface:
    """
    命令行界面类
    
    功能：
    1. 解析用户输入
    2. 调用协调器处理请求
    3. 格式化输出结果
    4. 提供交互式体验
    """

    def __init__(self):
        """初始化CLI界面"""
        self.coordinator = AgentCoordinator()
        self.running = True
        
        # 命令映射
        self.commands = {
            "analyze": self._cmd_analyze,
            "edit": self._cmd_edit,
            "publish": self._cmd_publish,
            "help": self._cmd_help,
            "status": self._cmd_status,
            "history": self._cmd_history,
            "exit": self._cmd_exit,
            "quit": self._cmd_exit
        }

    def run(self):
        """运行CLI界面"""
        self._show_welcome()
        
        while self.running:
            try:
                user_input = input("\n🎬 IntelliCut> ").strip()
                
                if not user_input:
                    continue
                
                self._process_command(user_input)
                
            except KeyboardInterrupt:
                print("\n\n👋 再见！")
                break
            except EOFError:
                print("\n\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")

    def _show_welcome(self):
        """显示欢迎信息"""
        print("=" * 60)
        print("🎬 欢迎使用 IntelliCutAgent 2.0 智能视频处理系统")
        print("=" * 60)
        print("💡 输入 'help' 查看可用命令")
        print("💡 输入 'exit' 或 'quit' 退出程序")
        print("💡 按 Ctrl+C 也可以退出")

    def _process_command(self, user_input: str):
        """处理用户命令"""
        parts = user_input.split()
        if not parts:
            return
        
        command = parts[0].lower()
        args = parts[1:] if len(parts) > 1 else []
        
        if command in self.commands:
            self.commands[command](args)
        else:
            print(f"❌ 未知命令: {command}")
            print("💡 输入 'help' 查看可用命令")

    def _cmd_analyze(self, args: List[str]):
        """分析视频命令"""
        if not args:
            print("❌ 请指定视频文件路径")
            print("💡 用法: analyze <video_path>")
            return
        
        video_path = args[0]
        
        print(f"🔍 开始分析视频: {video_path}")
        
        request = {
            "action": "analyze_video",
            "params": {"video_path": video_path}
        }
        
        result = self.coordinator.process_request(request)
        self._display_result(result)

    def _cmd_edit(self, args: List[str]):
        """编辑视频命令"""
        if not args:
            print("❌ 请指定视频文件路径")
            print("💡 用法: edit <video_path> [duration] [style]")
            return
        
        video_path = args[0]
        duration = int(args[1]) if len(args) > 1 and args[1].isdigit() else 60
        style = args[2] if len(args) > 2 else "standard"
        
        print(f"✂️ 开始编辑视频: {video_path}")
        print(f"📏 目标时长: {duration}秒")
        print(f"🎨 编辑风格: {style}")
        
        request = {
            "action": "edit_video",
            "params": {
                "video_path": video_path,
                "duration": duration,
                "style": style
            }
        }
        
        result = self.coordinator.process_request(request)
        self._display_result(result)

    def _cmd_publish(self, args: List[str]):
        """发布视频命令"""
        if not args:
            print("❌ 请指定视频文件路径")
            print("💡 用法: publish <video_path> [platform1] [platform2] ...")
            return
        
        video_path = args[0]
        platforms = args[1:] if len(args) > 1 else ["douyin"]
        
        print(f"📤 开始发布视频: {video_path}")
        print(f"🎯 目标平台: {', '.join(platforms)}")
        
        request = {
            "action": "publish_video",
            "params": {
                "video_path": video_path,
                "platforms": platforms
            }
        }
        
        result = self.coordinator.process_request(request)
        self._display_result(result)

    def _cmd_help(self, args: List[str]):
        """帮助命令"""
        if args and args[0] in self.commands:
            # 显示特定命令的帮助
            self._show_command_help(args[0])
        else:
            # 显示所有命令的帮助
            request = {"action": "help"}
            result = self.coordinator.process_request(request)
            
            if result["status"] == "success":
                help_info = result["result"]
                print("\n📖 IntelliCutAgent 帮助信息")
                print("=" * 40)
                print(f"系统名称: {help_info['system_name']}")
                print(f"版本: {help_info['version']}")
                print(f"描述: {help_info['description']}")
                
                print("\n🔧 可用命令:")
                print("  analyze <video_path>           - 分析视频内容")
                print("  edit <video_path> [duration]   - 编辑视频")
                print("  publish <video_path> [platforms] - 发布视频")
                print("  status                         - 显示系统状态")
                print("  history                        - 显示任务历史")
                print("  help [command]                 - 显示帮助信息")
                print("  exit/quit                      - 退出程序")
                
                print(f"\n🎯 支持的平台: {', '.join(help_info['supported_platforms'])}")
                print(f"📁 支持的格式: {', '.join(help_info['supported_formats'])}")
            else:
                print("❌ 获取帮助信息失败")

    def _cmd_status(self, args: List[str]):
        """状态命令"""
        request = {"action": "status"}
        result = self.coordinator.process_request(request)
        self._display_result(result)

    def _cmd_history(self, args: List[str]):
        """历史命令"""
        limit = int(args[0]) if args and args[0].isdigit() else 10
        history = self.coordinator.get_task_history(limit)
        
        if history:
            print(f"\n📋 最近 {len(history)} 个任务:")
            print("-" * 50)
            for i, task in enumerate(history, 1):
                print(f"{i:2d}. {task['action']} - {task.get('status', 'unknown')}")
                if task.get('params'):
                    print(f"     参数: {task['params']}")
        else:
            print("📋 暂无任务历史")

    def _cmd_exit(self, args: List[str]):
        """退出命令"""
        print("👋 再见！")
        self.running = False

    def _show_command_help(self, command: str):
        """显示特定命令的帮助"""
        help_texts = {
            "analyze": """
📖 analyze 命令帮助:
  用法: analyze <video_path>
  
  功能: 分析视频内容，包括基础信息、场景检测、质量评估等
  
  参数:
    video_path  - 视频文件路径（必需）
  
  示例:
    analyze video.mp4
    analyze /path/to/video.mp4
            """,
            "edit": """
📖 edit 命令帮助:
  用法: edit <video_path> [duration] [style]
  
  功能: 智能编辑视频，生成指定时长和风格的短视频
  
  参数:
    video_path  - 视频文件路径（必需）
    duration    - 目标时长，单位秒（可选，默认60）
    style       - 编辑风格（可选，默认standard）
  
  示例:
    edit video.mp4
    edit video.mp4 30
    edit video.mp4 45 dynamic
            """,
            "publish": """
📖 publish 命令帮助:
  用法: publish <video_path> [platform1] [platform2] ...
  
  功能: 发布视频到指定平台
  
  参数:
    video_path  - 视频文件路径（必需）
    platforms   - 目标平台列表（可选，默认douyin）
  
  支持的平台: douyin, bilibili, youtube, xiaohongshu
  
  示例:
    publish video.mp4
    publish video.mp4 douyin bilibili
    publish video.mp4 youtube
            """
        }
        
        help_text = help_texts.get(command, f"❌ 没有找到命令 '{command}' 的帮助信息")
        print(help_text)

    def _display_result(self, result: Dict[str, Any]):
        """显示处理结果"""
        if result["status"] == "success":
            print(f"✅ {result['message']}")
            
            if "result" in result:
                self._display_result_details(result["result"])
        else:
            print(f"❌ {result['message']}")

    def _display_result_details(self, result_data: Dict[str, Any]):
        """显示结果详情"""
        if "basic_info" in result_data:
            # 视频分析结果
            basic = result_data["basic_info"]
            print(f"📊 基础信息:")
            print(f"   时长: {basic.get('duration', 'unknown')}秒")
            print(f"   分辨率: {basic.get('resolution', 'unknown')}")
            print(f"   帧率: {basic.get('fps', 'unknown')}fps")
            print(f"   文件大小: {basic.get('file_size', 'unknown')}")
            
            if "content_analysis" in result_data:
                content = result_data["content_analysis"]
                if "scenes" in content:
                    print(f"🎬 场景分析: 检测到 {len(content['scenes'])} 个场景")
                if "highlights" in content:
                    print(f"⭐ 精彩片段: 发现 {len(content['highlights'])} 个亮点")
            
            if "quality_assessment" in result_data:
                quality = result_data["quality_assessment"]
                print(f"🏆 质量评分: {quality.get('overall_score', 0):.2f}")
        
        elif "output_path" in result_data:
            # 视频编辑结果
            print(f"📁 输出文件: {result_data['output_path']}")
            if "processing_info" in result_data:
                info = result_data["processing_info"]
                print(f"⚡ 处理信息: 选择了 {info.get('segments_selected', 0)} 个片段")
        
        elif "publish_results" in result_data:
            # 发布结果
            results = result_data["publish_results"]
            print(f"📤 发布结果:")
            for platform, info in results.items():
                status_icon = "✅" if info["status"] == "success" else "❌"
                print(f"   {status_icon} {platform}: {info['message']}")
                if info.get("url"):
                    print(f"      链接: {info['url']}")
        
        elif "system_status" in result_data:
            # 系统状态
            print(f"🖥️  系统状态: {result_data['system_status']}")
            print(f"📦 已加载模块: {result_data['modules_loaded']}")
            print(f"✅ 完成任务数: {result_data['tasks_completed']}")


def main():
    """主函数"""
    cli = CLIInterface()
    cli.run()


if __name__ == "__main__":
    main()
