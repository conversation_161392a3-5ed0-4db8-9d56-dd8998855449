#!/usr/bin/env python3
"""
紧急语法修复脚本 - 修复E999缩进错误
"""

import subprocess
from pathlib import Path


def fix_indentation_errors():
    """修复所有E999缩进错误"""
    print("🚨 紧急修复E999缩进错误...")
    
    # 获取所有有E999错误的文件
    result = subprocess.run([
        "python", "-m", "flake8", 
        "--select=E999",
        "--max-line-length=120", 
        "--exclude=venv,__pycache__,.git",
        "."
    ], capture_output=True, text=True)
    
    error_files = set()
    for line in result.stdout.strip().split('\n'):
        if line and ':' in line:
            file_path = line.split(':')[0]
            error_files.add(file_path)
    
    print(f"发现 {len(error_files)} 个文件有缩进错误")
    
    fixed_count = 0
    for file_path in error_files:
        try:
            path_obj = Path(file_path)
            if not path_obj.exists():
                continue
                
            content = path_obj.read_text(encoding='utf-8')
            lines = content.split('\n')
            
            # 修复缩进问题
            new_lines = []
            for i, line in enumerate(lines):
                # 如果第一行就有缩进错误，去掉开头的空格
                if i == 0 and line.startswith('    '):
                    new_lines.append(line.lstrip())
                # 如果是导入语句开头有错误缩进
                elif line.strip().startswith(('import ', 'from ')) and line.startswith('    '):
                    new_lines.append(line.lstrip())
                # 其他情况保持原样
                else:
                    new_lines.append(line)
            
            new_content = '\n'.join(new_lines)
            if new_content != content:
                path_obj.write_text(new_content, encoding='utf-8')
                fixed_count += 1
                print(f"  ✅ 修复了 {file_path}")
                
        except Exception as e:
            print(f"  ❌ 修复 {file_path} 失败: {e}")
    
    print(f"✅ 修复了 {fixed_count} 个文件的缩进错误")
    return fixed_count

def fix_syntax_errors():
    """修复其他语法错误"""
    print("🔧 修复其他语法错误...")
    
    # 修复test_intelli_cut_agent.py中的语法错误
    test_file = Path("test_intelli_cut_agent.py")
    if test_file.exists():
        try:
            content = test_file.read_text(encoding='utf-8')
            # 修复第40行的语法错误
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'expected' in line and '(' in line:
                    # 可能是函数调用语法错误，尝试修复
                    if line.strip().endswith('expected'):
                        lines[i] = line + '()'
            
            new_content = '\n'.join(lines)
            if new_content != content:
                test_file.write_text(new_content, encoding='utf-8')
                print("  ✅ 修复了 test_intelli_cut_agent.py")
        except Exception as e:
            print(f"  ❌ 修复 test_intelli_cut_agent.py 失败: {e}")

def run_basic_formatting():
    """运行基础格式化"""
    print("🎨 运行基础格式化...")
    
    try:
        # 只运行autopep8进行基础修复
        result = subprocess.run([
            "python", "-m", "autopep8", 
            "--in-place",
            "--recursive",
            "--max-line-length=120",
            "."
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ autopep8格式化完成")
        else:
            print("⚠️ autopep8执行有警告")
            
    except Exception as e:
        print(f"❌ autopep8失败: {e}")

def get_final_count():
    """获取最终问题数量"""
    try:
        result = subprocess.run([
            "python", "-m", "flake8", 
            "--count",
            "--max-line-length=120", 
            "--exclude=venv,__pycache__,.git",
            "."
        ], capture_output=True, text=True)
        
        output = result.stdout.strip()
        lines = output.split('\n')
        
        # 获取最后一行的数字
        for line in reversed(lines):
            if line.strip().isdigit():
                return int(line.strip())
        
        return 0
    except:
        return -1

def main():
    """主函数"""
    print("🚨 紧急语法修复脚本")
    print("=" * 50)
    
    # 获取初始错误数
    initial_count = get_final_count()
    print(f"📊 初始问题数: {initial_count}")
    
    # 执行修复
    print("\n🔧 开始紧急修复...")
    
    # 1. 修复缩进错误
    fix_indentation_errors()
    
    # 2. 修复语法错误
    fix_syntax_errors()
    
    # 3. 基础格式化
    run_basic_formatting()
    
    # 最终检查
    print("\n📊 修复结果:")
    final_count = get_final_count()
    
    if final_count >= 0:
        print(f"修复前: {initial_count} 个问题")
        print(f"修复后: {final_count} 个问题")
        
        if initial_count > 0:
            reduction = initial_count - final_count
            reduction_percent = (reduction / initial_count * 100)
            print(f"修复了: {reduction} 个问题 ({reduction_percent:.1f}%)")
        
        if final_count == 0:
            print("🎉 完美! 所有问题已解决!")
        elif final_count < 10:
            print("🎊 优秀! 接近完美!")
        elif final_count < 50:
            print("👍 良好! 大部分问题已解决!")
        else:
            print("⚠️ 仍需继续修复")
    else:
        print("❌ 无法获取准确统计")

if __name__ == "__main__":
    main()
