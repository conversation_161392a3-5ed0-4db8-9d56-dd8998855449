#!/usr/bin/env python3
"""
完美修复脚本 - 彻底解决剩余的152个问题
"""

import re
import subprocess
from pathlib import Path


def get_specific_issues():
    """获取具体的问题列表"""
    try:
        result = subprocess.run([
            "python", "-m", "flake8",
            "--max-line-length=120",
            "--exclude=venv,__pycache__,.git",
            "."
        ], capture_output=True, text=True)
        
        issues = []
        for line in result.stdout.strip().split('\n'):
            if line and ':' in line:
                issues.append(line)
        return issues
    except:
        return []

def fix_specific_issues():
    """修复具体的问题"""
    print("🔧 修复具体问题...")
    
    issues = get_specific_issues()
    print(f"发现 {len(issues)} 个具体问题")
    
    # 按文件分组
    file_issues = {}
    for issue in issues:
        parts = issue.split(':')
        if len(parts) >= 4:
            file_path = parts[0]
            line_num = int(parts[1])
            error_code = parts[3].strip().split()[0]
            
            if file_path not in file_issues:
                file_issues[file_path] = []
            file_issues[file_path].append((line_num, error_code, issue))
    
    fixed_count = 0
    
    for file_path, issues_list in file_issues.items():
        try:
            path_obj = Path(file_path)
            if not path_obj.exists():
                continue
                
            content = path_obj.read_text(encoding='utf-8')
            lines = content.split('\n')
            modified = False
            
            # 按行号倒序处理
            for line_num, error_code, full_issue in sorted(issues_list, reverse=True):
                if 1 <= line_num <= len(lines):
                    line = lines[line_num - 1]
                    
                    # 根据错误类型修复
                    if error_code == 'E302':
                        # 在函数/类定义前添加2个空行
                        if line.strip().startswith(('def ', 'class ', 'async def ')):
                            # 检查前面是否已有足够空行
                            empty_lines = 0
                            for i in range(line_num - 2, -1, -1):
                                if lines[i].strip() == '':
                                    empty_lines += 1
                                else:
                                    break
                            
                            if empty_lines < 2:
                                lines.insert(line_num - 1, '')
                                modified = True
                    
                    elif error_code == 'E305':
                        # 在函数/类定义后添加2个空行
                        if line_num < len(lines):
                            next_line = lines[line_num] if line_num < len(lines) else ''
                            if next_line.strip() and not next_line.startswith(' '):
                                lines.insert(line_num, '')
                                modified = True
                    
                    elif error_code == 'F841':
                        # 删除未使用的变量
                        if ' = ' in line and not line.strip().startswith('#'):
                            var_name = line.split(' = ')[0].strip()
                            if var_name not in ['_', '__']:
                                lines[line_num - 1] = line.replace(f'{var_name} =', '_ =')
                                modified = True
                    
                    elif error_code == 'E501':
                        # 修复超长行
                        if len(line) > 120:
                            # 简单的换行处理
                            if ', ' in line and len(line) > 120:
                                parts = line.split(', ')
                                if len(parts) > 2:
                                    indent = len(line) - len(line.lstrip())
                                    new_lines = [parts[0] + ',']
                                    for part in parts[1:-1]:
                                        new_lines.append(' ' * (indent + 4) + part + ',')
                                    new_lines.append(' ' * (indent + 4) + parts[-1])
                                    
                                    lines[line_num - 1:line_num] = new_lines
                                    modified = True
                    
                    elif error_code in ['W291', 'W293']:
                        # 删除行尾空白
                        lines[line_num - 1] = line.rstrip()
                        modified = True
                    
                    elif error_code == 'E203':
                        # 修复冒号前的空白
                        lines[line_num - 1] = re.sub(r'\s+:', ':', line)
                        modified = True
                    
                    elif error_code == 'E231':
                        # 逗号后添加空格
                        lines[line_num - 1] = re.sub(r',([^\s])', r', \1', line)
                        modified = True
            
            if modified:
                new_content = '\n'.join(lines)
                path_obj.write_text(new_content, encoding='utf-8')
                fixed_count += 1
                print(f"  ✅ 修复了 {file_path}")
                
        except Exception as e:
            print(f"  ❌ 修复 {file_path} 失败: {e}")
    
    print(f"✅ 修复了 {fixed_count} 个文件")

def run_final_cleanup():
    """运行最终清理"""
    print("🧹 最终清理...")
    
    # 再次运行autoflake
    try:
        subprocess.run([
            "autoflake",
            "--remove-all-unused-imports",
            "--remove-unused-variables", 
            "--in-place",
            "--recursive",
            "--exclude=venv",
            "."
        ], check=False, capture_output=True)
        print("✅ autoflake完成")
    except:
        print("⚠️ autoflake失败")
    
    # 再次运行isort
    try:
        subprocess.run([
            "isort",
            "--profile", "black",
            "--line-length", "120",
            "--skip=venv",
            "."
        ], check=False, capture_output=True)
        print("✅ isort完成")
    except:
        print("⚠️ isort失败")

def get_final_count():
    """获取最终问题数量"""
    try:
        result = subprocess.run([
            "python", "-m", "flake8",
            "--count",
            "--max-line-length=120",
            "--exclude=venv,__pycache__,.git",
            "."
        ], capture_output=True, text=True)
        
        output = result.stdout.strip()
        if output:
            lines = output.split('\n')
            for line in reversed(lines):
                if line.strip().isdigit():
                    return int(line.strip())
        return 0
    except:
        return -1

def main():
    """主函数"""
    print("🎯 完美修复脚本")
    print("=" * 50)
    print("目标: 彻底解决所有剩余问题")
    
    # 获取初始问题数
    initial_count = get_final_count()
    print(f"📊 初始问题数: {initial_count}")
    
    # 执行修复
    print("\n🔧 开始完美修复...")
    
    # 多轮修复
    for round_num in range(3):
        print(f"\n📋 第 {round_num + 1} 轮修复:")
        
        # 修复具体问题
        fix_specific_issues()
        
        # 运行清理工具
        run_final_cleanup()
        
        # 检查进度
        current_count = get_final_count()
        print(f"当前问题数: {current_count}")
        
        if current_count == 0:
            print("🎉 完美! 所有问题已解决!")
            break
        elif current_count >= initial_count:
            print("⚠️ 没有进一步改善，停止修复")
            break
        else:
            initial_count = current_count
    
    # 最终统计
    print("\n" + "=" * 50)
    print("📊 最终修复结果")
    print("=" * 50)
    
    final_count = get_final_count()
    
    if final_count >= 0:
        total_reduction = 635 - final_count
        total_percent = (total_reduction / 635 * 100)
        
        print(f"🎯 最终问题数: {final_count}")
        print(f"📈 总体修复率: {total_percent:.1f}% (635 → {final_count})")
        
        if final_count == 0:
            print("🎉 完美! 达到A级代码质量标准!")
            print("🏆 项目现在是零问题的完美代码库!")
        elif final_count < 10:
            print("🎊 优秀! 接近完美的代码质量!")
        elif final_count < 50:
            print("👍 良好! 代码质量显著提升!")
        else:
            print("⚠️ 仍需继续优化")
            
        # 显示剩余问题
        if 0 < final_count <= 20:
            print("\n📋 剩余问题:")
            issues = get_specific_issues()
            for issue in issues[:10]:
                print(f"  {issue}")
            if len(issues) > 10:
                print(f"  ... 还有 {len(issues) - 10} 个问题")
    else:
        print("❌ 无法获取准确统计")

if __name__ == "__main__":
    main()
