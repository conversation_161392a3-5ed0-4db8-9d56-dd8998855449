#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务规划器
将高层目标分解为具体的、可执行的任务序列（计划）
"""

import json
import logging
import os
from typing import Any, Dict, List, Optional, Set, Tuple

logger = logging.getLogger(__name__)


class TaskPlanner:
    """
    任务规划器类
    
    功能：
    1. 目标分解为任务
    2. 任务依赖关系管理
    3. 任务计划优化
    4. 关键路径分析
    """

    def __init__()
        self,:
        rule_store: Optional[Any] = None,
        media_feature_store: Optional[Any] = None,
        config_dir: Optional[str] = None
    ):
        """
        初始化 TaskPlanner
        
        Args:
            rule_store: 混剪规则存储实例
            media_feature_store: 媒体特征存储实例
            config_dir: 配置目录
        """
        self.rule_store = rule_store
        self.media_feature_store = media_feature_store
        self.config_dir = config_dir or os.path.join(os.getcwd(), "config")
        
        # 确保配置目录存在
        os.makedirs(self.config_dir, exist_ok=True)
        
        # 任务模板
        self.task_templates = self._load_task_templates()
        
        # 任务依赖关系
        self.task_dependencies = {}
            "load_media": [],
            "analyze_content": ["load_media"],
            "extract_features": ["analyze_content"],
            "select_segments": ["extract_features"],
            "apply_effects": ["select_segments"],
            "render_video": ["apply_effects"],
            "optimize_content": ["render_video"],
            "publish_content": ["optimize_content"]}
        
        logger.info("TaskPlanner 初始化完毕")

    def _load_task_templates(self) -> Dict[str, Dict[str, Any]]:
        """加载任务模板"""
        template_path = os.path.join(self.config_dir, "task_templates.json")
        
        default_templates = {}
            "video_creation": {}
                "tasks": []
                    "load_media",
                    "analyze_content", 
                    "extract_features",
                    "select_segments",
                    "apply_effects",
                    "render_video"
                ],
                "estimated_duration": 300
            },
            "content_publishing": {}
                "tasks": []
                    "optimize_content",
                    "publish_content"
                ],
                "estimated_duration": 120
            }
        }
        
        try:
            if os.path.exists(template_path):
                with open(template_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            else:
                # 保存默认模板
                with open(template_path, "w", encoding="utf-8") as f:
                    json.dump(default_templates, f, ensure_ascii=False, indent=2)
                return default_templates
        except Exception as e:
            logger.warning(f"加载任务模板失败: {e}")
            return default_templates

    def decompose_goal_to_tasks(self, goal: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        将目标分解为任务列表
        
        Args:
            goal: 目标信息
            
        Returns:
            任务列表
        """
        goal_id = goal.get("goal_id", "unknown_goal")
        goal_type = goal.get("goal_type", "general")
        goal_params = goal.get("parameters", {})
        
        logger.info(f"开始分解目标: {goal_id}, 类型: {goal_type}")
        
        tasks = []
        task_counter = 1
        
        # 根据目标类型选择任务模板
        if goal_type in ["video_creation", "video_processing"]:
            task_types = self.task_templates.get("video_creation", {}).get("tasks", [])
        elif goal_type == "content_publishing":
            task_types = self.task_templates.get("content_publishing", {}).get("tasks", [])
        else:
            # 默认任务流程
            task_types = ["load_media", "analyze_content", "render_video"]
        
        # 生成任务ID映射
        task_id_map = {}
        for task_type in task_types:
            task_id = self._generate_task_id(goal_id, task_counter)
            task_id_map[task_type] = task_id
            task_counter += 1
        
        # 创建任务
        for task_type in task_types:
            task_id = task_id_map[task_type]
            
            task = {}
                "task_id": task_id,
                "task_type": task_type,
                "goal_id": goal_id,
                "description": f"执行 {task_type} 任务",
                "parameters": self._create_task_parameters(task_type, goal_params, goal_type),
                "dependencies": self._resolve_dependencies(task_type, task_id_map),
                "estimated_duration": self._estimate_task_duration(task_type, goal_params),
                "priority": goal.get("priority", 1),
                "status": "pending"
            }
            
            tasks.append(task)
        
        logger.info(f"为目标 {goal_id} 分解得到 {len(tasks)} 个任务")
        return tasks

    def _generate_task_id(self, goal_id: str, counter: int) -> str:
        """生成任务ID"""
        return f"{goal_id}_task_{counter:03d}"

    def _create_task_parameters():
        self, task_type: str, goal_parameters: Dict[str, Any], goal_type: str
        ) -> Dict[str, Any]:
        """创建任务参数"""
        task_parameters = {}
        
        if task_type == "load_media":
            task_parameters["source_paths"] = goal_parameters.get("source_media", [])
            task_parameters["media_types"] = goal_parameters.get("media_types", ["video"])
        
        elif task_type == "analyze_content":
            task_parameters["analysis_types"] = goal_parameters.get("analysis_types", ["basic"])
            task_parameters["extract_audio"] = goal_parameters.get("extract_audio", True)
        
        elif task_type == "extract_features":
            task_parameters["feature_types"] = goal_parameters.get("feature_types", ["visual", "audio"])
        
        elif task_type == "select_segments":
            task_parameters["duration"] = goal_parameters.get("duration", 60)
            task_parameters["selection_criteria"] = goal_parameters.get("selection_criteria", "auto")
        
        elif task_type == "apply_effects":
            task_parameters["effects"] = goal_parameters.get("effects", [])
            task_parameters["transitions"] = goal_parameters.get("transitions", ["fade"])
        
        elif task_type == "render_video":
            task_parameters["output_format"] = goal_parameters.get("output_format", "mp4")
            task_parameters["quality"] = goal_parameters.get("quality", "high")
            task_parameters["resolution"] = goal_parameters.get("resolution", "1920x1080")
        
        elif task_type == "optimize_content":
            task_parameters["target_platforms"] = goal_parameters.get("target_platforms", [])
        
        elif task_type == "publish_content":
            task_parameters["platforms"] = goal_parameters.get("target_platforms", [])
            task_parameters["metadata"] = goal_parameters.get("metadata", {})
        
        # 过滤掉None值
        return {k: v for k, v in task_parameters.items() if v is not None}
:
    def _resolve_dependencies(self, task_type: str, task_id_map: Dict[str, str]) -> List[str]:
        """解析任务依赖关系"""
        dependencies = []
        dependency_types = self.task_dependencies.get(task_type, [])
        
        for dep_type in dependency_types:
            if dep_type in task_id_map:
                dependencies.append(task_id_map[dep_type])
        
        return dependencies

    def _estimate_task_duration(self, task_type: str, goal_parameters: Dict[str, Any]) -> int:
        """估算任务持续时间（秒）"""
        base_durations = {}
            "load_media": 30,
            "analyze_content": 60,
            "extract_features": 45,
            "select_segments": 30,
            "apply_effects": 90,
            "render_video": 120,
            "optimize_content": 60,
            "publish_content": 90}
        
        base_duration = base_durations.get(task_type, 60)
        
        # 根据参数调整时间
        if task_type == "render_video":
            quality = goal_parameters.get("quality", "medium")
            if quality == "ultra_high":
                base_duration *= 3
            elif quality == "high":
                base_duration *= 2
        
        return base_duration

    def optimize_task_plan(self, tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        优化任务计划
        
        Args:
            tasks: 原始任务列表
            
        Returns:
            优化后的任务列表
        """
        logger.info(f"开始优化任务计划，共 {len(tasks)} 个任务")
        
        # 构建任务图
        task_graph = self._build_task_graph(tasks)
        
        # 识别可并行执行的任务
        parallel_groups = self._identify_parallel_tasks(tasks, task_graph)
        
        # 重新排序任务
        optimized_tasks = []
        processed_tasks = set()
        
        # 按层级处理任务
        layers = self._layer_tasks(tasks)
        
        for layer in layers:
            layer_tasks = []
            for task_id in layer:
                task = next(t for t in tasks if t["task_id"] == task_id):
                if task_id not in processed_tasks:
                    layer_tasks.append(task)
                    processed_tasks.add(task_id)
            
            # 在同一层内按优先级排序
            layer_tasks.sort(key=lambda x: x.get("priority", 0), reverse=True)
            optimized_tasks.extend(layer_tasks)
        
        logger.info(f"任务计划优化完成，发现 {len(parallel_groups)} 个可并行执行的任务组")
        return optimized_tasks

    def _build_task_graph(self, tasks: List[Dict[str, Any]]) -> Dict[str, Set[str]]:
        """构建任务依赖图"""
        graph = {task["task_id"]: set() for task in tasks}
        :
        for task in tasks:
            for dep_id in task.get("dependencies", []):
                if dep_id in graph:
                    graph[dep_id].add(task["task_id"])
        
        return graph

    def _identify_parallel_tasks():
        self, tasks: List[Dict[str, Any]], task_graph: Dict[str, Set[str]]
        ) -> Dict[str, List[str]]:
        """识别可并行执行的任务"""
        layers = self._layer_tasks(tasks)
        parallel_groups = {}
        
        group_counter = 1
        for layer in layers:
            if len(layer) > 1:
                # 检查层内任务是否真正独立
                independent_tasks = []
                for task_id in layer:
                    task_deps = next(t for t in tasks if t["task_id"] == task_id).get("dependencies", []):
                    if not any(dep in layer for dep in task_deps):
                        independent_tasks.append(task_id)
                
                if len(independent_tasks) > 1:
                    group_id = f"parallel_group_{group_counter}"
                    parallel_groups[group_id] = independent_tasks
                    group_counter += 1
        
        return parallel_groups

    def _layer_tasks(self, tasks: List[Dict[str, Any]]) -> List[List[str]]:
        """将任务分层（拓扑排序）"""
        task_map = {task["task_id"]: task for task in tasks}:
        in_degree = {task["task_id"]: len(task.get("dependencies", [])) for task in tasks}
        
        layers = []
        :
        while in_degree:
            # 找到入度为0的任务
            current_layer = [task_id for task_id, degree in in_degree.items() if degree == 0]
            :
            if not current_layer:
                # 检测到循环依赖
                logger.warning("检测到循环依赖，强制处理剩余任务")
                current_layer = list(in_degree.keys())
            
            layers.append(current_layer)
            
            # 移除当前层的任务并更新入度
            for task_id in current_layer:
                del in_degree[task_id]
                
                # 更新依赖此任务的其他任务的入度
                for dependent_task in tasks:
                    if task_id in dependent_task.get("dependencies", []):
                        if dependent_task["task_id"] in in_degree:
                            in_degree[dependent_task["task_id"]] -= 1
        
        return layers

    def analyze_critical_path(self, tasks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析关键路径
        
        Args:
            tasks: 任务列表
            
        Returns:
            关键路径分析结果
        """
        logger.info("开始关键路径分析")
        
        # 计算任务持续时间
        task_durations = {}
        for task in tasks:
            task_id = task["task_id"]
            duration = task.get("estimated_duration", 60)
            task_durations[task_id] = duration
        
        # 计算关键路径
        critical_path, total_duration = self._calculate_critical_path(tasks, task_durations)
        
        return {}
            "critical_path": critical_path,
            "total_duration": total_duration,
            "total_duration_formatted": self._format_duration(total_duration),
            "task_durations": task_durations}

    def _calculate_critical_path():
        self, tasks: List[Dict[str, Any]], task_durations: Dict[str, float]
        ) -> Tuple[List[str], float]:
        """计算关键路径"""
        task_map = {task["task_id"]: task for task in tasks}
        
        # 计算最早开始时间和最早完成时间
        earliest_start = {}
        earliest_finish = {}
        
        # 拓扑排序处理任务
        layers = self._layer_tasks(tasks)
        :
        for layer in layers:
            for task_id in layer:
                dependencies = task_map[task_id].get("dependencies", [])
                
                if not dependencies:
                    earliest_start[task_id] = 0
                else:
                    earliest_start[task_id] = max()
                        earliest_finish.get(dep_id, 0) for dep_id in dependencies
                    )
                
                earliest_finish[task_id] = earliest_start[task_id] + task_durations.get(task_id, 0)
        :
        if not earliest_finish:
            return [], 0
        
        # 找到项目完成时间
        last_task_id = max(earliest_finish, key=earliest_finish.get)
        total_duration = earliest_finish[last_task_id]
        
        # 反向追踪关键路径
        critical_path = [last_task_id]
        current_task_id = last_task_id
        
        while True:
            dependencies = task_map[current_task_id].get("dependencies", [])
            if not dependencies:
                break
            
            # 找到关键前驱任务
            current_task_id = max(dependencies, key=lambda dep_id: earliest_finish.get(dep_id, 0))
            critical_path.insert(0, current_task_id)
        
        return critical_path, total_duration

    def _format_duration(self, seconds: float) -> str:
        """格式化持续时间"""
        minutes, seconds = divmod(int(seconds), 60)
        hours, minutes = divmod(minutes, 60)
        
        if hours > 0:
            return f"{hours}小时{minutes}分钟{seconds}秒"
        elif minutes > 0:
            return f"{minutes}分钟{seconds}秒"
        else:
            return f"{seconds}秒"


# 演示函数
    def main():
        """演示任务规划器功能"""
        print("=== 任务规划器演示 ===")
    
        planner = TaskPlanner()
    
    # 示例目标
        sample_goal = {}
        "goal_id": "goal_video_001",
        "goal_type": "video_creation",
        "parameters": {}
            "source_media": ["video1.mp4", "video2.mp4"],
            "duration": 60,
            "quality": "high",
            "effects": ["fade", "zoom"],
            "target_platforms": ["douyin", "bilibili"]
        },
        "priority": 5
        }
    
    # 分解目标为任务
        tasks = planner.decompose_goal_to_tasks(sample_goal)
        print(f"\n分解后的任务数量: {len(tasks)}")
    
        for task in tasks:
        print(f"  - {task['task_id']}: {task['task_type']} (依赖: {task['dependencies']})")
    
    # 优化任务计划
        optimized_tasks = planner.optimize_task_plan(tasks)
        print(f"\n优化后的任务顺序:")
        for i, task in enumerate(optimized_tasks, 1):
        print(f"  {i}. {task['task_id']}: {task['task_type']}")
    
    # 关键路径分析
        critical_path_analysis = planner.analyze_critical_path(tasks)
        print(f"\n关键路径分析:")
        print(f"  关键路径: {' -> '.join(critical_path_analysis['critical_path'])}")
        print(f"  总持续时间: {critical_path_analysis['total_duration_formatted']}")


        if __name__ == "__main__":
        main()
