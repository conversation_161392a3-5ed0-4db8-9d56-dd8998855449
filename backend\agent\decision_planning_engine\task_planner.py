# backend.agent.decision_planning_engine.task_planner

import datetime
import json
import logging
import os
from typing import Any
from typing import Dict
from typing import List
from typing import Optional
from typing import Set
from typing import Tuple

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

class TaskPlanner:
    """将高层目标分解为具体的、可执行的任务序列（计划）。"""

    def __init__(
        self, rule_store: Optional[Any] = None, media_feature_store: Optional[Any] = None, config_dir: str = None
    ):
        """
        初始化 TaskPlanner。

        Args:
            rule_store: 混剪规则存储实例 (可选, 用于指导任务分解)。
            media_feature_store: 媒体特征存储实例 (可选, 用于获取媒体信息以辅助规划)。
            config_dir: 配置文件目录，默认为当前目录下的 'config'
        """
        self.rule_store = rule_store
        self.media_feature_store = media_feature_store
        self.config_dir = config_dir or os.path.join(os.getcwd(), "config")

        # 确保目录存在
        os.makedirs(self.config_dir, exist_ok=True)

        # 加载任务模板
        self.task_templates = self._load_task_templates()

        # 任务类型及其依赖关系
        self.task_dependencies = {
            "load_media": [],
            "analyze_media_content": ["load_media"],
            "select_clips_for_timeline": ["analyze_media_content"],
            "assemble_video_timeline": ["select_clips_for_timeline"],
            "add_effects": ["assemble_video_timeline"],
            "add_music": ["assemble_video_timeline"],
            "add_subtitles": ["assemble_video_timeline"],
            "render_final_video": ["assemble_video_timeline", "add_effects", "add_music", "add_subtitles"],
            "publish_video": ["render_final_video"],
            "prepare_training_data": [],
            "train_model": ["prepare_training_data"],
            "evaluate_model": ["train_model"],
            "deploy_model": ["evaluate_model"],
        }

        logger.info("TaskPlanner 初始化完毕。")

    def _load_task_templates(self) -> Dict[str, Dict[str, Any]]:
        """
        加载任务模板。

        Returns:
            任务模板字典
        """
        template_path = os.path.join(self.config_dir, "task_templates.json")

        # 默认模板
        default_templates = {
            "video_creation": {
                "tasks": [
                    {"task_type": "load_media", "required": True},
                    {"task_type": "analyze_media_content", "required": False},
                    {"task_type": "select_clips_for_timeline", "required": True},
                    {"task_type": "assemble_video_timeline", "required": True},
                    {"task_type": "add_effects", "required": False},
                    {"task_type": "add_music", "required": False},
                    {"task_type": "add_subtitles", "required": False},
                    {"task_type": "render_final_video", "required": True},
                ]
            },
            "video_processing": {
                "tasks": [
                    {"task_type": "load_media", "required": True},
                    {"task_type": "analyze_media_content", "required": False},
                    {"task_type": "select_clips_for_timeline", "required": True},
                    {"task_type": "assemble_video_timeline", "required": True},
                    {"task_type": "add_effects", "required": False},
                    {"task_type": "add_music", "required": False},
                    {"task_type": "add_subtitles", "required": False},
                    {"task_type": "render_final_video", "required": True},
                ]
            },
            "model_retraining": {
                "tasks": [
                    {"task_type": "prepare_training_data", "required": True},
                    {"task_type": "train_model", "required": True},
                    {"task_type": "evaluate_model", "required": True},
                    {"task_type": "deploy_model", "required": False},
                ]
            },
        }

        # 尝试加载自定义模板
        if os.path.exists(template_path):
            try:
                with open(template_path, "r", encoding="utf-8") as f:
                    custom_templates = json.load(f)
                    # 合并自定义模板和默认模板
                    for goal_type, template in custom_templates.items():
                        default_templates[goal_type] = template
                logger.info("已加载自定义任务模板: {template_path}")
            except Exception as e:
                logger.warning("加载任务模板失败: {e}")

        return default_templates

    def decompose_goal_to_tasks(self, goal: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        将单个目标分解为一系列有序的任务步骤。

        Args:
            goal: 要分解的目标。
                示例 (来自 GoalProcessor):
                {
                    "goal_id": "goal_123",
                    "goal_type": "video_creation",
                    "description": "Create a 30-second highlight reel...",
                    "parameters": {"duration_seconds": 30, "theme": "epic_wins", ...},
                    ...
                }

        Returns:
            分解后的任务列表 (计划)。
                示例: [
                    {"task_id": "task_123_1",
        "goal_id": "goal_123",
        "task_type": "load_media",
        "parameters": {"media_ids": [...]}, "status": "pending",
        "dependencies": []},
                    {"task_id": "task_123_2",
        "goal_id": "goal_123",
        "task_type": "analyze_scenes",
        "parameters": {"analysis_type": "action_detection"}, "status": "pending",
        "dependencies": ["task_123_1"]},
                    ...
                ]
        """
        logger.info(f"开始为目标 '{goal.get('goal_id')}' ({goal.get('description')}) 分解任务。")

        tasks = []
        goal_id = goal.get("goal_id", "unknown_goal")
        goal_type = goal.get("goal_type")
        parameters = goal.get("parameters", {})

        # 检查目标类型是否有对应的任务模板
        if goal_type not in self.task_templates:
            logger.warning(f"未知的目标类型 '{goal_type}'，无法分解任务。")
            return [
                {
                    "task_id": self._generate_task_id(goal_id, 1),
                    "goal_id": goal_id,
                    "task_type": "unsupported_goal_type",
                    "parameters": {"message": f"Goal type '{goal_type}' is not supported for task decomposition."},
                    "status": "failed",
                    "dependencies": [],
                    "created_at": datetime.datetime.now().isoformat(),
                }
            ]

        # 获取任务模板
        template = self.task_templates[goal_type]
        template_tasks = template.get("tasks", [])

        # 检查源媒体是否存在 (对于视频处理和创建类型的目标)
        if goal_type in ["video_processing", "video_creation"]:
            source_media = parameters.get("source_media", [])
            if not source_media:
                logger.warning(f"警告: 目标 '{goal_id}' 没有指定源媒体，无法规划加载任务。")
                return [
                    {
                        "task_id": self._generate_task_id(goal_id, 1),
                        "goal_id": goal_id,
                        "task_type": "error_handling",
                        "parameters": {"error_message": "Missing source media for video processing goal."},
                        "status": "failed",
                        "dependencies": [],
                        "created_at": datetime.datetime.now().isoformat(),
                    }
                ]

        # 根据模板创建任务
        task_counter = 1
        task_id_map = {}  # 用于记录任务类型到任务ID的映射

        for task_template in template_tasks:
            task_type = task_template.get("task_type")
            required = task_template.get("required", True)

            # 检查是否需要跳过可选任务
            if not required:
                # 根据目标参数决定是否跳过
                if task_type == "add_effects" and not parameters.get("apply_effects", False):
                    continue
                if task_type == "add_music" and not parameters.get("add_background_music", False):
                    continue
                if task_type == "add_subtitles" and not parameters.get("generate_subtitles", False):
                    continue
                if task_type == "deploy_model" and not parameters.get("auto_deploy", False):
                    continue

            # 生成任务ID
            task_id = self._generate_task_id(goal_id, task_counter)
            task_id_map[task_type] = task_id

            # 创建任务参数
            task_parameters = self._create_task_parameters(task_type, parameters, goal_type)

            # 确定任务依赖
            dependencies = self._resolve_dependencies(task_type, task_id_map)

            # 创建任务
            task = {
                "task_id": task_id,
                "goal_id": goal_id,
                "task_type": task_type,
                "parameters": task_parameters,
                "status": "pending",
                "dependencies": dependencies,
                "created_at": datetime.datetime.now().isoformat(),
            }

            tasks.append(task)
            task_counter += 1

        logger.info(f"为目标 '{goal_id}' 分解得到 {len(tasks)} 个任务。")
        return tasks

    def _generate_task_id(self, goal_id: str, counter: int) -> str:
        """
        生成任务ID。

        Args:
            goal_id: 目标ID
            counter: 任务计数器

        Returns:
            任务ID
        """
        return "{goal_id}_task_{counter}"

    def _create_task_parameters(
        self, task_type: str, goal_parameters: Dict[str, Any], goal_type: str
    ) -> Dict[str, Any]:
        """
        根据任务类型和目标参数创建任务参数。

        Args:
            task_type: 任务类型
            goal_parameters: 目标参数
            goal_type: 目标类型

        Returns:
            任务参数
        """
        task_parameters = {}

        if task_type == "load_media":
            task_parameters["media_paths_or_ids"] = goal_parameters.get("source_media", [])

        elif task_type == "analyze_media_content":
            # 根据主题或其他参数确定分析类型
            theme = goal_parameters.get("theme")
            if theme == "epic_wins":
                task_parameters["analysis_type"] = "action_detection"
                task_parameters["keywords"] = ["kill", "victory", "win", "highlight"]
            elif theme == "funny_moments":
                task_parameters["analysis_type"] = "emotion_detection"
                task_parameters["keywords"] = ["laugh", "fail", "funny"]
            else:
                task_parameters["analysis_type"] = "general_scene_analysis"

        elif task_type == "select_clips_for_timeline":
            task_parameters["target_duration_seconds"] = goal_parameters.get("duration_seconds")
            task_parameters["theme"] = goal_parameters.get("theme")
            task_parameters["style_preferences"] = goal_parameters.get("preferred_style")

        elif task_type == "assemble_video_timeline":
            task_parameters["transitions"] = goal_parameters.get("transitions", ["fade"])
            task_parameters["text_overlays"] = goal_parameters.get("text_overlays_config")

        elif task_type == "add_effects":
            task_parameters["effects"] = goal_parameters.get("effects", [])
            task_parameters["intensity"] = goal_parameters.get("effects_intensity", 1.0)

        elif task_type == "add_music":
            task_parameters["music_preference"] = goal_parameters.get("music_preference")
            task_parameters["music_path"] = goal_parameters.get("music_path")
            task_parameters["volume"] = goal_parameters.get("music_volume", 0.3)

        elif task_type == "add_subtitles":
            task_parameters["subtitle_style"] = goal_parameters.get("subtitle_style", "standard")
            task_parameters["language"] = goal_parameters.get("language", "zh-CN")

        elif task_type == "render_final_video":
            task_parameters["output_format"] = goal_parameters.get("output_format", "mp4")
            task_parameters["quality"] = goal_parameters.get("quality", "high")
            task_parameters["resolution"] = goal_parameters.get("resolution", "1080p")

        elif task_type == "publish_video":
            task_parameters["platforms"] = goal_parameters.get("platforms", [])
            task_parameters["publish_time"] = goal_parameters.get("publish_time")

        elif task_type == "prepare_training_data":
            task_parameters["source"] = goal_parameters.get("data_source")
            task_parameters["model_type"] = goal_parameters.get("model_name")

        elif task_type == "train_model":
            task_parameters["model_name"] = goal_parameters.get("model_name")
            task_parameters["training_params"] = goal_parameters.get("training_params", {})

        elif task_type == "evaluate_model":
            task_parameters["model_name"] = goal_parameters.get("model_name")
            task_parameters["evaluation_metrics"] = goal_parameters.get("evaluation_metrics", ["accuracy", "f1"])

        elif task_type == "deploy_model":
            task_parameters["model_name"] = goal_parameters.get("model_name")
            task_parameters["deployment_target"] = goal_parameters.get("deployment_target", "production")

        # 移除None值
        return {k: v for k, v in task_parameters.items() if v is not None}

    def _resolve_dependencies(self, task_type: str, task_id_map: Dict[str, str]) -> List[str]:
        """
        解析任务依赖关系。

        Args:
            task_type: 任务类型
            task_id_map: 任务类型到任务ID的映射

        Returns:
            依赖任务ID列表
        """
        dependencies = []

        # 获取任务类型的依赖
        dependency_types = self.task_dependencies.get(task_type, [])

        # 将依赖类型转换为依赖任务ID
        for dep_type in dependency_types:
            if dep_type in task_id_map:
                dependencies.append(task_id_map[dep_type])

        return dependencies

    def optimize_task_plan(self, tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        优化任务计划 (例如，合并任务、并行化、调整顺序)。

        Args:
            tasks: 原始任务计划

        Returns:
            优化后的任务计划
        """
        logger.info("优化任务计划 (当前共 {len(tasks)} 个任务)")

        # 如果任务数量太少，不需要优化
        if len(tasks) <= 2:
            logger.info("任务数量较少，无需优化")
            return tasks

        # 构建任务依赖图
        task_graph = self._build_task_graph(tasks)

        # 检查并行执行的可能性
        parallel_groups = self._identify_parallel_tasks(tasks, task_graph)

        # 如果没有可并行的任务，直接返回
        if not parallel_groups:
            logger.info("未发现可并行执行的任务")
            return tasks

        # 标记可并行执行的任务
        optimized_tasks = tasks.copy()
        for i, task in enumerate(optimized_tasks):
            for group_id, group_tasks in parallel_groups.items():
                if task["task_id"] in group_tasks:
                    optimized_tasks[i]["parallel_group"] = group_id
                    break

        logger.info("任务计划优化完成，发现 {len(parallel_groups)} 个可并行执行的任务组")
        return optimized_tasks

    def _build_task_graph(self, tasks: List[Dict[str, Any]]) -> Dict[str, Set[str]]:
        """
        构建任务依赖图。

        Args:
            tasks: 任务列表

        Returns:
            任务依赖图，键为任务ID，值为依赖该任务的任务ID集合
        """
        # 初始化图
        graph = {task["task_id"]: set() for task in tasks}

        # 添加依赖关系
        for task in tasks:
            for dep_id in task.get("dependencies", []):
                if dep_id in graph:
                    graph[dep_id].add(task["task_id"])

        return graph

    def _identify_parallel_tasks(
        self, tasks: List[Dict[str, Any]], task_graph: Dict[str, Set[str]]
    ) -> Dict[str, List[str]]:
        """
        识别可并行执行的任务。

        Args:
            tasks: 任务列表
            task_graph: 任务依赖图

        Returns:
            可并行执行的任务组，键为组ID，值为任务ID列表
        """
        # 按依赖关系对任务进行分层
        layers = self._layer_tasks(tasks)

        # 识别每一层中可并行执行的任务
        parallel_groups = {}
        group_counter = 1

        for layer in layers:
            if len(layer) > 1:
                # 检查层内的任务是否有相互依赖
                independent_tasks = []
                for task_id in layer:
                    # 检查该任务是否依赖层内的其他任务
                    is_independent = True
                    for other_id in layer:
                        if other_id != task_id:
                            # 如果other_id依赖task_id，则task_id不独立
                            if task_id in self._get_all_dependencies(other_id, tasks):
                                is_independent = False
                                break

                    if is_independent:
                        independent_tasks.append(task_id)

                # 如果有多个独立任务，将它们分为一组
                if len(independent_tasks) > 1:
                    group_id = "parallel_group_{group_counter}"
                    parallel_groups[group_id] = independent_tasks
                    group_counter += 1

        return parallel_groups

    def _layer_tasks(self, tasks: List[Dict[str, Any]]) -> List[List[str]]:
        """
        按依赖关系对任务进行分层。

        Args:
            tasks: 任务列表

        Returns:
            任务层次，每一层是一个任务ID列表
        """
        # 构建任务ID到任务的映射
        task_map = {task["task_id"]: task for task in tasks}

        # 计算每个任务的入度（依赖数量）
        in_degree = {task["task_id"]: len(task.get("dependencies", [])) for task in tasks}

        # 初始化层次
        layers = []
        remaining_tasks = set(task_map.keys())

        while remaining_tasks:
            # 找出当前入度为0的任务（没有依赖或依赖已处理）
            current_layer = [task_id for task_id in remaining_tasks if in_degree[task_id] == 0]

            if not current_layer:
                # 如果没有入度为0的任务但仍有剩余任务，说明存在循环依赖
                logger.warning("检测到循环依赖，无法完全分层")
                # 将剩余任务作为最后一层
                layers.append(list(remaining_tasks))
                break

            # 添加当前层
            layers.append(current_layer)

            # 更新剩余任务
            remaining_tasks -= set(current_layer)

            # 更新依赖于当前层任务的任务的入度
            for task_id in current_layer:
                task_map[task_id]
                for dependent_task in tasks:
                    if task_id in dependent_task.get("dependencies", []):
                        in_degree[dependent_task["task_id"]] -= 1

        return layers

    def _get_all_dependencies(self, task_id: str, tasks: List[Dict[str, Any]]) -> Set[str]:
        """
        获取任务的所有依赖（直接和间接）。

        Args:
            task_id: 任务ID
            tasks: 任务列表

        Returns:
            依赖任务ID集合
        """
        # 构建任务ID到任务的映射
        task_map = {task["task_id"]: task for task in tasks}

        # 递归获取所有依赖
        all_deps = set()

        def collect_deps(tid):
            if tid not in task_map:
                return

            deps = task_map[tid].get("dependencies", [])
            all_deps.update(deps)

            for dep_id in deps:
                collect_deps(dep_id)

        collect_deps(task_id)
        return all_deps

    def get_task_templates(self) -> Dict[str, Dict[str, Any]]:
        """
        获取任务模板。

        Returns:
            任务模板字典
        """
        return self.task_templates

    def add_task_template(self, goal_type: str, template: Dict[str, Any]) -> bool:
        """
        添加任务模板。

        Args:
            goal_type: 目标类型
            template: 任务模板

        Returns:
            是否添加成功
        """
        logger.info("添加任务模板: {goal_type}")

        # 验证模板格式
        if "tasks" not in template or not isinstance(template["tasks"], list):
            logger.error("无效的任务模板格式: {template}")
            return False

        # 添加模板
        self.task_templates[goal_type] = template

        # 保存模板
        self._save_task_templates()

        return True

    def _save_task_templates(self) -> None:
        """保存任务模板"""
        template_path = os.path.join(self.config_dir, "task_templates.json")

        try:
            with open(template_path, "w", encoding="utf-8") as f:
                json.dump(self.task_templates, f, ensure_ascii=False, indent=2)
            logger.info("任务模板已保存到: {template_path}")
        except Exception as e:
            logger.error("保存任务模板失败: {e}")

    def estimate_task_duration(self, tasks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        估计任务执行时间。

        Args:
            tasks: 任务列表

        Returns:
            任务执行时间估计
        """
        # 任务类型的基本执行时间估计（秒）
        base_durations = {
            "load_media": 5,
            "analyze_media_content": 30,
            "select_clips_for_timeline": 15,
            "assemble_video_timeline": 20,
            "add_effects": 25,
            "add_music": 10,
            "add_subtitles": 30,
            "render_final_video": 60,
            "publish_video": 20,
            "prepare_training_data": 120,
            "train_model": 600,
            "evaluate_model": 60,
            "deploy_model": 120,
        }

        # 估计每个任务的执行时间
        task_durations = {}
        for task in tasks:
            task_type = task.get("task_type")
            base_duration = base_durations.get(task_type, 30)  # 默认30秒

            # 根据任务参数调整时间
            adjusted_duration = base_duration

            # 例如，渲染高质量视频需要更长时间
            if task_type == "render_final_video":
                quality = task.get("parameters", {}).get("quality", "medium")
                if quality == "high":
                    adjusted_duration *= 2
                elif quality == "ultra":
                    adjusted_duration *= 4

            # 训练模型时间与参数有关
            elif task_type == "train_model":
                training_params = task.get("parameters", {}).get("training_params", {})
                epochs = training_params.get("epochs", 100)
                adjusted_duration = base_duration * (epochs / 100)

            task_durations[task["task_id"]] = adjusted_duration

        # 计算关键路径和总执行时间
        critical_path, total_duration = self._calculate_critical_path(tasks, task_durations)

        return {
            "total_duration_seconds": total_duration,
            "total_duration_formatted": self._format_duration(total_duration),
            "task_durations": task_durations,
            "critical_path": critical_path,
        }

    def _calculate_critical_path(
        self, tasks: List[Dict[str, Any]], task_durations: Dict[str, float]
    ) -> Tuple[List[str], float]:
        """
        计算关键路径和总执行时间。

        Args:
            tasks: 任务列表
            task_durations: 任务执行时间

        Returns:
            (关键路径, 总执行时间)
        """
        # 构建任务ID到任务的映射
        task_map = {task["task_id"]: task for task in tasks}

        # 计算每个任务的最早开始时间和最早结束时间
        earliest_start = {}
        earliest_finish = {}

        # 拓扑排序
        layers = self._layer_tasks(tasks)
        flattened_layers = [task_id for layer in layers for task_id in layer]

        # 正向传递：计算最早开始和结束时间
        for task_id in flattened_layers:
            task = task_map[task_id]
            dependencies = task.get("dependencies", [])

            if not dependencies:
                earliest_start[task_id] = 0
            else:
                earliest_start[task_id] = max(earliest_finish.get(dep_id, 0) for dep_id in dependencies)

            earliest_finish[task_id] = earliest_start[task_id] + task_durations.get(task_id, 0)

        # 找出最后完成的任务
        if not earliest_finish:
            return [], 0

        last_task_id = max(earliest_finish, key=earliest_finish.get)
        total_duration = earliest_finish[last_task_id]

        # 反向传递：找出关键路径
        critical_path = [last_task_id]
        current_task_id = last_task_id

        while task_map[current_task_id].get("dependencies"):
            dependencies = task_map[current_task_id].get("dependencies", [])
            if not dependencies:
                break

            # 找出最晚完成的依赖任务
            current_task_id = max(dependencies, key=lambda dep_id: earliest_finish.get(dep_id, 0))
            critical_path.insert(0, current_task_id)

        return critical_path, total_duration

    def _format_duration(self, seconds: float) -> str:
        """
        格式化时间。

        Args:
            seconds: 秒数

        Returns:
            格式化后的时间字符串
        """
        minutes, seconds = divmod(int(seconds), 60)
        hours, minutes = divmod(minutes, 60)

        if hours > 0:
            return "{hours}小时 {minutes}分钟 {seconds}秒"
        elif minutes > 0:
            return "{minutes}分钟 {seconds}秒"
        else:
            return "{seconds}秒"

if __name__ == "__main__":
    planner = TaskPlanner()

    # 示例目标 (来自GoalProcessor的输出)
    sample_video_goal = {
        "goal_id": "goal_user_vid_001",
        "goal_type": "video_creation",  # 或者 "video_processing"
        "description": "Create a 60-second funny moments reel from cat_videos folder, prefer fast cuts.",
        "parameters": {
            "duration_seconds": 60,
            "theme": "funny_moments",
            "source_media": ["cat_video_01.mp4", "cat_video_02.mp4", "cat_video_03.mp4"],
            "output_format": "webm",
            "quality": "720p",
            "preferred_style": "fast_paced_cuts",
            "music_preference": "quirky_instrumental",
        },
        "priority": 1,
        "user_preferences_applied": ["preferred_editing_style_fast_paced_cuts"],
    }

    video_tasks = planner.decompose_goal_to_tasks(sample_video_goal)
    print("\n分解后的视频创作任务:")
    for task in video_tasks:
        print(f"  - {task['task_id']}: {task['task_type']} (Deps: {task['dependencies']})")

    optimized_video_tasks = planner.optimize_task_plan(video_tasks)
    # print("\n优化后的视频创作任务:")
    # for task in optimized_video_tasks:
    #     print(f"  - {task['task_id']}: {task['task_type']}")

    print("-" * 20)

    sample_system_goal = {
        "goal_id": "sys_goal_retrain_002",
        "goal_type": "model_retraining",
        "description": "Retrain object detection model.",
        "parameters": {
            "model_name": "object_detector_yolo_v5",
            "data_source": "/data/new_images_for_detection/",
            "training_params": {"epochs": 100, "learning_rate": 0.001},
        },
        "priority": 3,
        "source": "system_internal",
    }

    system_tasks = planner.decompose_goal_to_tasks(sample_system_goal)
    print("\n分解后的模型再训练任务:")
    for task in system_tasks:
        print(f"  - {task['task_id']}: {task['task_type']} (Deps: {task['dependencies']})")

    print("-" * 20)

    empty_media_goal = {
        "goal_id": "goal_user_vid_002",
        "goal_type": "video_creation",
        "description": "Make a video.",
        "parameters": {"source_media": []},  # 没有源媒体
    }
    error_tasks = planner.decompose_goal_to_tasks(empty_media_goal)
    print("\n处理无源媒体目标的任务:")
    for task in error_tasks:
        print(f"  - {task['task_id']}: {task['task_type']} - {task['parameters']}")
