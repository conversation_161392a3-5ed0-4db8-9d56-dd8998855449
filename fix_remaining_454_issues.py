#!/usr/bin/env python3
"""
修复剩余454个代码质量问题的专用脚本
"""

import re
import subprocess
from pathlib import Path
from typing import Dict
from typing import Tuple


def fix_critical_errors() -> bool:
    """修复2个严重错误"""
    print("🚨 修复严重错误...")

    # 1. 修复 test_dependencies.py 中的未定义变量
    test_deps_file = Path("test_dependencies.py")
    if test_deps_file.exists():
        content = test_deps_file.read_text(encoding="utf-8")

        # 在test_opencv函数中添加numpy导入
        if "def test_opencv():" in content and "import numpy as np" not in content:
            # 查找test_opencv函数并添加numpy导入
            lines = content.split("\n")
            new_lines = []

            for i, line in enumerate(lines):
                new_lines.append(line)
                if "def test_opencv():" in line:
                    # 在函数开始后添加导入
                    indent = "    "
                    new_lines.append(f"{indent}import cv2")
                    new_lines.append(f"{indent}import numpy as np")
                    # 跳过原来的cv2导入行
                    if i + 1 < len(lines) and "import cv2" in lines[i + 1]:
                        continue

            test_deps_file.write_text("\n".join(new_lines), encoding="utf-8")
            print("✅ 修复了 test_dependencies.py 中的 numpy 导入问题")

    # 2. 修复 api_server.py 中的重复导入
    api_server_file = Path("backend/agent/user_interface/api_server.py")
    if api_server_file.exists():
        content = api_server_file.read_text(encoding="utf-8")
        lines = content.split("\n")

        # 删除第593行附近的重复uvicorn导入
        new_lines = []
        uvicorn_imported = False

        for line in lines:
            if "import uvicorn" in line:
                if not uvicorn_imported:
                    new_lines.append(line)
                    uvicorn_imported = True
                # 跳过重复的导入
            else:
                new_lines.append(line)

        api_server_file.write_text("\n".join(new_lines), encoding="utf-8")
        print("✅ 修复了 api_server.py 中的重复导入问题")

    return True


def clean_unused_variables() -> bool:
    """清理298个未使用变量"""
    print("🧹 清理未使用变量...")

    # 使用更激进的autoflake参数
    command = (
        "autoflake "
        "--remove-all-unused-imports "
        "--remove-unused-variables "
        "--remove-duplicate-keys "
        "--in-place "
        "--recursive "
        "--exclude=venv "
        "."
    )

    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 未使用变量清理完成")
            return True
        else:
            print(f"❌ 清理失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 清理异常: {e}")
        return False


def fix_unused_exception_variables() -> bool:
    """修复未使用的异常变量"""
    print("🔧 修复未使用的异常变量...")

    python_files = list(Path(".").rglob("*.py"))
    fixed_count = 0

    for file_path in python_files:
        if "venv" in str(file_path) or "__pycache__" in str(file_path):
            continue

        try:
            content = file_path.read_text(encoding="utf-8")
            original_content = content

            # 替换未使用的异常变量
            # except Exception as e: -> except Exception:
            content = re.sub(r"except\s+(\w+)\s+as\s+\w+\s*:\s*\n(\s*)pass", r"except \1:\n\2pass", content)

            # except Exception as e: (后面没有使用e的情况)
            content = re.sub(
                r"except\s+(\w+)\s+as\s+(\w+)\s*:\s*\n(\s*)([^#\n]*)\n",
                lambda m: (
                    f"except {m.group(1)}:\n{m.group(3)}{m.group(4)}\n" if m.group(2) not in m.group(4) else m.group(0)
                ),
                content,
            )

            if content != original_content:
                file_path.write_text(content, encoding="utf-8")
                fixed_count += 1

        except Exception as e:
            print(f"⚠️ 处理文件 {file_path} 时出错: {e}")

    print(f"✅ 修复了 {fixed_count} 个文件中的异常变量问题")
    return True


def fix_import_positions() -> bool:
    """修复65个导入位置问题"""
    print("📦 修复导入位置...")

    # 使用isort重新排序导入
    command = "isort --profile black --line-length 120 --skip=venv --force-single-line ."

    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 导入位置修复完成")
            return True
        else:
            print(f"❌ 导入修复失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 导入修复异常: {e}")
        return False


def fix_long_lines() -> bool:
    """修复42个超长行"""
    print("📏 修复超长行...")

    python_files = list(Path(".").rglob("*.py"))
    fixed_count = 0

    for file_path in python_files:
        if "venv" in str(file_path) or "__pycache__" in str(file_path):
            continue

        try:
            content = file_path.read_text(encoding="utf-8")
            lines = content.split("\n")
            new_lines = []

            for line in lines:
                if len(line) > 120:
                    # 尝试自动换行
                    if '", "' in line and len(line) > 120:
                        # 处理长字符串
                        new_line = line.replace('", "', '",\n        "')
                        new_lines.append(new_line)
                        fixed_count += 1
                    elif " and " in line and len(line) > 120:
                        # 处理长条件语句
                        new_line = line.replace(" and ", " \\\n        and ")
                        new_lines.append(new_line)
                        fixed_count += 1
                    else:
                        new_lines.append(line)
                else:
                    new_lines.append(line)

            if fixed_count > 0:
                file_path.write_text("\n".join(new_lines), encoding="utf-8")

        except Exception as e:
            print(f"⚠️ 处理文件 {file_path} 时出错: {e}")

    print(f"✅ 修复了 {fixed_count} 个超长行")
    return True


def clean_trailing_whitespace() -> bool:
    """清理行尾空白"""
    print("🧽 清理行尾空白...")

    python_files = list(Path(".").rglob("*.py"))
    fixed_count = 0

    for file_path in python_files:
        if "venv" in str(file_path) or "__pycache__" in str(file_path):
            continue

        try:
            content = file_path.read_text(encoding="utf-8")
            lines = content.split("\n")
            new_lines = [line.rstrip() for line in lines]

            if lines != new_lines:
                file_path.write_text("\n".join(new_lines), encoding="utf-8")
                fixed_count += 1

        except Exception as e:
            print(f"⚠️ 处理文件 {file_path} 时出错: {e}")

    print(f"✅ 清理了 {fixed_count} 个文件的行尾空白")
    return True


def run_final_check() -> Tuple[int, Dict[str, int]]:
    """运行最终检查"""
    print("🔍 运行最终代码质量检查...")

    try:
        result = subprocess.run(
            "python -m flake8 --statistics --count --max-line-length=120 --exclude=venv,__pycache__,.git .",
            shell=True,
            capture_output=True,
            text=True,
        )

        output = result.stdout
        lines = output.strip().split("\n")

        # 解析统计信息
        stats = {}
        total_issues = 0

        for line in lines:
            if line and not line.startswith(".") and not line.startswith("flake8"):
                parts = line.strip().split()
                if len(parts) >= 2 and parts[0].isdigit():
                    count = int(parts[0])
                    error_type = parts[1]
                    stats[error_type] = count
                    total_issues += count

        return total_issues, stats

    except Exception as e:
        print(f"❌ 最终检查失败: {e}")
        return -1, {}


def main():
    """主修复流程"""
    print("🚀 修复剩余454个问题")
    print("=" * 50)

    # 确认执行
    response = input("是否继续执行修复? (y/N): ").strip().lower()
    if response != "y":
        print("❌ 用户取消操作")
        return

    # 修复步骤
    steps = [
        ("修复严重错误", fix_critical_errors),
        ("清理未使用变量", clean_unused_variables),
        ("修复异常变量", fix_unused_exception_variables),
        ("修复导入位置", fix_import_positions),
        ("修复超长行", fix_long_lines),
        ("清理行尾空白", clean_trailing_whitespace),
    ]

    success_count = 0

    for step_name, step_func in steps:
        print(f"\n📋 步骤: {step_name}")
        try:
            if step_func():
                success_count += 1
                print(f"✅ {step_name} 完成")
            else:
                print(f"❌ {step_name} 失败")
        except Exception as e:
            print(f"❌ {step_name} 异常: {e}")

    # 最终检查
    print("\n" + "=" * 50)
    print("📊 修复结果统计")
    print("=" * 50)

    total_issues, stats = run_final_check()

    if total_issues >= 0:
        print(f"🎯 修复后剩余问题: {total_issues} 个")
        print(f"📈 修复率: {((454 - total_issues) / 454 * 100):.1f}%")

        if stats:
            print("\n📋 剩余问题分类:")
            for error_type, count in sorted(stats.items()):
                print(f"  {error_type}: {count}")
    else:
        print("❌ 无法获取最终统计信息")

    print(f"\n✅ 修复流程完成! 成功执行 {success_count}/{len(steps)} 个步骤")

    if total_issues < 50:
        print("🎉 恭喜! 代码质量已达到优秀水平!")
    elif total_issues < 100:
        print("👍 不错! 大部分问题已修复")
    else:
        print("⚠️ 仍有较多问题，建议继续优化")


if __name__ == "__main__":
    main()
