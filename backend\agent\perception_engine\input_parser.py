# backend.agent.perception_engine.input_parser

import json
import logging
import os
import re
from typing import Any
from typing import Dict
from typing import Optional
from typing import Union

import yaml

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

class InputParser:
    """
    解析用户输入和配置文件，支持多种输入格式和配置文件类型。
    """

    def __init__(self, config_dir: str = None):
        """
        初始化输入解析器。

        Args:
            config_dir: 配置文件目录，默认为当前目录下的 'config'
        """
        self.config_dir = config_dir or os.path.join(os.getcwd(), "config")

        # 确保配置目录存在
        os.makedirs(self.config_dir, exist_ok=True)

        # 支持的命令类型
        self.supported_commands = {
            "create_and_publish_video": "创建并发布视频",
            "analyze_video": "分析视频",
            "edit_video": "编辑视频",
            "add_effects": "添加特效",
            "extract_highlights": "提取精彩片段",
            "generate_subtitles": "生成字幕",
            "add_background_music": "添加背景音乐",
            "batch_process": "批量处理",
            "help": "帮助",
        }

        # 命令参数验证规则
        self.command_validation_rules = {
            "create_and_publish_video": {
                "required": ["material_path"],
                "optional": ["platforms", "edit_rules", "title", "description", "tags"],
            },
            "analyze_video": {"required": ["video_path"], "optional": ["analysis_types", "output_format"]},
            "edit_video": {"required": ["video_path"], "optional": ["output_path", "edit_rules", "duration", "style"]},
            "add_effects": {"required": ["video_path"], "optional": ["effects", "output_path"]},
            "extract_highlights": {
                "required": ["video_path"],
                "optional": ["duration", "output_path", "highlight_criteria"],
            },
            "generate_subtitles": {"required": ["video_path"], "optional": ["language", "output_format", "style"]},
            "add_background_music": {
                "required": ["video_path", "music_path"],
                "optional": ["volume", "fade_in", "fade_out"],
            },
            "batch_process": {
                "required": ["input_directory"],
                "optional": ["output_directory", "process_type", "file_pattern"],
            },
            "help": {"required": [], "optional": ["command"]},
        }

        logger.info("InputParser 初始化完成。配置目录: {self.config_dir}")

    def parse_user_command(self, command_input: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        解析用户命令，支持字符串或字典格式的输入。

        Args:
            command_input: 用户输入的命令，可以是字符串或字典

        Returns:
            解析后的命令参数字典
        """
        logger.info("解析用户命令: {command_input}")

        # 如果输入是字符串，尝试解析为结构化命令
        if isinstance(command_input, str):
            return self._parse_command_string(command_input)

        # 如果输入是字典，直接验证和处理
        elif isinstance(command_input, dict):
            return self._validate_command_dict(command_input)

        # 不支持的输入类型
        else:
            error_msg = f"不支持的命令输入类型: {type(command_input)}"
            logger.error(error_msg)
            return {"action": "error", "params": {}, "message": error_msg}

    def _parse_command_string(self, command_str: str) -> Dict[str, Any]:
        """
        解析命令字符串为结构化命令。

        Args:
            command_str: 命令字符串

        Returns:
            解析后的命令参数字典
        """
        logger.info("解析命令字符串: {command_str}")

        # 尝试识别命令类型
        command_str = command_str.strip()

        # 检查是否是JSON格式
        if command_str.startswith("{") and command_str.endswith("}"):
            try:
                command_data = json.loads(command_str)
                return self._validate_command_dict(command_data)
            except json.JSONDecodeError:
                logger.warning("命令字符串不是有效的JSON格式，尝试自然语言解析")

        # 自然语言命令解析
        return self._parse_natural_language_command(command_str)

    def _parse_natural_language_command(self, command_str: str) -> Dict[str, Any]:
        """
        解析自然语言命令。

        Args:
            command_str: 自然语言命令字符串

        Returns:
            解析后的命令参数字典
        """
        logger.info("解析自然语言命令: {command_str}")

        # 尝试匹配命令类型
        action = "unknown"
        params = {}

        # 创建并发布视频
        if re.search(r"创建|发布|制作|生成.*视频", command_str, re.IGNORECASE):
            action = "create_and_publish_video"

            # 尝试提取参数
            # 素材路径
            material_path_match = re.search(r"素材[路径|地址]?[:|：]?\s*([^\s,，]+)", command_str)
            if material_path_match:
                params["material_path"] = material_path_match.group(1)

            # 平台
            platforms_match = re.search(r"平台[:|：]?\s*([^,，.。]+)", command_str)
            if platforms_match:
                platforms_str = platforms_match.group(1)
                platforms = [p.strip() for p in re.split(r"[,，、和与]", platforms_str) if p.strip()]
                if platforms:
                    params["platforms"] = platforms

            # 标题
            title_match = re.search(r"标题[:|：]?\s*([^,，.。]+)", command_str)
            if title_match:
                params["title"] = title_match.group(1).strip()

            # 描述
            description_match = re.search(r"描述[:|：]?\s*([^,，.。]+)", command_str)
            if description_match:
                params["description"] = description_match.group(1).strip()

            # 标签
            tags_match = re.search(r"标签[:|：]?\s*([^,，.。]+)", command_str)
            if tags_match:
                tags_str = tags_match.group(1)
                tags = [t.strip() for t in re.split(r"[,，、和与]", tags_str) if t.strip()]
                if tags:
                    params["tags"] = tags

            # 时长
            duration_match = re.search(r"时长[:|：]?\s*(\d+)\s*(秒|分钟|s|min)", command_str)
            if duration_match:
                int(duration_match.group(1))
                unit = duration_match.group(2)
                if unit in ["分钟", "min"]:
                    duration = "{value}min"
                else:
                    duration = "{value}s"

                if "edit_rules" not in params:
                    params["edit_rules"] = {}
                params["edit_rules"]["duration"] = duration

            # 风格
            style_match = re.search(r"风格[:|：]?\s*([^,，.。]+)", command_str)
            if style_match:
                style = style_match.group(1).strip()
                if "edit_rules" not in params:
                    params["edit_rules"] = {}
                params["edit_rules"]["style"] = style

        # 分析视频
        elif re.search(r"分析.*视频", command_str, re.IGNORECASE):
            action = "analyze_video"

            # 视频路径
            video_path_match = re.search(r"视频[路径|地址]?[:|：]?\s*([^\s,，]+)", command_str)
            if video_path_match:
                params["video_path"] = video_path_match.group(1)

            # 分析类型
            analysis_types = []
            if re.search(r"场景|镜头", command_str):
                analysis_types.append("scene_detection")
            if re.search(r"物体|目标", command_str):
                analysis_types.append("object_detection")
            if re.search(r"人脸|面部", command_str):
                analysis_types.append("face_detection")
            if re.search(r"情感|情绪", command_str):
                analysis_types.append("emotion_detection")
            if re.search(r"语音|文本|字幕", command_str):
                analysis_types.append("speech_to_text")

            if analysis_types:
                params["analysis_types"] = analysis_types

        # 编辑视频
        elif re.search(r"编辑.*视频", command_str, re.IGNORECASE):
            action = "edit_video"

            # 视频路径
            video_path_match = re.search(r"视频[路径|地址]?[:|：]?\s*([^\s,，]+)", command_str)
            if video_path_match:
                params["video_path"] = video_path_match.group(1)

            # 输出路径
            output_path_match = re.search(r"输出[路径|地址]?[:|：]?\s*([^\s,，]+)", command_str)
            if output_path_match:
                params["output_path"] = output_path_match.group(1)

            # 时长
            duration_match = re.search(r"时长[:|：]?\s*(\d+)\s*(秒|分钟|s|min)", command_str)
            if duration_match:
                int(duration_match.group(1))
                unit = duration_match.group(2)
                if unit in ["分钟", "min"]:
                    params["duration"] = "{value}min"
                else:
                    params["duration"] = "{value}s"

            # 风格
            style_match = re.search(r"风格[:|：]?\s*([^,，.。]+)", command_str)
            if style_match:
                params["style"] = style_match.group(1).strip()

        # 生成字幕
        elif re.search(r"生成.*字幕", command_str, re.IGNORECASE):
            action = "generate_subtitles"

            # 视频路径
            video_path_match = re.search(r"视频[路径|地址]?[:|：]?\s*([^\s,，]+)", command_str)
            if video_path_match:
                params["video_path"] = video_path_match.group(1)

            # 语言
            language_match = re.search(r"语言[:|：]?\s*([^,，.。]+)", command_str)
            if language_match:
                params["language"] = language_match.group(1).strip()

        # 添加背景音乐
        elif re.search(r"添加.*音乐|背景音乐", command_str, re.IGNORECASE):
            action = "add_background_music"

            # 视频路径
            video_path_match = re.search(r"视频[路径|地址]?[:|：]?\s*([^\s,，]+)", command_str)
            if video_path_match:
                params["video_path"] = video_path_match.group(1)

            # 音乐路径
            music_path_match = re.search(r"音乐[路径|地址]?[:|：]?\s*([^\s,，]+)", command_str)
            if music_path_match:
                params["music_path"] = music_path_match.group(1)

            # 音量
            volume_match = re.search(r"音量[:|：]?\s*([\d\.]+)", command_str)
            if volume_match:
                params["volume"] = float(volume_match.group(1))

        # 提取精彩片段
        elif re.search(r"提取.*精彩|亮点|片段|集锦", command_str, re.IGNORECASE):
            action = "extract_highlights"

            # 视频路径
            video_path_match = re.search(r"视频[路径|地址]?[:|：]?\s*([^\s,，]+)", command_str)
            if video_path_match:
                params["video_path"] = video_path_match.group(1)

            # 时长
            duration_match = re.search(r"时长[:|：]?\s*(\d+)\s*(秒|分钟|s|min)", command_str)
            if duration_match:
                int(duration_match.group(1))
                unit = duration_match.group(2)
                if unit in ["分钟", "min"]:
                    params["duration"] = "{value}min"
                else:
                    params["duration"] = "{value}s"

        # 帮助命令
        elif re.search(r"帮助|help", command_str, re.IGNORECASE):
            action = "help"

            # 特定命令的帮助
            command_match = re.search(r"帮助[:|：]?\s*([^,，.。]+)", command_str)
            if command_match:
                params["command"] = command_match.group(1).strip()

        # 未识别的命令
        else:
            logger.warning(f"无法识别的命令: {command_str}")
            return {"action": "unknown", "params": {}, "message": "无法识别的命令: {command_str}"}

        # 验证解析结果
        return self._validate_parsed_command(action, params)

    def _validate_parsed_command(self, action: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证解析后的命令。

        Args:
            action: 命令动作
            params: 命令参数

        Returns:
            验证后的命令字典
        """
        logger.info("验证命令: action={action}, params={params}")

        # 检查命令是否支持
        if action not in self.supported_commands and action != "unknown":
            logger.warning(f"不支持的命令: {action}")
            return {"action": "error", "params": {}, "message": "不支持的命令: {action}"}

        # 如果是未知命令，直接返回
        if action == "unknown":
            return {"action": action, "params": params}

        # 验证必需参数
        validation_rules = self.command_validation_rules.get(action, {})
        required_params = validation_rules.get("required", [])

        missing_params = [param for param in required_params if param not in params]

        if missing_params:
            logger.warning(f"命令 {action} 缺少必需参数: {missing_params}")
            return {"action": "error", "params": params, "message": f"命令缺少必需参数: {', '.join(missing_params)}"}

        return {"action": action, "params": params}

    def _validate_command_dict(self, command_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证命令字典。

        Args:
            command_data: 命令字典

        Returns:
            验证后的命令字典
        """
        logger.info("验证命令字典: {command_data}")

        if not isinstance(command_data, dict):
            logger.error(f"命令格式不正确，应为字典: {command_data}")
            return {"action": "error", "params": {}, "message": "命令格式不正确，应为字典。"}

        action = command_data.get("action", "unknown")
        params = command_data.get("params", {})

        return self._validate_parsed_command(action, params)

    def parse_config_file(self, config_path: str) -> Dict[str, Any]:
        """
        解析配置文件，支持多种格式（JSON, YAML, INI等）。

        Args:
            config_path: 配置文件路径

        Returns:
            解析后的配置字典
        """
        logger.info("解析配置文件: {config_path}")

        # 检查文件是否存在
        if not os.path.exists(config_path):
            logger.error(f"配置文件不存在: {config_path}")
            return {"status": "error", "message": "配置文件不存在: {config_path}"}

        # 根据文件扩展名确定解析方法
        file_ext = os.path.splitext(config_path)[1].lower()

        try:
            if file_ext == ".json":
                return self._parse_json_config(config_path)
            elif file_ext in [".yml", ".yaml"]:
                return self._parse_yaml_config(config_path)
            elif file_ext == ".ini":
                return self._parse_ini_config(config_path)
            else:
                # 默认按简单的键值对格式解析
                return self._parse_simple_config(config_path)
        except Exception as e:
            logger.error(f"解析配置文件 {config_path} 失败: {e}")
            return {"status": "error", "message": "解析配置文件失败: {e}"}

    def _parse_json_config(self, config_path: str) -> Dict[str, Any]:
        """解析JSON格式配置文件"""
        with open(config_path, "r", encoding="utf-8") as f:
            return json.load(f)

    def _parse_yaml_config(self, config_path: str) -> Dict[str, Any]:
        """解析YAML格式配置文件"""
        with open(config_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f)

    def _parse_ini_config(self, config_path: str) -> Dict[str, Any]:
        """解析INI格式配置文件"""
        config_data = {}
        with open(config_path, "r", encoding="utf-8") as f:
            current_section = None
            for line in f:
                line = line.strip()
                if not line or line.startswith("#") or line.startswith(";"):
                    continue

                # 处理节
                if line.startswith("[") and line.endswith("]"):
                    current_section = line[1:-1].strip()
                    if current_section not in config_data:
                        config_data[current_section] = {}
                    continue

                # 处理键值对
                if "=" in line:
                    key, value = line.split("=", 1)
                    key = key.strip()
                    value = value.strip()

                    if current_section:
                        config_data[current_section][key] = value
                    else:
                        config_data[key] = value

        return config_data

    def _parse_simple_config(self, config_path: str) -> Dict[str, Any]:
        """解析简单的键值对格式配置文件"""
        config_data = {}
        with open(config_path, "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith("#"):
                    continue

                if "=" in line:
                    key, value = line.split("=", 1)
                    config_data[key.strip()] = value.strip()

        return config_data

    def get_command_help(self, command: Optional[str] = None) -> str:
        """
        获取命令帮助信息。

        Args:
            command: 特定命令，如果为None则返回所有命令的帮助

        Returns:
            帮助信息字符串
        """
        if command is None:
            # 返回所有命令的简要帮助
            help_text = "支持的命令列表:\n"
            for cmd, desc in self.supported_commands.items():
                help_text += "- {cmd}: {desc}\n"
            help_text += "\n使用 'help:<command>' 获取特定命令的详细帮助。"
            return help_text

        # 返回特定命令的详细帮助
        if command not in self.supported_commands:
            return "未知命令: {command}"

        help_text = f"命令: {command} - {self.supported_commands[command]}\n\n"

        # 添加参数信息
        validation_rules = self.command_validation_rules.get(command, {})
        required_params = validation_rules.get("required", [])
        optional_params = validation_rules.get("optional", [])

        if required_params:
            help_text += "必需参数:\n"
            for param in required_params:
                help_text += "- {param}\n"
            help_text += "\n"

        if optional_params:
            help_text += "可选参数:\n"
            for param in optional_params:
                help_text += "- {param}\n"
            help_text += "\n"

        # 添加示例
        help_text += "示例:\n"
        if command == "create_and_publish_video":
            help_text += '{"action": "create_and_publish_video", "params": {"material_path": "/path/to/video.mp4", "platforms": ["douyin", "bilibili"]}}\n'
        elif command == "analyze_video":
            help_text += '{"action": "analyze_video", "params": {"video_path": "/path/to/video.mp4", "analysis_types": ["scene_detection", "object_detection"]}}\n'

        return help_text

    def save_config(self, config_data: Dict[str, Any], config_name: str, format: str = "json") -> str:
        """
        保存配置到文件。

        Args:
            config_data: 配置数据
            config_name: 配置名称
            format: 配置文件格式，支持 'json', 'yaml', 'ini'

        Returns:
            保存的配置文件路径
        """
        logger.info("保存配置: {config_name}, 格式: {format}")

        # 确定文件扩展名
        if format == "json":
            pass
        elif format == "yaml":
            pass
        elif format == "ini":
            pass
        else:
            pass

        # 生成文件路径
        config_path = os.path.join(self.config_dir, "{config_name}{file_ext}")

        try:
            # 根据格式保存配置
            if format == "json":
                with open(config_path, "w", encoding="utf-8") as f:
                    json.dump(config_data, f, ensure_ascii=False, indent=2)
            elif format == "yaml":
                with open(config_path, "w", encoding="utf-8") as f:
                    yaml.dump(config_data, f, allow_unicode=True)
            elif format == "ini":
                with open(config_path, "w", encoding="utf-8") as f:
                    for section, section_data in config_data.items():
                        if isinstance(section_data, dict):
                            f.write("[{section}]\n")
                            for key, value in section_data.items():
                                f.write("{key} = {value}\n")
                            f.write("\n")
                        else:
                            f.write("{section} = {section_data}\n")
            else:
                # 简单键值对格式
                with open(config_path, "w", encoding="utf-8") as f:
                    for key, value in config_data.items():
                        f.write("{key} = {value}\n")

            logger.info("配置已保存到: {config_path}")
            return config_path
        except Exception as e:
            logger.error("保存配置失败: {e}")
            return ""
