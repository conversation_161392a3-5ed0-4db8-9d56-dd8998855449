﻿#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import logging
import os
from typing import Any, Optional, Dict, Union

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

class InputParser:
    def __init__(self, config_dir: Optional[str] = None):
        self.config_dir = config_dir or os.path.join(os.getcwd(), "config")
        os.makedirs(self.config_dir, exist_ok=True)
        self.supported_commands = {}
            "create_and_publish_video": "创建并发布视频",
            "analyze_video": "分析视频",
            "edit_video": "编辑视频",
            "help": "帮助"}
        logger.info(f"InputParser 初始化完成。配置目录: {self.config_dir}")

    def parse_user_command(self, command_input: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
        logger.info(f"解析用户命令: {command_input}")
        if isinstance(command_input, str):
            return {"action": "create_and_publish_video", "params": {"material_path": command_input}}
        elif isinstance(command_input, dict):
            return command_input
        else:
            error_msg = f"不支持的命令输入类型: {type(command_input)}"
            logger.error(error_msg)
            return {"action": "error", "params": {}, "message": error_msg}

    def parse_config_file(self, config_path: str) -> Dict[str, Any]:
        logger.info(f"解析配置文件: {config_path}")
        if not os.path.exists(config_path):
            logger.error(f"配置文件不存在: {config_path}")
            return {"status": "error", "message": f"配置文件不存在: {config_path}"}
        
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"解析配置文件 {config_path} 失败: {e}")
            return {"status": "error", "message": f"解析配置文件失败: {e}"}

    def get_command_help(self, command: Optional[str] = None) -> str:
        if command is None:
            help_text = "支持的命令列表:\n"
            for cmd, desc in self.supported_commands.items():
                help_text += f"- {cmd}: {desc}\n"
            return help_text
        
        if command not in self.supported_commands:
            return f"未知命令: {command}"
        
        return f"命令: {command} - {self.supported_commands[command]}"

        if __name__ == "__main__":
        parser = InputParser()
        print("InputParser 测试完成")
