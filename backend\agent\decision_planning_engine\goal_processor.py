# backend.agent.decision_planning_engine.goal_processor

from typing import Any
from typing import Dict
from typing import List
from typing import Optional

# 假设与感知引擎的输入解析器交互
# from ..perception_engine.input_parser import InputParser

class GoalProcessor:
    """解析用户意图和系统目标，将其转化为可执行的任务目标。"""

    def __init__(self, user_profile_store):
        """
        初始化 GoalProcessor。
        :param user_profile_store: 用户画像存储实例。
        """
        self.user_profile_store = user_profile_store
        print("GoalProcessor 初始化完毕。")

    def parse_user_intent(
        self, user_input: Dict[str, Any], user_profile: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        解析用户的输入（命令、请求）以确定其意图和目标。

        Args:
            user_input (Dict[str, Any]): 用户输入的数据。
                示例: {"type": "command",
        "command_text": "Create a 30-second highlight reel of my last gaming session about epic wins",
        "source_media_ids": ["game_session_001.mp4"]}
                      {"type": "config_update", "config_path": "/path/to/project_config.json"}
            user_profile (Optional[Dict[str, Any]], optional): 用户画像数据，用于个性化目标解析。Defaults to None.

        Returns:
            List[Dict[str, Any]]: 解析出的目标列表。
                示例: [
                    {
                        "goal_id": "goal_123",
                        "goal_type": "video_creation",
                        "description": "Create a 30-second highlight reel of epic wins from game_session_001.mp4",
                        "parameters": {
                            "duration_seconds": 30,
                            "theme": "epic_wins",
                            "source_media": ["game_session_001.mp4"],
                            "output_format": "mp4",
                            "quality": "high"
                        },
                        "priority": 1, # 1 (highest) to 5 (lowest)
                        "constraints": ["avoid_spoilers_if_possible"],
                        "user_preferences_applied": ["preferred_music_genre_action"]
                    }
                ]
        """
        print("解析用户意图: {user_input}")
        goals = []

        # TODO: 实现更复杂的意图解析逻辑 (NLP, 规则引擎等)
        # - 识别命令类型 (创建、编辑、分析、学习等)
        # - 提取关键参数 (时长、主题、风格、源素材等)
        # - 应用用户偏好 (从 user_profile)
        # - 设置默认值

        input_type = user_input.get("type")

        if input_type == "command" and "command_text" in user_input:
            command_text = user_input["command_text"].lower()
            source_media_ids = user_input.get("source_media_ids", [])
            goal_id = f"goal_{int(user_input.get('timestamp', __import__('time').time()))}"
            description = f"User command: {user_input['command_text']}"
            parameters = {
                "source_media": source_media_ids,
                "output_format": "mp4",  # Default
                "quality": "medium",  # Default
            }
            user_preferences_applied = []

            # 简化的关键词匹配
            if "highlight reel" in command_text or "highlights" in command_text:
                parameters["task_type"] = "generate_highlights"
                if "30-second" in command_text or "30 second" in command_text:
                    parameters["duration_seconds"] = 30
                elif "1 minute" in command_text or "60-second" in command_text:
                    parameters["duration_seconds"] = 60

                # 尝试提取主题 (非常简化)
                if "epic wins" in command_text:
                    parameters["theme"] = "epic_wins"
                elif "funny moments" in command_text:
                    parameters["theme"] = "funny_moments"

            elif "summarize" in command_text:
                parameters["task_type"] = "summarize_video"

            elif "add music" in command_text:
                parameters["task_type"] = "add_background_music"
                # 提取音乐类型等

            if user_profile:
                preferred_style = user_profile.get("preferences", {}).get("editing_style")
                if preferred_style:
                    parameters["preferred_style"] = preferred_style
                    user_preferences_applied.append(f"preferred_editing_style_{preferred_style}")

            goals.append(
                {
                    "goal_id": goal_id,
                    "goal_type": "video_processing",  # 更通用的类型
                    "description": description,
                    "parameters": parameters,
                    "priority": user_input.get("priority", 2),
                    "constraints": user_input.get("constraints", []),
                    "user_preferences_applied": user_preferences_applied,
                }
            )

        elif input_type == "config_update":
            # 处理配置更新目标，可能触发系统行为调整
            goals.append(
                {
                    "goal_id": f"goal_config_{int(user_input.get('timestamp', __import__('time').time()))}",
                    "goal_type": "system_configuration",
                    "description": f"Update system configuration from {user_input.get('config_path')}",
                    "parameters": {"config_path": user_input.get("config_path")},
                    "priority": 1,
                }
            )
        else:
            print("未知用户输入类型: {input_type}")

        print("解析得到的目标: {goals}")
        return goals

    def define_system_goal(
        self, goal_type: str, description: str, parameters: Dict[str, Any], priority: int = 3
    ) -> Dict[str, Any]:
        """
        定义一个由系统内部触发的目标 (例如，定期维护、模型更新)。

        Args:
            goal_type (str): 目标的类型 (e.g., "model_retraining", "data_cleanup", "trend_analysis").
            description (str): 目标的描述。
            parameters (Dict[str, Any]): 目标相关的参数。
            priority (int, optional): 目标的优先级. Defaults to 3.

        Returns:
            Dict[str, Any]: 定义的系统目标。
        """
        goal_id = f"sys_goal_{goal_type.replace('_', '')}_{int(__import__('time').time())}"
        system_goal = {
            "goal_id": goal_id,
            "goal_type": goal_type,
            "description": description,
            "parameters": parameters,
            "priority": priority,
            "source": "system_internal",
        }
        print("定义系统目标: {system_goal}")
        return system_goal

    def prioritize_goals(self, goals: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        根据优先级、依赖关系、资源等对目标列表进行排序。
        (简单实现：仅按 'priority' 字段排序)

        Args:
            goals (List[Dict[str, Any]]): 待排序的目标列表。

        Returns:
            List[Dict[str, Any]]:排序后的目标列表 (优先级高的在前)。
        """
        print("对目标列表进行优先级排序 (当前共 {len(goals)} 个目标)。")
        # 简单的基于优先级的排序，数字越小优先级越高
        # TODO: 实现更复杂的排序逻辑，考虑依赖、截止日期、资源冲突等
        sorted_goals = sorted(goals, key=lambda g: g.get("priority", 5))
        print("目标排序完成。")
        return sorted_goals

if __name__ == "__main__":
    processor = GoalProcessor()

    # 测试用户意图解析
    sample_user_command = {
        "type": "command",
        "command_text": "Make a 1 minute highlight reel of my cat playing with a laser pointer, focus on funny moments. Use upbeat music.",
        "source_media_ids": ["cat_video_01.mp4", "cat_video_02.mp4"],
        "timestamp": 1678886400,
    }
    user_profile_data = {
        "user_id": "cat_lover_88",
        "preferences": {"editing_style": "fast_paced_cuts", "preferred_music_genre": "upbeat_electronic"},
    }
    parsed_goals = processor.parse_user_intent(sample_user_command, user_profile=user_profile_data)

    print("-" * 20)

    # 测试定义系统目标
    system_maintenance_goal = processor.define_system_goal(
        goal_type="knowledge_base_backup",
        description="Perform daily backup of the knowledge base.",
        parameters={"backup_location": "/mnt/backup_storage/kb/"},
        priority=4,
    )
    parsed_goals.append(system_maintenance_goal)

    model_retrain_goal = processor.define_system_goal(
        goal_type="model_retraining",
        description="Retrain scene classification model with new data.",
        parameters={"model_name": "scene_classifier_v2", "data_source": "/data/new_labeled_scenes/"},
        priority=2,
    )
    parsed_goals.append(model_retrain_goal)

    print("-" * 20)

    # 测试目标优先级排序
    print(f"排序前的目标列表 (部分): {[(g['goal_id'], g['priority']) for g in parsed_goals]}")
    prioritized_goals = processor.prioritize_goals(parsed_goals)
    print(f"排序后的目标列表 (部分): {[(g['goal_id'], g['priority']) for g in prioritized_goals]}")

    # 假设第一个目标是最高优先级的
    if prioritized_goals:
        print(f"最高优先级目标: {prioritized_goals[0]['description']}")
