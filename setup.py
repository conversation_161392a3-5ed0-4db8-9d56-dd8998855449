#!/usr/bin/env python
# -*- coding: utf-8 -*-

from setuptools import find_packages
from setuptools import setup

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as f:
    requirements = f.read().splitlines()

setup(
    name="intellicut-agent",
    version="0.1.0",
    author="IntelliCut Team",
    author_email="<EMAIL>",
    description="AI agent for automating and intelligently editing videos",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/intellicut/intellicut-agent",
    packages=find_packages(),
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "intellicut=cli:main",
        ],
    },
)
