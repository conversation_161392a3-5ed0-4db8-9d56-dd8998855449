#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合修复脚本 - 修复剩余的严重问题
"""

import os
import re


def fix_task_executor():
    """修复 task_executor.py"""
    file_path = "backend/agent/action_execution_engine/task_executor.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复字符串问题
        fixes = [
            (r"task_to_run\['task_type\\'\]", r"task_to_run['task_type']"),
            (r"f'task_id'", r"task_id"),
            (r"f'task_type'", r"task_type"),
            (r"\{result\.get\('execution_time_ms'\)\}ms", r"{result.get('execution_time_ms')}ms"),
        ]
        
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 修复完成: {file_path}")
        
    except Exception as e:
        print(f"❌ 修复失败 {file_path}: {e}")


def fix_api_server():
    """修复 api_server.py"""
    file_path = "backend/agent/user_interface/api_server.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复路由定义中的变量问题
        fixes = [
            (r'@app\.get\(f"/api/analyze/video/status/\{task_id:str\}"\)', r'@app.get("/api/analyze/video/status/{task_id}")'),
            (r'@app\.get\(f"/api/analyze/video/result/\{task_id:str\}"\)', r'@app.get("/api/analyze/video/result/{task_id}")'),
            (r'@app\.get\(f"/api/edit/video/status/\{task_id:str\}"\)', r'@app.get("/api/edit/video/status/{task_id}")'),
            (r'@app\.get\(f"/api/edit/video/result/\{task_id:str\}"\)', r'@app.get("/api/edit/video/result/{task_id}")'),
            (r'@app\.get\(f"/api/edit/video/download/\{task_id:str\}"\)', r'@app.get("/api/edit/video/download/{task_id}")'),
        ]
        
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 修复完成: {file_path}")
        
    except Exception as e:
        print(f"❌ 修复失败 {file_path}: {e}")


def fix_youtube_api():
    """修复 youtube_api.py"""
    file_path = "backend/agent/tools/api_clients/youtube_api.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复缩进问题
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # 修复类定义后的缩进
            if i > 0 and lines[i-1].strip() == "class YouTubeAPI:":
                if line and not line.startswith('    ') and not line.startswith('\t'):
                    line = '    ' + line.lstrip()
            
            fixed_lines.append(line)
        
        content = '\n'.join(fixed_lines)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 修复完成: {file_path}")
        
    except Exception as e:
        print(f"❌ 修复失败 {file_path}: {e}")


def fix_config_loader():
    """修复 config_loader.py"""
    file_path = "backend/utils/config_loader.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复缩进问题
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # 修复类定义后的缩进
            if i > 0 and lines[i-1].strip() == "class ConfigLoader:":
                if line and not line.startswith('    ') and not line.startswith('\t'):
                    line = '    ' + line.lstrip()
            
            fixed_lines.append(line)
        
        content = '\n'.join(fixed_lines)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 修复完成: {file_path}")
        
    except Exception as e:
        print(f"❌ 修复失败 {file_path}: {e}")


def fix_test_files():
    """修复测试文件"""
    test_files = [
        "backend/agent/batch_publisher/test_batch_publisher.py",
        "backend/agent/content_analyzer/test_content_analyzer.py"
    ]
    
    for file_path in test_files:
        if not os.path.exists(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 修复基本的缩进问题
            lines = content.split('\n')
            fixed_lines = []
            
            for i, line in enumerate(lines):
                # 修复类定义后的缩进
                if i > 0 and 'class ' in lines[i-1] and lines[i-1].endswith(':'):
                    if line and not line.startswith('    ') and not line.startswith('\t'):
                        line = '    ' + line.lstrip()
                
                fixed_lines.append(line)
            
            content = '\n'.join(fixed_lines)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 修复完成: {file_path}")
            
        except Exception as e:
            print(f"❌ 修复失败 {file_path}: {e}")


def remove_unused_imports():
    """移除未使用的导入"""
    files_to_fix = [
        "backend/agent/content_analyzer.py",
        "backend/agent/batch_publisher/batch_publisher.py",
        "backend/agent/content_analyzer/content_analyzer.py",
        "fix_project.py",
        "quick_fix.py"
    ]
    
    for file_path in files_to_fix:
        if not os.path.exists(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 移除未使用的导入（简单处理）
            lines = content.split('\n')
            fixed_lines = []
            
            for line in lines:
                # 跳过明显未使用的导入
                if (line.strip().startswith('from typing import') and 
                    ('Optional' in line or 'List' in line) and 
                    'Optional' not in content[content.find(line)+len(line):] and
                    'List' not in content[content.find(line)+len(line):]):
                    continue
                
                fixed_lines.append(line)
            
            content = '\n'.join(fixed_lines)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 清理导入: {file_path}")
            
        except Exception as e:
            print(f"❌ 清理失败 {file_path}: {e}")


def fix_corrupted_files():
    """修复损坏的文件"""
    # 修复 cli.py
    cli_path = "cli.py"
    if os.path.exists(cli_path):
        try:
            # 删除损坏的文件
            os.remove(cli_path)
            print(f"✅ 删除损坏文件: {cli_path}")
        except Exception as e:
            print(f"❌ 删除失败 {cli_path}: {e}")


def main():
    """主函数"""
    print("🔧 开始综合修复...")
    print("=" * 40)
    
    # 修复各个文件
    fix_task_executor()
    fix_api_server()
    fix_youtube_api()
    fix_config_loader()
    fix_test_files()
    remove_unused_imports()
    fix_corrupted_files()
    
    print("\n🎉 综合修复完成!")
    
    # 测试系统
    print("\n🧪 测试系统...")
    try:
        import subprocess
        result = subprocess.run([
            "python", "-c", 
            "from backend.agent.agent_coordinator import AgentCoordinator; print('System import successful')"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 系统测试通过！")
        else:
            print(f"❌ 系统测试失败: {result.stderr}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    main()
