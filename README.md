# IntelliCutAgent 2.0

🎬 **智能视频处理系统** - 全新重构版本

## 📋 项目简介

IntelliCutAgent 2.0 是一个智能视频处理系统，能够自动分析、编辑和发布视频内容到多个平台。本版本采用全新的架构设计，更加简洁、稳定和易于扩展。

## ✨ 主要功能

- 🔍 **智能视频分析** - 自动检测场景、提取亮点、评估质量
- ✂️ **智能视频编辑** - 根据内容自动生成短视频
- 📤 **多平台发布** - 支持抖音、B站、YouTube等平台
- 💬 **命令行界面** - 简单易用的交互式命令行工具
- 📊 **任务管理** - 完整的任务历史和状态跟踪

## 🏗️ 项目结构

```
IntelliCutAgent/
├── backend/                    # 后端代码
│   ├── agent/                 # 核心代理模块
│   │   ├── core/             # 核心组件
│   │   │   └── coordinator.py # 代理协调器
│   │   └── modules/          # 功能模块
│   │       └── cli_interface.py # 命令行界面
│   └── utils/                # 工具函数
├── config/                   # 配置文件
│   └── settings.py          # 系统配置
├── data/                    # 数据目录
├── logs/                    # 日志目录
├── output/                  # 输出目录
├── tests/                   # 测试文件
├── venv/                    # 虚拟环境
├── main.py                  # 主入口文件
└── README.md               # 项目说明
```

## 🚀 快速开始

### 1. 环境准备

确保已安装Python 3.8+，并激活虚拟环境：

```bash
# 激活虚拟环境（Windows）
venv\Scripts\activate

# 激活虚拟环境（Linux/Mac）
source venv/bin/activate
```

### 2. 运行系统

```bash
python main.py
```

### 3. 使用命令

系统启动后，可以使用以下命令：

```bash
# 查看帮助
help

# 分析视频
analyze video.mp4

# 编辑视频（生成60秒短视频）
edit video.mp4 60

# 发布视频到抖音和B站
publish edited_video.mp4 douyin bilibili

# 查看系统状态
status

# 查看任务历史
history

# 退出系统
exit
```

## 🎯 支持的平台

- 🎵 **抖音** (douyin) - 短视频平台
- 📺 **哔哩哔哩** (bilibili) - 视频分享平台
- 🌐 **YouTube** (youtube) - 国际视频平台
- 📱 **小红书** (xiaohongshu) - 生活分享平台
- 📰 **今日头条** (toutiao) - 资讯平台

## 📁 支持的格式

### 视频格式
- MP4, AVI, MOV, MKV, WMV, FLV

### 音频格式
- MP3, WAV, AAC, FLAC, OGG

## 🔧 系统架构

### 核心组件

1. **AgentCoordinator** - 系统大脑，负责协调各个模块
2. **CLIInterface** - 命令行界面，提供用户交互
3. **配置管理** - 统一的配置管理系统

### 设计原则

- 🎯 **简洁性** - 代码结构清晰，易于理解
- 🔧 **模块化** - 功能模块独立，便于扩展
- 🛡️ **稳定性** - 完善的错误处理和日志记录
- 📈 **可扩展** - 预留接口，支持功能扩展

## 📝 开发计划

### 当前版本 (v2.0.0)
- ✅ 基础架构搭建
- ✅ 命令行界面
- ✅ 核心协调器
- ✅ 配置管理系统

### 下一版本 (v2.1.0)
- 🔄 视频分析模块
- 🔄 视频编辑模块
- 🔄 平台发布模块
- 🔄 Web界面

## 🤝 贡献指南

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

如有问题或建议，请通过以下方式联系：

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/intellicut/agent/issues)
- 💬 讨论: [GitHub Discussions](https://github.com/intellicut/agent/discussions)

---

**IntelliCutAgent 2.0** - 让视频创作更智能！ 🎬✨
