#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超级修复脚本 - 批量修复最严重的问题
"""

import os
import re


def fix_critical_issues(file_path: str) -> int:
    """修复关键问题"""
    fixes_count = 0
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复最严重的语法错误
        patterns = [
            # 修复未终止的字符串
            (r"'task_id\\'", r"'task_id'"),
            (r"'message\\'", r"'message'"),
            (r"'duration\\'", r"'duration'"),
            (r"'output_path\\'", r"'output_path'"),
            (r"'status\\'", r"'status'"),
            (r"'unknown\\'", r"'unknown'"),
            (r"'generic\\'", r"'generic'"),
            (r"'\.py\\'", r"'.py'"),
            (r"'venv\\'", r"'venv'"),
            (r"'\.png\\'", r"'.png'"),
            (r"'\.wmv\\'", r"'.wmv'"),
            (r"'\.gi\\'", r"'.gif'"),
            
            # 修复方法定义的缩进
            (r'^def (_[a-zA-Z_][a-zA-Z0-9_]*\([^)]*\)[^:]*:)', r'    def \1'),
            
            # 修复User-Agent字符串
            (r'""Mozilla/5\.0 \([^"]*\)"', r'"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"'),
            
            # 修复f-string错误
            (r'f"([^"]*)\{([^}]+)\}ms"', r'f"\1{\2}ms"'),
            
            # 修复except子句
            (r'except Exception:\s*logger\.error\("操作失败"\)\s*raise HTTPException\(status_code=500, detail=f"([^"]+): \{e\}"\)', 
             r'except Exception as e:\n        logger.error("操作失败")\n        raise HTTPException(status_code=500, detail=f"\1: {e}")'),
            
            # 修复路径中的f-string
            (r'@app\.get\(f"/api/[^"]*\{task_id\}"\)', lambda m: m.group(0).replace('{task_id}', '{task_id:str}')),
        ]
        
        for pattern, replacement in patterns:
            if callable(replacement):
                new_content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
            else:
                new_content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
            
            if new_content != content:
                fixes_count += len(re.findall(pattern, content, flags=re.MULTILINE))
                content = new_content
        
        # 只有在内容发生变化时才写入文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ 修复 {file_path}: {fixes_count} 个关键问题")
        
        return fixes_count
        
    except Exception as e:
        print(f"✗ 修复 {file_path} 失败: {e}")
        return 0


def main():
    """主函数"""
    print("🚀 超级修复脚本")
    print("=" * 30)
    
    # 获取项目根目录
    project_root = os.path.dirname(os.path.abspath(__file__))
    
    # 需要修复的关键文件
    critical_files = [
        "backend/agent/action_execution_engine/task_executor.py",
        "backend/agent/user_interface/cli_interface.py",
        "backend/agent/user_interface/api_server.py",
        "backend/agent/content_optimizer/content_optimizer.py",
        "backend/agent/tools/api_clients/xigua_api.py",
        "fix_project.py",
    ]
    
    total_fixes = 0
    
    # 修复每个文件
    for relative_path in critical_files:
        file_path = os.path.join(project_root, relative_path)
        
        if os.path.exists(file_path):
            fixes = fix_critical_issues(file_path)
            total_fixes += fixes
        else:
            print(f"⚠️ 文件不存在: {relative_path}")
    
    print(f"\n🎉 修复完成！总共修复了 {total_fixes} 个关键问题")
    
    # 现在测试系统
    print("\n🧪 测试系统...")
    try:
        import subprocess
        result = subprocess.run([
            "python", "-c", 
            "from backend.agent.agent_coordinator import AgentCoordinator; print('✓ 系统导入成功')"
        ], capture_output=True, text=True, cwd=project_root)
        
        if result.returncode == 0:
            print("✅ 系统测试通过！")
        else:
            print(f"❌ 系统测试失败: {result.stderr}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    main()
