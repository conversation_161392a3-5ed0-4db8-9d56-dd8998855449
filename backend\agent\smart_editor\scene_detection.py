import logging
from typing import List
from typing import Tuple

import cv2

logger = logging.getLogger(__name__)

def extract_scenes_threshold(
    cap: cv2.VideoCapture, fps: float, threshold: float, min_scene_length: float
) -> List[Tuple[float, float]]:
    """基于简单阈值的场景检测"""
    scenes = []
    prev_frame = None
    scene_start_frame = 0
    frame_idx = 0
    min_scene_frames = int(min_scene_length * fps)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = frame_count / fps

    # 逐帧处理
    while True:
        ret, frame = cap.read()
        if not ret:
            break

        # 转换为灰度图
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        if prev_frame is not None:
            # 计算帧差异
            diff = cv2.absdiff(gray, prev_frame)
            diff_mean = float(cv2.mean(diff)[0])

            # 检测场景变化
            if diff_mean > threshold:
                # 计算场景时间
                scene_start_time = scene_start_frame / fps
                scene_end_time = frame_idx / fps

                # 检查场景长度
                if frame_idx - scene_start_frame >= min_scene_frames:
                    scenes.append((scene_start_time, scene_end_time))
                    logger.debug("检测到场景: {scene_start_time:.2f}s - {scene_end_time:.2f}s")

                # 更新场景起始帧
                scene_start_frame = frame_idx

        # 更新变量
        prev_frame = gray
        frame_idx += 1

    # 添加最后一个场景
    if frame_idx - scene_start_frame >= min_scene_frames:
        scene_start_time = scene_start_frame / fps
        scene_end_time = duration
        scenes.append((scene_start_time, scene_end_time))
        logger.debug("检测到场景: {scene_start_time:.2f}s - {scene_end_time:.2f}s")

    return scenes

def extract_scenes_content(
    cap: cv2.VideoCapture, fps: float, threshold: float, min_scene_length: float
) -> List[Tuple[float, float]]:
    """基于内容特征的场景检测（更智能）"""
    scenes = []
    prev_hist = None
    scene_start_frame = 0
    frame_idx = 0
    min_scene_frames = int(min_scene_length * fps)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = frame_count / fps

    # 逐帧处理
    while True:
        ret, frame = cap.read()
        if not ret:
            break

        # 计算颜色直方图特征
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        hist = cv2.calcHist([hsv], [0, 1], None, [50, 60], [0, 180, 0, 256])
        cv2.normalize(hist, hist, 0, 1, cv2.NORM_MINMAX)

        if prev_hist is not None:
            # 计算直方图相似度（巴氏距离）
            bc = cv2.compareHist(hist, prev_hist, cv2.HISTCMP_BHATTACHARYYA)

            # 检测场景变化（巴氏距离越大，差异越大）
            if bc > threshold / 100.0:  # 调整阈值范围
                # 计算场景时间
                scene_start_time = scene_start_frame / fps
                scene_end_time = frame_idx / fps

                # 检查场景长度
                if frame_idx - scene_start_frame >= min_scene_frames:
                    scenes.append((scene_start_time, scene_end_time))
                    logger.debug("检测到场景(内容): {scene_start_time:.2f}s - {scene_end_time:.2f}s, 差异: {bc:.4f}")

                # 更新场景起始帧
                scene_start_frame = frame_idx

        # 更新变量
        prev_hist = hist
        frame_idx += 1

    # 添加最后一个场景
    if frame_idx - scene_start_frame >= min_scene_frames:
        scene_start_time = scene_start_frame / fps
        scene_end_time = duration
        scenes.append((scene_start_time, scene_end_time))
        logger.debug("检测到场景(内容): {scene_start_time:.2f}s - {scene_end_time:.2f}s")

    return scenes

def extract_scenes_edge(
    cap: cv2.VideoCapture, fps: float, threshold: float, min_scene_length: float
) -> List[Tuple[float, float]]:
    """基于边缘特征的场景检测"""
    scenes = []
    prev_edges = None
    scene_start_frame = 0
    frame_idx = 0
    min_scene_frames = int(min_scene_length * fps)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = frame_count / fps

    # 逐帧处理
    while True:
        ret, frame = cap.read()
        if not ret:
            break

        # 转换为灰度图并检测边缘
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        edges = cv2.Canny(gray, 50, 150)

        if prev_edges is not None:
            # 计算边缘差异
            edge_diff = cv2.absdiff(edges, prev_edges)
            edge_score = float(cv2.mean(edge_diff)[0])

            # 检测场景变化
            if edge_score > threshold:
                # 计算场景时间
                scene_start_time = scene_start_frame / fps
                scene_end_time = frame_idx / fps

                # 检查场景长度
                if frame_idx - scene_start_frame >= min_scene_frames:
                    scenes.append((scene_start_time, scene_end_time))
                    logger.debug(
                        "检测到场景(边缘): {scene_start_time:.2f}s - {scene_end_time:.2f}s, 差异: {edge_score:.4f}"
                    )

                # 更新场景起始帧
                scene_start_frame = frame_idx

        # 更新变量
        prev_edges = edges
        frame_idx += 1

    # 添加最后一个场景
    if frame_idx - scene_start_frame >= min_scene_frames:
        scene_start_time = scene_start_frame / fps
        scene_end_time = duration
        scenes.append((scene_start_time, scene_end_time))
        logger.debug("检测到场景(边缘): {scene_start_time:.2f}s - {scene_end_time:.2f}s")

    return scenes
