#!/usr/bin/env python3
"""
scene_detection module
"""

import logging
from typing import List, Tuple

import cv2

"""基于简单阈值的场景检测"""
"""基于内容特征的场景检测（更智能）"""
"""基于边缘特征的场景检测"""
logger = logging.getLogger(__name__)
def extract_scenes_threshold(
cap: cv2.VideoCapture, fps: float, threshold: float, min_scene_length: float
) -> List[Tuple[float, float]]:
scenes = []
prev_frame = None
scene_start_frame = 0
frame_idx = 0
min_scene_frames = int(min_scene_length * fps)
frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
duration = frame_count / fps
while True:
    ret, frame = cap.read()
    if not ret:
        break
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    if prev_frame is not None:
        diff = cv2.absdiff(gray, prev_frame)
        diff_mean = float(cv2.mean(diff)[0])
        if diff_mean > threshold:
            scene_start_time = scene_start_frame / fps
            scene_end_time = frame_idx / fps
            if frame_idx - scene_start_frame >= min_scene_frames:
                scenes.append((scene_start_time, scene_end_time))
                logger.debug("检测到场景: {scene_start_time:.2f}s - {scene_end_time:.2f}s")
            scene_start_frame = frame_idx
    prev_frame = gray
    frame_idx += 1
if frame_idx - scene_start_frame >= min_scene_frames:
    scene_start_time = scene_start_frame / fps
    scene_end_time = duration
    scenes.append((scene_start_time, scene_end_time))
    logger.debug("检测到场景: {scene_start_time:.2f}s - {scene_end_time:.2f}s")
return scenes
def extract_scenes_content(
cap: cv2.VideoCapture, fps: float, threshold: float, min_scene_length: float
) -> List[Tuple[float, float]]:
scenes = []
prev_hist = None
scene_start_frame = 0
frame_idx = 0
min_scene_frames = int(min_scene_length * fps)
frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
duration = frame_count / fps
while True:
    ret, frame = cap.read()
    if not ret:
        break
    hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
    hist = cv2.calcHist([hsv], [0, 1], None, [50, 60], [0, 180, 0, 256])
    cv2.normalize(hist, hist, 0, 1, cv2.NORM_MINMAX)
    if prev_hist is not None:
        bc = cv2.compareHist(hist, prev_hist, cv2.HISTCMP_BHATTACHARYYA)
        if bc > threshold / 100.0:  # 调整阈值范围
            scene_start_time = scene_start_frame / fps
            scene_end_time = frame_idx / fps
            if frame_idx - scene_start_frame >= min_scene_frames:
                scenes.append((scene_start_time, scene_end_time))
                logger.debug("检测到场景(内容): {scene_start_time:.2f}s - {scene_end_time:.2f}s, 差异: {bc:.4f}")
            scene_start_frame = frame_idx
    prev_hist = hist
    frame_idx += 1
if frame_idx - scene_start_frame >= min_scene_frames:
    scene_start_time = scene_start_frame / fps
    scene_end_time = duration
    scenes.append((scene_start_time, scene_end_time))
    logger.debug("检测到场景(内容): {scene_start_time:.2f}s - {scene_end_time:.2f}s")
return scenes
def extract_scenes_edge(
cap: cv2.VideoCapture, fps: float, threshold: float, min_scene_length: float
) -> List[Tuple[float, float]]:
scenes = []
prev_edges = None
scene_start_frame = 0
frame_idx = 0
min_scene_frames = int(min_scene_length * fps)
frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
duration = frame_count / fps
while True:
    ret, frame = cap.read()
    if not ret:
        break
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    edges = cv2.Canny(gray, 50, 150)
    if prev_edges is not None:
        edge_diff = cv2.absdiff(edges, prev_edges)
        edge_score = float(cv2.mean(edge_diff)[0])
        if edge_score > threshold:
            scene_start_time = scene_start_frame / fps
            scene_end_time = frame_idx / fps
            if frame_idx - scene_start_frame >= min_scene_frames:
                scenes.append((scene_start_time, scene_end_time))
                logger.debug(
                    "检测到场景(边缘): {scene_start_time:.2f}s - {scene_end_time:.2f}s, 差异: {edge_score:.4f}"
                )
            scene_start_frame = frame_idx
    prev_edges = edges
    frame_idx += 1
if frame_idx - scene_start_frame >= min_scene_frames:
    scene_start_time = scene_start_frame / fps
    scene_end_time = duration
    scenes.append((scene_start_time, scene_end_time))
    logger.debug("检测到场景(边缘): {scene_start_time:.2f}s - {scene_end_time:.2f}s")
return scenes