#!/usr/bin/env python3
"""
依赖测试脚本 - 检查IntelliCutAgent项目的所有依赖是否正常工作
"""

import importlib
from typing import Tuple


def test_import(module_name: str, alias: str = None) -> Tuple[bool, str]:
    """
    测试模块导入

    Args:
        module_name: 模块名
        alias: 别名

    Returns:
        (是否成功, 错误信息)
    """
    try:
        if alias:
            module = importlib.import_module(module_name)
            globals()[alias] = module
        else:
            importlib.import_module(module_name)
        return True, "OK"
    except Exception as e:
        return False, str(e)


def test_functionality(module_name: str, test_func) -> Tuple[bool, str]:
    """
    测试模块功能

    Args:
        module_name: 模块名
        test_func: 测试函数

    Returns:
        (是否成功, 错误信息)
    """
    try:
        test_func()
        return True, "功能正常"
    except Exception as e:
        return False, "功能测试失败: {str(e)}"


def main():
    """主测试函数"""
    print("🔍 IntelliCutAgent 依赖检查报告")
    print("=" * 60)

    # 核心依赖测试
    core_deps = [
        ("numpy", "np"),
        ("scipy", None),
        ("yaml", None),
        ("tqdm", None),
        ("requests", None),
        ("dotenv", None),
    ]

    print("\n📦 核心依赖:")
    for module, alias in core_deps:
        success, msg = test_import(module, alias)
        status = "✅" if success else "❌"
        print("  {status} {module}: {msg}")

    # 视频处理依赖
    video_deps = [
        ("moviepy.editor", "mp"),
        ("ffmpeg", None),
        ("cv2", None),
        ("PIL", None),
    ]

    print("\n🎬 视频处理依赖:")
    for module, alias in video_deps:
        success, msg = test_import(module, alias)
        status = "✅" if success else "❌"
        print("  {status} {module}: {msg}")

    # 音频处理依赖
    audio_deps = [
        ("librosa", None),
        ("pydub", None),
        ("soundfile", None),
        ("mutagen", None),
    ]

    print("\n🎵 音频处理依赖:")
    for module, alias in audio_deps:
        success, msg = test_import(module, alias)
        status = "✅" if success else "❌"
        print("  {status} {module}: {msg}")

    # NLP依赖
    nlp_deps = [
        ("nltk", None),
        ("spacy", None),
        ("transformers", None),
    ]

    print("\n🧠 自然语言处理依赖:")
    for module, alias in nlp_deps:
        success, msg = test_import(module, alias)
        status = "✅" if success else "❌"
        print("  {status} {module}: {msg}")

    # 机器学习依赖
    ml_deps = [
        ("sklearn", None),
        ("tensorflow", "t"),
    ]

    print("\n🤖 机器学习依赖:")
    for module, alias in ml_deps:
        success, msg = test_import(module, alias)
        status = "✅" if success else "❌"
        print("  {status} {module}: {msg}")

    # 数据处理依赖
    data_deps = [
        ("pandas", "pd"),
        ("matplotlib.pyplot", "plt"),
    ]

    print("\n📊 数据处理依赖:")
    for module, alias in data_deps:
        success, msg = test_import(module, alias)
        status = "✅" if success else "❌"
        print("  {status} {module}: {msg}")

    # API服务依赖
    api_deps = [
        ("fastapi", None),
        ("uvicorn", None),
        ("pydantic", None),
    ]

    print("\n🌐 API服务依赖:")
    for module, alias in api_deps:
        success, msg = test_import(module, alias)
        status = "✅" if success else "❌"
        print("  {status} {module}: {msg}")

    # Google API依赖
    google_deps = [
        ("googleapiclient.discovery", None),
        ("google.auth", None),
        ("google_auth_oauthlib", None),
        ("google_auth_httplib2", None),
    ]

    print("\n🔍 Google API依赖:")
    for module, alias in google_deps:
        success, msg = test_import(module, alias)
        status = "✅" if success else "❌"
        print("  {status} {module}: {msg}")

    # 其他工具依赖
    tool_deps = [
        ("colorlog", None),
        ("psutil", None),
        ("humanize", None),
        ("requests_toolbelt", None),
    ]

    print("\n🛠️ 其他工具依赖:")
    for module, alias in tool_deps:
        success, msg = test_import(module, alias)
        status = "✅" if success else "❌"
        print("  {status} {module}: {msg}")

    # 功能测试
    print("\n🧪 功能测试:")

    # 测试numpy
    def test_numpy():
        import numpy as np

        arr = np.array([1, 2, 3])
        assert arr.sum() == 6

    success, msg = test_functionality("numpy", test_numpy)
    status = "✅" if success else "❌"
    print("  {status} numpy 基础运算: {msg}")

    # 测试opencv
    def test_opencv():
        pass
        # 创建一个简单的图像
        img = np.zeros((100, 100, 3), dtype="uint8")
        assert img.shape == (100, 100, 3)

    success, msg = test_functionality("cv2", test_opencv)
    status = "✅" if success else "❌"
    print("  {status} OpenCV 图像处理: {msg}")

    # 测试tensorflow
    def test_tensorflow():
        import tensorflow as tf

        # 创建一个简单的张量
        tensor = tf.constant([1, 2, 3])
        assert tensor.shape == (3,)

    success, msg = test_functionality("tensorflow", test_tensorflow)
    status = "✅" if success else "❌"
    print("  {status} TensorFlow 张量操作: {msg}")

    # 测试fastapi
    def test_fastapi():
        from fastapi import FastAPI

        app = FastAPI()
        assert app is not None

    success, msg = test_functionality("fastapi", test_fastapi)
    status = "✅" if success else "❌"
    print("  {status} FastAPI 应用创建: {msg}")

    print("\n" + "=" * 60)
    print("📋 检查完成!")

    # 检查项目模块导入
    print("\n🏗️ 项目模块导入测试:")

    project_modules = [
        "backend.agent_coordinator",
        "main",
    ]

    for module in project_modules:
        success, msg = test_import(module)
        status = "✅" if success else "❌"
        print("  {status} {module}: {msg}")

    print("\n💡 建议:")
    print("1. 如果有❌标记的依赖，请使用 pip install 安装")
    print("2. 对于moviepy问题，可能需要重新安装: pip uninstall moviepy && pip install moviepy")
    print("3. 确保系统已安装FFmpeg (视频处理必需)")
    print("4. 如果TensorFlow有警告，可以忽略或设置环境变量 TF_ENABLE_ONEDNN_OPTS=0")


if __name__ == "__main__":
    main()
