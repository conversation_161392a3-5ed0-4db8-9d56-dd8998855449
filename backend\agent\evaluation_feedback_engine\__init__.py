# backend.agent.evaluation_feedback_engine

"""
评估与反馈引擎 (Evaluation and Feedback Engine)

该模块负责评估智能体生成结果的质量、收集用户反馈，并将这些信息传递给学习引擎以驱动改进。

核心组件:
- PerformanceEvaluator: 评估混剪视频的客观指标 (如流畅度、节奏感、主题相关性等) 和主观质量。
- FeedbackCollector: 收集来自用户的显式反馈 (如评分、评论) 和隐式反馈 (如观看时长、分享行为)。
- ReportGenerator: 生成评估报告，总结性能和反馈，供学习引擎使用或人工审查。
"""

from .feedback_collector import FeedbackCollector
from .performance_evaluator import PerformanceEvaluator
from .report_generator import ReportGenerator

__all__ = ["PerformanceEvaluator", "FeedbackCollector", "ReportGenerator"]

print("Backend Agent: Evaluation and Feedback Engine initialized.")
