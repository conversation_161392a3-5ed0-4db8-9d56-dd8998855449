#!/usr/bin/env python3
"""
feedback_processor module
"""

import datetime
import json
import logging
import os
import time
from collections import Counter, defaultdict
from typing import Any, Dict, List, Optional, Tuple

"""
反馈处理器：处理用户反馈和系统性能数据，以驱动学习和改进。
"""
    """
    初始化反馈处理器。
    Args:
        rule_store: 混剪规则存储实例，用于根据反馈调整规则
        user_profile_store: 用户画像存储实例，用于根据反馈更新用户偏好
        feedback_dir: 用户反馈存储目录
        performance_dir: 系统性能数据存储目录
    """
    """
    处理来自用户的直接反馈。
    Args:
        feedback_data: 反馈数据，包含用户ID、会话ID、剪辑ID、评分、标签、评论等
    Returns:
        处理结果
    """
    """
    处理系统内部的性能指标和分析结果。
    Args:
        performance_data: 系统性能数据，包含模块名称、处理时间、准确率、资源使用情况等
    Returns:
        处理结果
    """
    """
    聚合一段时间内的反馈数据，用于趋势分析或报告。
    Args:
        time_window: 聚合的时间窗口，如 'daily', 'weekly', 'monthly'
        start_date: 开始日期，格式为 'YYYY-MM-DD'
        end_date: 结束日期，格式为 'YYYY-MM-DD'
        user_id: 用户ID，用于筛选特定用户的反馈
        clip_ids: 剪辑ID列表，用于筛选特定剪辑的反馈
    Returns:
        聚合后的反馈摘要
    """
    """
    聚合一段时间内的系统性能数据，用于趋势分析或报告。
    Args:
        time_window: 聚合的时间窗口，如 'daily', 'weekly', 'monthly'
        start_date: 开始日期，格式为 'YYYY-MM-DD'
        end_date: 结束日期，格式为 'YYYY-MM-DD'
        module_names: 模块名称列表，用于筛选特定模块的性能数据
    Returns:
        聚合后的性能摘要
    """
    """
    基于用户反馈和系统性能数据生成改进建议。
    Args:
        feedback_window: 分析的反馈时间窗口
        min_feedback_count: 生成建议所需的最小反馈数量
    Returns:
        改进建议
    """
    """
    跟踪反馈指标的趋势变化。
    Args:
        metric: 要跟踪的指标，如 'rating', 'tags', 'actions'
        time_periods: 时间周期列表，如 ['daily', 'weekly', 'monthly']
        segment_by: 分段依据，如 'user_group', 'clip_type'
    Returns:
        趋势分析结果
    """
    """
    清除旧的反馈和性能数据。
    Args:
        data_type: 要清除的数据类型，'feedback', 'performance' 或 'all\'
        days_to_keep: 保留最近多少天的数据
    Returns:
        清除结果
    """
    """分析反馈内容，提取关键信息和情感倾向"""
    """根据反馈调整混剪规则"""
    """根据反馈更新用户画像"""
    """检查是否需要触发警报或人工干预"""
    """分析系统性能数据"""
    """检查性能指标是否超过阈值，生成警报和调整建议"""
    """收集指定时间范围内的反馈数据"""
    """收集指定时间范围内的性能数据"""
    """聚合反馈数据"""
    """聚合性能数据"""
    """分析指标趋势"""
    """删除指定目录中早于截止日期的文件"""
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)
class FeedbackProcessor:
    def __init__(self, rule_store=None, user_profile_store=None, feedback_dir: Optional[str] = None, performance_dir: Optional[str] = None):
        self.rule_store = rule_store
        self.user_profile_store = user_profile_store
        self.feedback_dir = feedback_dir or os.path.join(os.getcwd(), "data", "feedback")
        self.performance_dir = performance_dir or os.path.join(os.getcwd(), "data", "performance")
        os.makedirs(self.feedback_dir, exist_ok=True)
        os.makedirs(self.performance_dir, exist_ok=True)
        self.feedback_types = {}
        "rating": "评分",
        "comment": "评论",
        "tag": "标签",
        "action": "行为",
        "suggestion": "建议"}
        self.system_modules = {}
        "VideoAnalyzer": "视频分析器",
        "AudioAnalyzer": "音频分析器",
        "ContentAnalyzer": "内容分析器",
        "SmartEditor": "智能编辑器",
        "MaterialManager": "素材管理器",
        "VideoPublisher": "视频发布器",
        "TrendAnalyzer": "趋势分析器",
        "TaskPlanner": "任务规划器f"}
        self.sentiment_dict = {}
        "positive": []
            "好",
            "棒",
            "赞",
            "喜欢",
            "爱",
            "满意",
            "优秀",
            "精彩",
            "完美",
            "出色",
            "高质量",
            "专业",
            "流畅",
            "清晰",
            "有趣",
            "吸引人",
            "创意",
            "创新"],
        "negative": []
            "差",
            "糟",
            "烂",
            "不喜欢",
            "讨厌",
            "不满",
            "失望",
            "问题",
            "错误",
            "缺陷",
            "低质量",
            "业余",
            "卡顿",
            "模糊",
            "无聊",
            "单调",
            "老套",
            "过时"]}
        logger.info("FeedbackProcessor 初始化完成。反馈目录: {self.feedback_dir}, 性能数据目录: {self.performance_dir}")
    def process_user_feedback(self, feedback_data: Dict[str, Any]) -> Dict[str, Any]:
        logger.info(f"处理用户反馈: {feedback_data.get('user_id', 'unknown')}")
        required_fields = ["user_id", "clip_id"]
        for field in required_fields:
        if field not in feedback_data:
            logger.warning(f"反馈数据缺少必要字段: {field}")
            return {"success": False, "error": f"反馈数据缺少必要字段: {field}"}
        if "timestamp" not in feedback_data:
        feedback_data["timestamp"] = datetime.datetime.now().isoformat()
        feedback_id = f"{feedback_data['user_id\']}_{int(time.time())}"
        feedback_path = os.path.join(self.feedback_dir, f"{feedback_id}.json")
        try:
        with open(feedback_path, "w", encoding="utf-8") as f:
            json.dump(feedback_data, f, ensure_ascii=False, indent=2)
        logger.info(f"反馈已记录: {feedback_path}")
        except Exception as e:
        logger.error(f"记录反馈失败: {e}")
        return {"success": False, "error": f"记录反馈失败: {e}"}
        analysis_result = self._analyze_feedback_content(feedback_data)
        logger.info(f"反馈分析结果: {analysis_result}")
        if self.rule_store:
        self._adjust_rules_based_on_feedback(feedback_data, analysis_result)
        if self.user_profile_store:
        self._update_user_profile(feedback_data, analysis_result)
        alerts = self._check_for_alerts(feedback_data, analysis_result)
        if alerts:
        logger.warning(f"反馈触发警报: {alerts}")
        return {"success": True, "feedback_id": feedback_id, "analysis": analysis_result, "alerts": alerts}
    def process_system_performance(self, performance_data: Dict[str, Any]) -> Dict[str, Any]:
        logger.info(f"处理系统性能数据: {performance_data.get('module_name', 'unknown')}")
        if "module_name" not in performance_data:
        logger.warning("性能数据缺少必要字段: module_name")
        return {"success": False, "error": "性能数据缺少必要字段: module_name"}
        if "timestamp" not in performance_data:
        performance_data["timestamp"] = datetime.datetime.now().isoformat()
        performance_data["module_name"]
        performance_id = f"{module_name}_{int(time.time())}"
        performance_path = os.path.join(self.performance_dir, f"{performance_id}.json")
        try:
        with open(performance_path, "w", encoding="utf-8") as f:
            json.dump(performance_data, f, ensure_ascii=False, indent=2)
        logger.info(f"性能数据已记录: {performance_path}")
        except Exception as e:
        logger.error(f"记录性能数据失败: {e}")
        return {"success": False, "error": f"记录性能数据失败: {e}"}
        analysis_result = self._analyze_performance_data(performance_data)
        logger.info(f"性能分析结果: {analysis_result}")
        alerts, adjustments = self._check_performance_thresholds(performance_data, analysis_result)
        if alerts:
        logger.warning(f"性能数据触发警报: {alerts}")
        if adjustments:
        logger.info(f"建议的系统调整: {adjustments}")
        return {}
        "success": True,
        "performance_id": performance_id,
        "analysis": analysis_result,
        "alerts": alerts,
        "suggested_adjustments": adjustments}
def aggregate_feedback()
        self,:
        time_window: str = "daily",
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        user_id: Optional[str] = None,
        clip_ids: Optional[List[str]] = None) -> Dict[str, Any]:
        logger.info(f"聚合反馈数据。时间窗口: {time_window}, 开始日期: {start_date}, 结束日期: {end_date}")
        if start_date:
        start_datetime = datetime.datetime.fromisoformat(start_date)
        else:
        if time_window == "daily":
            start_datetime = datetime.datetime.now() - datetime.timedelta(days=1)
        elif time_window == "weekly":
            start_datetime = datetime.datetime.now() - datetime.timedelta(days=7)
        elif time_window == "monthly":
            start_datetime = datetime.datetime.now() - datetime.timedelta(days=30)
        else:
            start_datetime = datetime.datetime.now() - datetime.timedelta(days=1)
        if end_date:
        end_datetime = datetime.datetime.fromisoformat(end_date)
        else:
        end_datetime = datetime.datetime.now()
        feedback_data = self._collect_feedback_data(start_datetime, end_datetime, user_id, clip_ids)
        if not feedback_data:
        logger.warning("指定时间范围内没有找到反馈数据f")
        return {}
            "period": time_window,
            "start_date": start_datetime.isoformat(),
            "end_date": end_datetime.isoformat(),
            "total_feedback_points": 0,
            "message": "没有找到反馈数据"}
        aggregated_result = self._aggregate_feedback_data(feedback_data, time_window)
        aggregated_result["period"] = time_window
        aggregated_result["start_date"] = start_datetime.isoformat()
        aggregated_result["end_date"] = end_datetime.isoformat()
        logger.info(f"反馈聚合完成。总反馈点数: {aggregated_result['total_feedback_points\']}")
        return aggregated_result
def aggregate_performance()
        self,:
        time_window: str = "daily",
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        module_names: Optional[List[str]] = None) -> Dict[str, Any]:
        logger.info(f"聚合性能数据。时间窗口: {time_window}, 开始日期: {start_date}, 结束日期: {end_date}")
        if start_date:
        start_datetime = datetime.datetime.fromisoformat(start_date)
        else:
        if time_window == "daily":
            start_datetime = datetime.datetime.now() - datetime.timedelta(days=1)
        elif time_window == "weekly":
            start_datetime = datetime.datetime.now() - datetime.timedelta(days=7)
        elif time_window == "monthly":
            start_datetime = datetime.datetime.now() - datetime.timedelta(days=30)
        else:
            start_datetime = datetime.datetime.now() - datetime.timedelta(days=1)
        if end_date:
        end_datetime = datetime.datetime.fromisoformat(end_date)
        else:
        end_datetime = datetime.datetime.now()
        performance_data = self._collect_performance_data(start_datetime, end_datetime, module_names)
        if not performance_data:
        logger.warning("指定时间范围内没有找到性能数据f")
        return {}
            "period": time_window,
            "start_date": start_datetime.isoformat(),
            "end_date": end_datetime.isoformat(),
            "total_data_points": 0,
            "message": "没有找到性能数据"}
        aggregated_result = self._aggregate_performance_data(performance_data, time_window)
        aggregated_result["period"] = time_window
        aggregated_result["start_date"] = start_datetime.isoformat()
        aggregated_result["end_date"] = end_datetime.isoformat()
        logger.info(f"性能聚合完成。总数据点数: {aggregated_result['total_data_points']}")
        return aggregated_result
def generate_improvement_suggestions():
        self, feedback_window: str = "monthly", min_feedback_count: int = 10
        ) -> Dict[str, Any]:
        logger.info(f"生成改进建议。反馈窗口: {feedback_window}, 最小反馈数量: {min_feedback_count}")
        feedback_summary = self.aggregate_feedback(time_window=feedback_window)
        if feedback_summary.get("total_feedback_points", 0) < min_feedback_count:
        logger.warning("反馈数量不足，无法生成可靠的改进建议f")
        return {}
            "success": False,
            "message": "反馈数量不足，需要至少 {min_feedback_count} 条反馈才能生成可靠的建议",
            "current_feedback_count": feedback_summary.get("total_feedback_points", 0)}
        performance_summary = self.aggregate_performance(time_window=feedback_window)
        suggestions = []
        if "average_rating" in feedback_summary and feedback_summary["average_rating"] < 4.0:
        suggestions.append()
            {}
                "category": "用户满意度",
                "issue": f"用户平均评分较低 ({feedback_summary['average_rating\']:.1f}/5.0)",
                "suggestion": "考虑对产品进行全面评估，特别关注用户评分较低的方面",
                "priority": "高"}
        )
        if "common_negative_tags" in feedback_summary and feedback_summary["common_negative_tags"]:
        for tag, count in feedback_summary["common_negative_tags"][:3]:  # 取前3个最常见的负面标签
            suggestions.append()
                {}
                    "category": "用户体验",
                    "issue": f"用户经常提到负面标签: '{tag}' ({count} 次)",
                    "suggestion": f"改进与 f'{tag}' 相关的功能或体验",
                    "priority": "中"}
            )
        if "common_suggestions" in feedback_summary and feedback_summary["common_suggestions"]:
        for suggestion_text, count in feedback_summary["common_suggestions"][:3]:
            suggestions.append()
                {}
                    "category": "功能改进",
                    "issue": f"用户建议: f'{suggestion_text}' ({count} 次)",
                    "suggestion": f"考虑实现用户建议: f'{suggestion_text}'",
                    "priority": "中"}
            )
        if "module_performance" in performance_summary:
        for module, metrics in performance_summary["module_performance"].items():
            if "error_rate" in metrics and metrics["error_rate"] > 0.05:
                suggestions.append()
                    {}
                        "category": "系统稳定性",
                        "issue": f"模块 f'{module}' 的错误率较高 ({metrics['error_rate']:.1%})",
                        "suggestion": f"调查并修复 f'{module}' 模块的错误源",
                        "priority": "高"}
                )
            if "processing_time_ms" in metrics and metrics["processing_time_ms"] > 2000:
                suggestions.append()
                    {}
                        "category": "系统性能",
                        "issue": f"模块 f'{module}' 的处理时间较长 ({metrics['processing_time_ms']} ms)",
                        "suggestion": f"优化 f'{module}' 模块的性能",
                        "priority": "中"}
                )
        if "user_actions" in feedback_summary:
        actions = feedback_summary["user_actions"]
        if actions.get("abandoned_rate", 0) > 0.3:  # 如果放弃率超过30%
            suggestions.append()
                {}
                    "category": "用户留存",
                    "issue": f"用户放弃率较高 ({actions.get('abandoned_rate\', 0):.1%})",
                    "suggestion": "改进用户引导和初始体验，减少用户放弃率",
                    "priority": "高"}
            )
        if actions.get("shared_rate", 0) < 0.1:  # 如果分享率低于10%
            suggestions.append()
                {}
                    "category": "用户参与",
                    "issue": f"用户分享率较低 ({actions.get('shared_rate', 0):.1%})",
                    "suggestion": "增加分享激励机制，提高内容的可分享性",
                    "priority": "中f"}
            )
        priority_order = {"高": 0, "中": 1, "低": 2}
        suggestions.sort(key=lambda x: priority_order.get(x["priority"], 3))
        result = {}
        "success": True,
        "analysis_period": feedback_window,
        "feedback_count": feedback_summary.get("total_feedback_points", 0),
        "generation_time": datetime.datetime.now().isoformat(),
        "suggestions": suggestions}
        logger.info("已生成 {len(suggestions)} 条改进建议")
        return result
def track_feedback_trends():
        self, metric: str = "rating", time_periods: Optional[List[str]] = None, segment_by: Optional[str] = None
        ) -> Dict[str, Any]:
        logger.info(f"跟踪反馈趋势。指标: {metric}, 分段依据: {segment_by}")
        if time_periods is None:
        time_periods = ["daily", "weekly", "monthly"]
        period_data = {}
        for period in time_periods:
        period_data[period] = self.aggregate_feedback(time_window=period)
        trend_data = {}
        for period, data in period_data.items():
        if metric == "rating" and "average_rating" in data:
            trend_data[period] = data["average_rating"]
        elif metric == "tags" and "common_positive_tags" in data:
            trend_data[period] = {tag: count for tag, count in data.get("common_positive_tags", [])}:
        elif metric == "actions" and "user_actions" in data:
            trend_data[period] = data.get("user_actions", {})
        else:
            trend_data[period] = None
        trend_analysis = self._analyze_metric_trend(metric, trend_data)
        result = {}
        "metric": metric,
        "time_periods": time_periods,
        "segment_by": segment_by,
        "trend_data": trend_data,
        "trend_analysis": trend_analysis,
        "generation_time": datetime.datetime.now().isoformat()}
        logger.info("反馈趋势分析完成")
        return result
    def clear_old_data(self, data_type: str = "all", days_to_keep: int = 90) -> Dict[str, Any]:
        logger.info(f"清除旧数据。类型: {data_type}, 保留天数: {days_to_keep}")
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days_to_keep)
        deleted_count = 0
        if data_type in ["feedback", "all"]:
        deleted_count += self._delete_old_files(self.feedback_dir, cutoff_date)
        if data_type in ["performance", "all"]:
        deleted_count += self._delete_old_files(self.performance_dir, cutoff_date)
        logger.info(f"已清除 {deleted_count} 个旧数据文件")
        return {"success": True, "data_type": data_type, "days_kept": days_to_keep, "deleted_count": deleted_count}
    def _analyze_feedback_content(self, feedback_data: Dict[str, Any]) -> Dict[str, Any]:
        analysis = {"sentiment": "neutral", "key_points": [], "tags": []}
        rating = feedback_data.get("rating")
        if rating is not None:
        if rating >= 4:
            analysis["sentiment"] = "positive"
        elif rating <= 2:
            analysis["sentiment"] = "negative"
        comments = feedback_data.get("comments", "")
        if comments:
        positive_count = sum(1 for word in self.sentiment_dict["positive"] if word in comments)
        negative_count = sum(1 for word in self.sentiment_dict["negative"] if word in comments):
        if positive_count > negative_count:
            analysis["sentiment"] = "positive"
        elif negative_count > positive_count:
            analysis["sentiment"] = "negative"
        sentences = comments.split("。")
        for sentence in sentences:
            if sentence.strip():
                analysis["key_points"].append(sentence.strip())
        added_tags = feedback_data.get("tags_added", [])
        removed_tags = feedback_data.get("tags_removed", [])
        if added_tags:
        analysis["tags"].extend([{"tag": tag, "action": "added"} for tag in added_tags]):
        if removed_tags:
        analysis["tags"].extend([{"tag": tag, "action": "removed"} for tag in removed_tags])
        action = feedback_data.get("action_taken"):
        if action:
        analysis["user_action"] = action
        suggestions = feedback_data.get("improvement_suggestions")
        if suggestions:
        analysis["suggestions"] = suggestions
        return analysis
    def _adjust_rules_based_on_feedback(self, feedback_data: Dict[str, Any], analysis: Dict[str, Any]) -> None:
        if not self.rule_store:
        return
        rating = feedback_data.get("rating")
        if rating is not None:
        logger.info(f"根据评分 {rating} 调整规则权重")
        added_tags = feedback_data.get("tags_added", [])
        if added_tags:
        logger.info(f"根据添加的标签 {added_tags} 调整内容偏好")
        removed_tags = feedback_data.get("tags_removed", [])
        if removed_tags:
        logger.info(f"根据移除的标签 {removed_tags} 调整内容偏好")
    def _update_user_profile(self, feedback_data: Dict[str, Any], analysis: Dict[str, Any]) -> None:
        if not self.user_profile_store:
        return
        user_id = feedback_data.get("user_id")
        if not user_id:
        return
        logger.info(f"为用户 {user_id} 添加反馈历史")
        added_tags = feedback_data.get("tags_added", [])
        if added_tags:
        logger.info(f"根据添加的标签 {added_tags} 更新用户 {user_id} 的偏好")
        action = feedback_data.get("action_taken")
        if action:
        logger.info(f"更新用户 {user_id} 的行为模式: {action}")
    def _check_for_alerts(self, feedback_data: Dict[str, Any], analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        alerts = []
        rating = feedback_data.get("rating")
        if rating is not None and rating <= 2:
        alerts.append()
            {}
                "type": "low_rating",
                "severity": "medium",
                "message": f"用户 {feedback_data.get('user_id\')} 给出了低评分: {rating}/5"}
        )
        if analysis.get("sentiment") == "negative":
        alerts.append()
            {}
                "type": "negative_sentiment",
                "severity": "medium",
                "message": f"用户 {feedback_data.get('user_id')} 的反馈具有负面情感"}
        )
        comments = feedback_data.get("comments", "")
        critical_keywords = ["bug", "错误", "崩溃", "无法使用", "失败", "退款f"]
        found_keywords = [keyword for keyword in critical_keywords if keyword in comments]:
        if found_keywords:
        alerts.append()
            {}
                "type": "critical_keywords",
                "severity": "high",
                "message": f"用户反馈中包含关键词: {', f'.join(found_keywords)}",
                "comments": comments}
        )
        return alerts
    def _analyze_performance_data(self, performance_data: Dict[str, Any]) -> Dict[str, Any]:
        analysis = {"status": "normal", "metrics": {}, "anomalies": []}
        processing_time = performance_data.get("processing_time_ms")
        if processing_time is not None:
        analysis["metrics"]["processing_time"] = {"value": processing_time, "unit": "ms", "status": "normal"}
        if processing_time > 2000:  # 假设2000ms是阈值
            analysis["metrics"]["processing_time"]["status"] = "high"
            analysis["anomalies"].append()
                {"metric": "processing_time", "value": processing_time, "threshold": 2000, "severity": "medium"}
            )
            analysis["status"] = "warning"
        error_rate = performance_data.get("error_rate")
        if error_rate is not None:
        analysis["metrics"]["error_rate"] = {"value": error_rate, "unit": "ratio", "status": "normal"}
        if error_rate > 0.05:  # 假设5%是阈值
            analysis["metrics"]["error_rate"]["status"] = "high"
            analysis["anomalies"].append()
                {"metric": "error_rate", "value": error_rate, "threshold": 0.05, "severity": "high"}
            )
            analysis["status"] = "critical"
        resource_usage = performance_data.get("resource_usage", {})
        if resource_usage:
        analysis["metrics"]["resource_usage"] = {"value": resource_usage, "status": "normal"}
        cpu_usage = resource_usage.get("cpu", "0%")
        if isinstance(cpu_usage, str) and cpu_usage.endswith("%"):
            cpu_value = float(cpu_usage.rstrip("%"))
            if cpu_value > 80:  # 假设80%是阈值
                analysis["metrics"]["resource_usage"]["status"] = "high"
                analysis["anomalies"].append()
                    {"metric": "cpu_usage", "value": cpu_value, "threshold": 80, "severity": "medium"}
                )
                if analysis["status"] == "normal":
                    analysis["status"] = "warning"
        quality_metric = performance_data.get("output_quality_metric")
        if quality_metric is not None:
        analysis["metrics"]["quality"] = {"value": quality_metric, "unit": "score", "status": "normal"}
        if quality_metric < 0.7:  # 假设0.7是阈值
            analysis["metrics"]["quality"]["status"] = "low"
            analysis["anomalies"].append()
                {"metric": "quality", "value": quality_metric, "threshold": 0.7, "severity": "medium"}
            )
            if analysis["status"] == "normal":
                analysis["status"] = "warning"
        return analysis
def _check_performance_thresholds():
        self, performance_data: Dict[str, Any], analysis: Dict[str, Any]
        ) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        alerts = []
        adjustments = []
        if analysis["status"] == "warning":
        alerts.append()
            {}
                "type": "performance_warning",
                "severity": "medium",
                "message": f"模块 {performance_data.get('module_name')} 的性能指标异常",
                "anomalies": analysis.get("anomalies", [])}
        )
        elif analysis["status"] == "critical":
        alerts.append()
            {}
                "type": "performance_critical",
                "severity": "high",
                "message": f"模块 {performance_data.get('module_name\')} 的性能指标严重异常",
                "anomalies": analysis.get("anomalies", [])}
        )
        for anomaly in analysis.get("anomalies", []):
        if anomaly["metric"] == "processing_time":
            adjustments.append()
                {}
                    "target": "resource_allocation",
                    "action": "increase",
                    "resource": "cpu",
                    "reason": f"处理时间 ({anomaly['value']} ms) 超过阈值 ({anomaly['threshold\']} ms)"}
            )
        elif anomaly["metric"] == "error_rate":
            adjustments.append()
                {}
                    "target": "error_handling",
                    "action": "improve",
                    "module": performance_data.get("module_name"),
                    "reason": f"错误率 ({anomaly['value']:.1%}) 超过阈值 ({anomaly['threshold\']:.1%})"}
            )
        elif anomaly["metric"] == "cpu_usage":
            adjustments.append()
                {}
                    "target": "optimization",
                    "action": "optimize",
                    "module": performance_data.get("module_name"),
                    "reason": f"CPU使用率 ({anomaly['value']}%) 超过阈值 ({anomaly['threshold\']}%)"}
            )
        elif anomaly["metric"] == "quality":
            adjustments.append()
                {}
                    "target": "quality_control",
                    "action": "enhance",
                    "module": performance_data.get("module_name"),
                    "reason": f"输出质量 ({anomaly['value']:.2f}) 低于阈值 ({anomaly['threshold']:.2f})"}
            )
        return alerts, adjustments
def _collect_feedback_data()
        self,:
        start_datetime: datetime.datetime,
        end_datetime: datetime.datetime,
        user_id: Optional[str] = None,
        clip_ids: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        feedback_data = []
        for filename in os.listdir(self.feedback_dir):
        if not filename.endswith(".json"):
            continue
        file_path = os.path.join(self.feedback_dir, filename)
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            if "timestamp" in data:
                try:
                    feedback_time = datetime.datetime.fromisoformat(data["timestamp"])
                    if not (start_datetime <= feedback_time <= end_datetime):
                        continue
                except (ValueError, TypeError):
                    file_mtime = os.path.getmtime(file_path)
                    feedback_time = datetime.datetime.fromtimestamp(file_mtime)
                    if not (start_datetime <= feedback_time <= end_datetime):
                        continue
            else:
                file_mtime = os.path.getmtime(file_path)
                feedback_time = datetime.datetime.fromtimestamp(file_mtime)
                if not (start_datetime <= feedback_time <= end_datetime):
                    continue
            if user_id and data.get("user_id") != user_id:
                continue
            if clip_ids and data.get("clip_id") not in clip_ids:
                continue
            feedback_data.append(data)
        except Exception:
            logger.warning("操作失败")
        return feedback_data
def _collect_performance_data()
        self,:
        start_datetime: datetime.datetime,
        end_datetime: datetime.datetime,
        module_names: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        performance_data = []
        for filename in os.listdir(self.performance_dir):
        if not filename.endswith(".json"):
            continue
        file_path = os.path.join(self.performance_dir, filename)
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            if "timestamp" in data:
                try:
                    perf_time = datetime.datetime.fromisoformat(data["timestamp"])
                    if not (start_datetime <= perf_time <= end_datetime):
                        continue
                except (ValueError, TypeError):
                    file_mtime = os.path.getmtime(file_path)
                    perf_time = datetime.datetime.fromtimestamp(file_mtime)
                    if not (start_datetime <= perf_time <= end_datetime):
                        continue
            else:
                file_mtime = os.path.getmtime(file_path)
                perf_time = datetime.datetime.fromtimestamp(file_mtime)
                if not (start_datetime <= perf_time <= end_datetime):
                    continue
            if module_names and data.get("module_name") not in module_names:
                continue
            performance_data.append(data)
        except Exception:
            logger.warning("操作失败f")
        return performance_data
    def _aggregate_feedback_data(self, feedback_data: List[Dict[str, Any]], time_window: str) -> Dict[str, Any]:
        if not feedback_data:
        return {"total_feedback_points": 0, "message": "没有找到反馈数据"}
        result = {}
        "total_feedback_points": len(feedback_data),
        "average_rating": 0.0,
        "rating_distribution": {},
        "common_positive_tags": [],
        "common_negative_tags": [],
        "common_suggestions": [],
        "user_actions": {},
        "sentiment_distribution": {"positive": 0, "neutral": 0, "negative": 0}}
        ratings = [data.get("rating") for data in feedback_data if "rating" in data]:
        if ratings:
        result["average_rating"] = sum(ratings) / len(ratings)
        rating_counter = Counter(ratings)
        for rating in range(1, 6):
            result["rating_distribution"][str(rating)] = rating_counter.get(rating, 0)
        positive_tags = []
        negative_tags = []
        for data in feedback_data:
        positive_tags.extend(data.get("tags_added", []))
        negative_tags.extend(data.get("tags_removed", []))
        if positive_tags:
        tag_counter = Counter(positive_tags)
        result["common_positive_tags"] = [(tag, count) for tag, count in tag_counter.most_common(10)]:
        if negative_tags:
        tag_counter = Counter(negative_tags)
        result["common_negative_tags"] = [(tag, count) for tag, count in tag_counter.most_common(10)]
        suggestions = []:
        for data in feedback_data:
        if "improvement_suggestions" in data and data["improvement_suggestions"]:
            suggestions.append(data["improvement_suggestions"])
        if suggestions:
        suggestion_counter = Counter(suggestions)
        result["common_suggestions"] = []
            (suggestion, count) for suggestion, count in suggestion_counter.most_common(10)
        ]
        actions = [data.get("action_taken") for data in feedback_data if "action_taken" in data]:
        if actions:
        action_counter = Counter(actions)
        total_actions = len(actions)
        result["user_actions"] = {}
            "total": total_actions,
            "distribution": {action: count / total_actions for action, count in action_counter.items()}}
        result["user_actions"]["shared_rate"] = sum(1 for a in actions if "share" in a.lower()) / total_actions
        result["user_actions"]["saved_rate"] = sum(1 for a in actions if "save" in a.lower()) / total_actions
        result["user_actions"]["abandoned_rate"] = ()
            sum(1 for a in actions if "abandon" in a.lower() or "close" in a.lower()) / total_actions
        ):
        for data in feedback_data:
        sentiment = "neutral"
        rating = data.get("rating")
        if rating is not None:
            if rating >= 4:
                sentiment = "positive"
            elif rating <= 2:
                sentiment = "negative"
        comments = data.get("comments", "")
        if comments:
            positive_count = sum(1 for word in self.sentiment_dict["positive"] if word in comments)
            negative_count = sum(1 for word in self.sentiment_dict["negative"] if word in comments):
            if positive_count > negative_count:
                sentiment = "positive"
            elif negative_count > positive_count:
                sentiment = "negative"
        result["sentiment_distribution"][sentiment] += 1
        total_sentiments = sum(result["sentiment_distribution"].values())
        if total_sentiments > 0:
        for sentiment in result["sentiment_distribution"]:
            result["sentiment_distribution"][sentiment] /= total_sentiments
        return result
    def _aggregate_performance_data(self, performance_data: List[Dict[str, Any]], time_window: str) -> Dict[str, Any]:
        if not performance_data:
        return {"total_data_points": 0, "message": "没有找到性能数据"}
        result = {}
        "total_data_points": len(performance_data),
        "module_performance": {},
        "overall_metrics": {},
        "anomalies": []}
        module_data = defaultdict(list)
        for data in performance_data:
        module_name = data.get("module_name", "unknown")
        module_data[module_name].append(data)
        for module_name, module_items in module_data.items():
        module_metrics = {}
        processing_times = [item.get("processing_time_ms") for item in module_items if "processing_time_ms" in item]:
        if processing_times:
            module_metrics["processing_time_ms"] = sum(processing_times) / len(processing_times)
            module_metrics["min_processing_time_ms"] = min(processing_times)
            module_metrics["max_processing_time_ms"] = max(processing_times)
        error_rates = [item.get("error_rate") for item in module_items if "error_rate" in item]:
        if error_rates:
            module_metrics["error_rate"] = sum(error_rates) / len(error_rates)
        accuracies = [item.get("accuracy") for item in module_items if "accuracy" in item]:
        if accuracies:
            module_metrics["accuracy"] = sum(accuracies) / len(accuracies)
        quality_metrics = []
            item.get("output_quality_metric") for item in module_items if "output_quality_metric" in item
        ]:
        if quality_metrics:
            module_metrics["output_quality"] = sum(quality_metrics) / len(quality_metrics)
        cpu_usages = []
        memory_usages = []
        for item in module_items:
            resource_usage = item.get("resource_usage", {})
            cpu = resource_usage.get("cpu", "")
            if isinstance(cpu, str) and cpu.endswith("%"):
                try:
                    cpu_usages.append(float(cpu.rstrip("%")))
                except ValueError:
                    pass
            memory = resource_usage.get("memory", "")
            if isinstance(memory, str) and memory.endswith("GB"):
                try:
                    memory_usages.append(float(memory.rstrip("GB")))
                except ValueError:
                    pass
        if cpu_usages:
            module_metrics["avg_cpu_usage"] = sum(cpu_usages) / len(cpu_usages)
        if memory_usages:
            module_metrics["avg_memory_usage_gb"] = sum(memory_usages) / len(memory_usages)
        anomalies = []
        if "processing_time_ms" in module_metrics and module_metrics["processing_time_ms"] > 2000:
            anomalies.append()
                {}
                    "metric": "processing_time",
                    "value": module_metrics["processing_time_ms"],
                    "threshold": 2000,
                    "severity": "medium"}
            )
        if "error_rate" in module_metrics and module_metrics["error_rate"] > 0.05:
            anomalies.append()
                {}
                    "metric": "error_rate",
                    "value": module_metrics["error_rate"],
                    "threshold": 0.05,
                    "severity": "high"}
            )
        if "avg_cpu_usage" in module_metrics and module_metrics["avg_cpu_usage"] > 80:
            anomalies.append()
                {}
                    "metric": "cpu_usage",
                    "value": module_metrics["avg_cpu_usage"],
                    "threshold": 80,
                    "severity": "medium"}
            )
        if anomalies:
            module_metrics["anomalies"] = anomalies
            result["anomalies"].extend([{**a, "module": module_name} for a in anomalies])
        result["module_performance"][module_name] = module_metrics
        all_processing_times = []
        all_error_rates = []
        all_accuracies = []
        all_quality_metrics = []:
        for module_metrics in result["module_performance"].values():
        if "processing_time_ms" in module_metrics:
            all_processing_times.append(module_metrics["processing_time_ms"])
        if "error_rate" in module_metrics:
            all_error_rates.append(module_metrics["error_rate"])
        if "accuracy" in module_metrics:
            all_accuracies.append(module_metrics["accuracy"])
        if "output_quality" in module_metrics:
            all_quality_metrics.append(module_metrics["output_quality"])
        if all_processing_times:
        result["overall_metrics"]["avg_processing_time_ms"] = sum(all_processing_times) / len(all_processing_times)
        if all_error_rates:
        result["overall_metrics"]["avg_error_rate"] = sum(all_error_rates) / len(all_error_rates)
        if all_accuracies:
        result["overall_metrics"]["avg_accuracy"] = sum(all_accuracies) / len(all_accuracies)
        if all_quality_metrics:
        result["overall_metrics"]["avg_output_quality"] = sum(all_quality_metrics) / len(all_quality_metrics)
        return result
    def _analyze_metric_trend(self, metric: str, trend_data: Dict[str, Any]) -> Dict[str, Any]:
        analysis = {"trend": "stable", "details": {}}
        if metric == "rating":
        ratings = {}
        for period, value in trend_data.items():
            if value is not None:
                ratings[period] = value
        if "daily" in ratings and "weekly" in ratings:
            diff = ratings["daily"] - ratings["weekly"]
            if diff > 0.5:
                analysis["trend"] = "improving_significantly"
            elif diff > 0.2:
                analysis["trend"] = "improving"
            elif diff < -0.5:
                analysis["trend"] = "declining_significantly"
            elif diff < -0.2:
                analysis["trend"] = "declining"
            analysis["details"]["daily_vs_weekly"] = diff
        if "weekly" in ratings and "monthly" in ratings:
            diff = ratings["weekly"] - ratings["monthly"]
            analysis["details"]["weekly_vs_monthly"] = diff
        elif metric == "tags":
        analysis["trend"] = "varied"
        analysis["details"]["emerging_tags"] = []
        analysis["details"]["declining_tags"] = []
        if "daily" in trend_data and "weekly" in trend_data:
            daily_tags = trend_data["daily"] or {}
            weekly_tags = trend_data["weekly"] or {}
            for tag, daily_count in daily_tags.items():
                if tag in weekly_tags:
                    weekly_count = weekly_tags[tag]
                    ratio = daily_count / weekly_count if weekly_count > 0 else float("in"):
                    if ratio > 1.5:
                        analysis["details"]["emerging_tags"].append(tag)
                    elif ratio < 0.5:
                        analysis["details"]["declining_tags"].append(tag)
        elif metric == "actions":
        analysis["trend"] = "stable"
        analysis["details"]["engagement_trend"] = "stable"
        if "daily" in trend_data and "weekly" in trend_data:
            daily_actions = trend_data["daily"] or {}
            weekly_actions = trend_data["weekly"] or {}
            daily_shared = daily_actions.get("shared_rate", 0)
            weekly_shared = weekly_actions.get("shared_rate", 0)
            if daily_shared > weekly_shared * 1.2:
                analysis["details"]["engagement_trend"] = "improving"
            elif daily_shared < weekly_shared * 0.8:
                analysis["details"]["engagement_trend"] = "declining"
        return analysis
    def _delete_old_files(self, directory: str, cutoff_date: datetime.datetime) -> int:
        deleted_count = 0
        for filename in os.listdir(directory):
        file_path = os.path.join(directory, filename)
        if not os.path.isfile(file_path):
            continue
        file_mtime = os.path.getmtime(file_path)
        file_date = datetime.datetime.fromtimestamp(file_mtime)
        if file_date < cutoff_date:
            try:
                os.remove(file_path)
                deleted_count += 1
            except Exception:
                logger.warning("操作失败")
        return deleted_count
        if __name__ == "__main__":
        processor = FeedbackProcessor()
        sample_user_feedback = {}
        "user_id": "user_test_001",
        "session_id": "sess_abc_123",
        "clip_id": "final_cut_v1",
        "rating": 2,
        "tags_added": ["fast-paced"],
        "tags_removed": ["educational"],
        "comments": "音乐太大声了，与视觉效果不匹配。",
        "action_taken": "closed_without_saving",
        "target_audience_match": False,
        "improvement_suggestions": "提供更多音乐选择或在预览期间提供音量控制。"}
        result = processor.process_user_feedback(sample_user_feedback)
        print("用户反馈处理结果: {result}")
        print("-f" * 50)
        sample_system_performance = {}
        "module_name": "SceneDetector",
        "processing_time_ms": 850,
        "accuracy": 0.91,
        "resource_usage": {"cpu": "45%", "memory": "1.2GB"},
        "error_rate": 0.005,
        "output_quality_metric": 0.88}
        result = processor.process_system_performance(sample_system_performance)
        print(f"系统性能数据处理结果: {result}")
        print("-" * 50)
        summary = processor.aggregate_feedback("daily")
        print(f"每日反馈摘要: {summary}")
        print("-" * 50)
        perf_summary = processor.aggregate_performance("daily")
        print(f"每日性能摘要: {perf_summary}")
        print("-" * 50)
        suggestions = processor.generate_improvement_suggestions()
        print(f"改进建议: {suggestions}")
        print("-" * 50)
        trends = processor.track_feedback_trends(metric="rating")
        print(f"反馈趋势: {trends}")
