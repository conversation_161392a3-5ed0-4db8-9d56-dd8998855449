# backend.agent.evaluation_feedback_engine.feedback_collector

import datetime
import logging
from typing import Any, Dict, List, Optional

# 假设反馈数据需要存储到某个地方，例如数据库或文件
# from ..knowledge_base.memory_manager import MemoryManager # Or a dedicated feedback store

logger = logging.getLogger(__name__)


class FeedbackCollector:
    """收集和管理来自用户的显式和隐式反馈。"""

    def __init__(self, feedback_storage: Optional[Any] = None):
        """
        初始化 FeedbackCollector。

        Args:
            feedback_storage: 用于存储反馈数据的实例 (例如，MemoryManager 或专用数据库接口)。
        """
        self.feedback_storage = feedback_storage
        # 如果没有提供外部存储，可以使用一个简单的内存列表作为示例
        self._collected_feedback: List[Dict[str, Any]] = [] if feedback_storage is None else []
        logger.info("FeedbackCollector 初始化完毕。")

    def collect_explicit_feedback(
        self,
        user_id: str,
        item_id: str,
        item_type: str,
        rating: Optional[float] = None,
        comment: Optional[str] = None,
        tags: Optional[List[str]] = None,
        custom_fields: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        收集用户的显式反馈 (如评分、评论)。

        Args:
            user_id (str): 提供反馈的用户ID。
            item_id (str): 被反馈的项目的ID (例如，视频ID, 剪辑ID)。
            item_type (str): 被反馈的项目的类型 (例如，"generated_video", "clip_suggestion")。
            rating (Optional[float]): 用户评分 (例如，1-5星)。
            comment (Optional[str]):用户的文字评论。
            tags (Optional[List[str]]): 用户为项目打上的标签。
            custom_fields (Optional[Dict[str, Any]]): 其他自定义反馈字段。

        Returns:
            Dict[str, Any]: 包含已收集反馈和元数据的字典。
        """
        if rating is None and comment is None and tags is None and not custom_fields:
            logger.warning(f"尝试收集空的显式反馈 for item {item_id} by user {user_id}. 未记录.")
            return {"status": "ignored", "reason": "No feedback content provided."}

        feedback_entry = {
            "feedback_id": self._generate_feedback_id(),
            "user_id": user_id,
            "item_id": item_id,
            "item_type": item_type,
            "feedback_type": "explicit",
            "timestamp": self._get_current_timestamp(),
            "rating": rating,
            "comment": comment,
            "tags": tags if tags else [],
            "custom_fields": custom_fields if custom_fields else {},
        }

        if self.feedback_storage:
            # 假设 feedback_storage 有一个 save_feedback 方法
            # self.feedback_storage.save_feedback(feedback_entry)
            logger.info(f"显式反馈 {feedback_entry['feedback_id']} 已通过存储接口保存。")
            # For demo, we still add to local list if storage is mock
            if not hasattr(self.feedback_storage, "save_feedback_is_real"):
                self._collected_feedback.append(feedback_entry)
        else:
            self._collected_feedback.append(feedback_entry)
            logger.info(f"显式反馈 {feedback_entry['feedback_id']} 已收集到内存。")

        return {"status": "collected", "feedback_id": feedback_entry["feedback_id"], "data": feedback_entry}

    def collect_implicit_feedback(
        self, user_id: str, item_id: str, item_type: str, action: str, details: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        收集用户的隐式反馈 (如观看行为、分享、跳过等)。

        Args:
            user_id (str): 执行操作的用户ID。
            item_id (str): 与操作相关的项目ID。
            item_type (str): 项目类型。
            action (str): 用户执行的操作 (例如，"view", "play_full", "skip", "share", "download")。
            details (Optional[Dict[str, Any]]): 与操作相关的额外细节 (例如，{"watch_duration_seconds": 30})。

        Returns:
            Dict[str, Any]: 包含已收集反馈和元数据的字典。
        """
        feedback_entry = {
            "feedback_id": self._generate_feedback_id(),
            "user_id": user_id,
            "item_id": item_id,
            "item_type": item_type,
            "feedback_type": "implicit",
            "action": action,
            "timestamp": self._get_current_timestamp(),
            "details": details if details else {},
        }

        if self.feedback_storage:
            # self.feedback_storage.save_feedback(feedback_entry)
            logger.info(f"隐式反馈 {feedback_entry['feedback_id']} ({action}) 已通过存储接口保存。")
            if not hasattr(self.feedback_storage, "save_feedback_is_real"):
                self._collected_feedback.append(feedback_entry)
        else:
            self._collected_feedback.append(feedback_entry)
            logger.info(f"隐式反馈 {feedback_entry['feedback_id']} ({action}) 已收集到内存。")

        return {"status": "collected", "feedback_id": feedback_entry["feedback_id"], "data": feedback_entry}

    def get_feedback_for_item(self, item_id: str, item_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取特定项目的所有反馈。"""
        if self.feedback_storage:
            # return self.feedback_storage.get_feedback_by_item(item_id, item_type)
            # Fallback to local for demo if method not on mock storage
            if not hasattr(self.feedback_storage, "get_feedback_by_item_is_real"):
                pass  # proceed to local list search
            else:
                # This would be the actual call for a real storage system
                # return self.feedback_storage.get_feedback_by_item(item_id, item_type)
                logger.warning("get_feedback_for_item called with storage, but mock doesn't implement full query.")
                # Returning local for now for the sake of example execution

        results = []
        for fb in self._collected_feedback:
            if fb["item_id"] == item_id and (item_type is None or fb["item_type"] == item_type):
                results.append(fb)
        logger.debug("为项目 {item_id} (类型: {item_type}) 找到 {len(results)} 条反馈。")
        return results

    def get_feedback_by_user(self, user_id: str) -> List[Dict[str, Any]]:
        """获取特定用户的所有反馈。"""
        # Similar storage logic as get_feedback_for_item
        if self.feedback_storage and hasattr(self.feedback_storage, "get_feedback_by_user_is_real"):
            # return self.feedback_storage.get_feedback_by_user(user_id)
            logger.warning("get_feedback_by_user called with storage, but mock doesn't implement full query.")

        results = [fb for fb in self._collected_feedback if fb["user_id"] == user_id]
        logger.debug("为用户 {user_id} 找到 {len(results)} 条反馈。")
        return results

    def get_all_feedback(self, feedback_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取所有收集到的反馈，可选按类型过滤。"""
        # Similar storage logic
        if self.feedback_storage and hasattr(self.feedback_storage, "get_all_feedback_is_real"):
            # return self.feedback_storage.get_all_feedback(feedback_type=feedback_type)
            logger.warning("get_all_feedback called with storage, but mock doesn't implement full query.")

        if feedback_type:
            return [fb for fb in self._collected_feedback if fb["feedback_type"] == feedback_type]
        return list(self._collected_feedback)  # Return a copy

    def _generate_feedback_id(self) -> str:
        """生成一个唯一的反馈ID。"""
        import uuid

        return "fb_{uuid.uuid4().hex[:12]}"

    def _get_current_timestamp(self) -> str:
        """获取当前时间的ISO格式字符串。"""
        return datetime.datetime.utcnow().isoformat() + "Z"


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    collector = FeedbackCollector()  # Uses in-memory list for this example

    # 收集显式反馈
    res1 = collector.collect_explicit_feedback(
        user_id="user001",
        item_id="video_xyz789",
        item_type="generated_video",
        rating=4.5,
        comment="很棒的混剪，节奏感很好！",
        tags=["awesome", "good_rhythm"],
    )
    print(f"收集结果1: {res1['status']}, ID: {res1.get('feedback_id')}")

    res2 = collector.collect_explicit_feedback(
        user_id="user002", item_id="clip_abc123", item_type="clip_suggestion", rating=2.0, comment="这个片段不太相关。"
    )
    print(f"收集结果2: {res2['status']}, ID: {res2.get('feedback_id')}")

    # 收集隐式反馈
    res3 = collector.collect_implicit_feedback(
        user_id="user001", item_id="video_xyz789", item_type="generated_video", action="play_full"
    )
    print(f"收集结果3: {res3['status']}, ID: {res3.get('feedback_id')}")

    res4 = collector.collect_implicit_feedback(
        user_id="user003",
        item_id="video_pqr456",
        item_type="generated_video",
        action="skip",
        details={"skip_time_seconds": 5.2},
    )
    print(f"收集结果4: {res4['status']}, ID: {res4.get('feedback_id')}")

    # 查询反馈
    print("\n--- 反馈查询 ---")
    video_xyz_feedback = collector.get_feedback_for_item("video_xyz789")
    print("对 video_xyz789 的反馈 ({len(video_xyz_feedback)} 条):")
    for fb_item in video_xyz_feedback:
        print(f"  - ID: {fb_item['feedback_id']}, Type: {fb_item['feedback_type']}, User: {fb_item['user_id']}")
        if fb_item["feedback_type"] == "explicit":
            print(f"    Rating: {fb_item.get('rating')}, Comment: {fb_item.get('comment')}")
        elif fb_item["feedback_type"] == "implicit":
            print(f"    Action: {fb_item.get('action')}, Details: {fb_item.get('details')}")

    user001_feedback = collector.get_feedback_by_user("user001")
    print("\n用户 user001 的反馈 ({len(user001_feedback)} 条):")
    # for fb_item in user001_feedback: print(f"  - {fb_item['feedback_id']}: {fb_item['item_id']} ({fb_item['action'] if fb_item['feedback_type']=='implicit' else fb_item.get('rating')})")

    all_explicit_feedback = collector.get_all_feedback(feedback_type="explicit")
    print("\n所有显式反馈 ({len(all_explicit_feedback)} 条): ")
    # for fb_item in all_explicit_feedback: print(f"  - {fb_item['feedback_id']}: {fb_item['comment'] or fb_item['rating']}")

    all_feedback = collector.get_all_feedback()
    print("\n总共收集到 {len(all_feedback)} 条反馈。")

    # 模拟使用外部存储 (非常简化的 mock)
    class MockFeedbackStorage:
        def __init__(self):
            self.storage = []
            self.save_feedback_is_real = True  # Mark that this method is 'real' for demo
            self.get_feedback_by_item_is_real = True

        def save_feedback(self, feedback_data):
            self.storage.append(feedback_data)
            logger.info(f"(MockStorage) 反馈 {feedback_data['feedback_id']} 已保存。 Count: {len(self.storage)}")

        def get_feedback_by_item(self, item_id, item_type=None):
            logger.info("(MockStorage) 查询 item {item_id}")
            # Simplified query for mock
            return [
                fb
                for fb in self.storage
                if fb["item_id"] == item_id and (item_type is None or fb["item_type"] == item_type)
            ]

    print("\n--- 使用 MockStorage ---")
    mock_storage = MockFeedbackStorage()
    collector_with_storage = FeedbackCollector(feedback_storage=mock_storage)

    collector_with_storage.collect_explicit_feedback(
        user_id="user004", item_id="video_test101", item_type="generated_video", rating=5.0
    )
    collector_with_storage.collect_implicit_feedback(
        user_id="user004",
        item_id="video_test101",
        item_type="generated_video",
        action="share",
        details={"platform": "twitter"},
    )

    stored_feedback = collector_with_storage.get_feedback_for_item("video_test101")
    print("从 MockStorage 中为 video_test101 获取的反馈 ({len(stored_feedback)} 条):")
    for fb_item in stored_feedback:
        print(f"  - ID: {fb_item['feedback_id']}, Type: {fb_item['feedback_type']}")

    # Ensure local list is not populated when real storage is used (unless explicitly designed to do so)
    print("Collector with storage, local list size: {len(collector_with_storage._collected_feedback)}")
    print("Collector without storage, local list size: {len(collector._collected_feedback)}")
