# IntelliCutAgent 修复实施指南

## 🎯 修复策略

### 总体原则
1. **安全第一**: 优先修复安全漏洞
2. **稳定性优先**: 确保系统基本功能可用
3. **渐进式改进**: 分阶段实施，避免大规模重构
4. **测试驱动**: 每个修复都要有对应的测试

### 实施顺序
1. 创建缺失模块的基础实现
2. 修复安全漏洞
3. 解决循环导入问题
4. 完善错误处理
5. 添加测试覆盖
6. 性能优化

## 🔧 具体实施步骤

### 第一阶段: 创建缺失模块 (Day 1-3)

#### 1.1 创建场景检测模块
```python
# backend/agent/smart_editor/scene_detection.py
import cv2
import numpy as np
from typing import List, Tuple
import logging

logger = logging.getLogger(__name__)

def extract_scenes_threshold(cap, fps: float, threshold: float = 30.0, 
                           min_scene_length: float = 1.0) -> List[Tuple[float, float]]:
    """
    基于阈值的场景检测
    
    Args:
        cap: OpenCV视频捕获对象
        fps: 视频帧率
        threshold: 场景变化阈值
        min_scene_length: 最小场景长度(秒)
    
    Returns:
        场景列表，每个场景为(开始时间, 结束时间)元组
    """
    scenes = []
    prev_frame = None
    scene_start = 0.0
    frame_count = 0
    
    try:
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # 转换为灰度图
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            if prev_frame is not None:
                # 计算帧差
                diff = cv2.absdiff(prev_frame, gray)
                mean_diff = np.mean(diff)
                
                # 检测场景变化
                if mean_diff > threshold:
                    current_time = frame_count / fps
                    scene_length = current_time - scene_start
                    
                    # 如果场景长度满足最小要求，添加到列表
                    if scene_length >= min_scene_length:
                        scenes.append((scene_start, current_time))
                    
                    scene_start = current_time
            
            prev_frame = gray.copy()
            frame_count += 1
        
        # 添加最后一个场景
        final_time = frame_count / fps
        if final_time - scene_start >= min_scene_length:
            scenes.append((scene_start, final_time))
        
        logger.info(f"检测到 {len(scenes)} 个场景")
        return scenes
        
    except Exception as e:
        logger.error(f"场景检测失败: {e}")
        return []

def extract_scenes_content(cap, fps: float, threshold: float = 30.0,
                         min_scene_length: float = 1.0) -> List[Tuple[float, float]]:
    """基于内容的场景检测 - 简化实现"""
    # 暂时使用阈值方法的实现
    return extract_scenes_threshold(cap, fps, threshold, min_scene_length)

def extract_scenes_edge(cap, fps: float, threshold: float = 30.0,
                       min_scene_length: float = 1.0) -> List[Tuple[float, float]]:
    """基于边缘的场景检测 - 简化实现"""
    # 暂时使用阈值方法的实现
    return extract_scenes_threshold(cap, fps, threshold, min_scene_length)
```

#### 1.2 创建视频摘要模块
```python
# backend/agent/smart_editor/video_summarizer.py
import logging
from typing import List, Dict, Any, Optional
import moviepy.editor as mp

logger = logging.getLogger(__name__)

class VideoSummarizer:
    """视频摘要生成器"""
    
    def __init__(self):
        self.supported_methods = ['highlight', 'random', 'uniform']
    
    def generate_summary(self, video_path: str, target_duration: float = 30.0,
                        method: str = 'highlight') -> Optional[str]:
        """
        生成视频摘要
        
        Args:
            video_path: 输入视频路径
            target_duration: 目标摘要时长(秒)
            method: 摘要生成方法
        
        Returns:
            摘要视频路径，失败返回None
        """
        try:
            if method not in self.supported_methods:
                logger.error(f"不支持的摘要方法: {method}")
                return None
            
            # 加载视频
            video = mp.VideoFileClip(video_path)
            
            if video.duration <= target_duration:
                logger.info("视频时长已小于目标时长，返回原视频")
                return video_path
            
            # 根据方法生成摘要
            if method == 'highlight':
                summary_clip = self._generate_highlight_summary(video, target_duration)
            elif method == 'random':
                summary_clip = self._generate_random_summary(video, target_duration)
            else:  # uniform
                summary_clip = self._generate_uniform_summary(video, target_duration)
            
            # 保存摘要视频
            output_path = video_path.replace('.mp4', '_summary.mp4')
            summary_clip.write_videofile(output_path, verbose=False, logger=None)
            
            # 清理资源
            video.close()
            summary_clip.close()
            
            logger.info(f"视频摘要生成成功: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"视频摘要生成失败: {e}")
            return None
    
    def _generate_highlight_summary(self, video: mp.VideoFileClip, 
                                  target_duration: float) -> mp.VideoFileClip:
        """生成精彩片段摘要"""
        # 简化实现：取视频中间部分
        start_time = (video.duration - target_duration) / 2
        end_time = start_time + target_duration
        return video.subclip(start_time, end_time)
    
    def _generate_random_summary(self, video: mp.VideoFileClip,
                               target_duration: float) -> mp.VideoFileClip:
        """生成随机片段摘要"""
        import random
        
        # 随机选择开始时间
        max_start = video.duration - target_duration
        start_time = random.uniform(0, max_start)
        end_time = start_time + target_duration
        return video.subclip(start_time, end_time)
    
    def _generate_uniform_summary(self, video: mp.VideoFileClip,
                                target_duration: float) -> mp.VideoFileClip:
        """生成均匀分布摘要"""
        # 均匀采样多个片段
        num_clips = 3
        clip_duration = target_duration / num_clips
        interval = video.duration / num_clips
        
        clips = []
        for i in range(num_clips):
            start_time = i * interval
            end_time = start_time + clip_duration
            if end_time <= video.duration:
                clips.append(video.subclip(start_time, end_time))
        
        return mp.concatenate_videoclips(clips)

# 添加方法到VideoEditor类
def add_summary_method_to_video_editor():
    """将摘要方法添加到VideoEditor类"""
    from backend.agent.smart_editor.video_editor import VideoEditor
    
    def generate_video_summary(self, video_path: str, target_duration: float = 30.0,
                             method: str = 'highlight') -> Optional[str]:
        """生成视频摘要"""
        summarizer = VideoSummarizer()
        return summarizer.generate_summary(video_path, target_duration, method)
    
    # 动态添加方法
    VideoEditor.generate_video_summary = generate_video_summary
```

#### 1.3 创建并行处理模块
```python
# backend/agent/smart_editor/parallel_processor.py
import logging
import asyncio
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from typing import List, Dict, Any, Callable, Optional
import multiprocessing

logger = logging.getLogger(__name__)

class ParallelVideoProcessor:
    """并行视频处理器"""
    
    def __init__(self, max_workers: Optional[int] = None):
        """
        初始化并行处理器
        
        Args:
            max_workers: 最大工作线程数，None表示使用CPU核心数
        """
        if max_workers is None:
            max_workers = multiprocessing.cpu_count()
        
        self.max_workers = max_workers
        self.thread_executor = ThreadPoolExecutor(max_workers=max_workers)
        self.process_executor = ProcessPoolExecutor(max_workers=max_workers)
        
        logger.info(f"并行处理器初始化完成，最大工作线程数: {max_workers}")
    
    async def process_videos_async(self, video_paths: List[str], 
                                 process_func: Callable, 
                                 use_processes: bool = False,
                                 **kwargs) -> List[Any]:
        """
        异步并行处理多个视频
        
        Args:
            video_paths: 视频文件路径列表
            process_func: 处理函数
            use_processes: 是否使用进程池(CPU密集型任务)
            **kwargs: 传递给处理函数的额外参数
        
        Returns:
            处理结果列表
        """
        try:
            executor = self.process_executor if use_processes else self.thread_executor
            loop = asyncio.get_event_loop()
            
            # 创建任务列表
            tasks = []
            for video_path in video_paths:
                task = loop.run_in_executor(
                    executor, 
                    process_func, 
                    video_path, 
                    **kwargs
                )
                tasks.append(task)
            
            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"处理视频 {video_paths[i]} 失败: {result}")
                    processed_results.append(None)
                else:
                    processed_results.append(result)
            
            logger.info(f"并行处理完成，成功: {len([r for r in processed_results if r is not None])}/{len(video_paths)}")
            return processed_results
            
        except Exception as e:
            logger.error(f"并行处理失败: {e}")
            return [None] * len(video_paths)
    
    def process_videos_sync(self, video_paths: List[str],
                          process_func: Callable,
                          use_processes: bool = False,
                          **kwargs) -> List[Any]:
        """
        同步并行处理多个视频
        
        Args:
            video_paths: 视频文件路径列表
            process_func: 处理函数
            use_processes: 是否使用进程池
            **kwargs: 传递给处理函数的额外参数
        
        Returns:
            处理结果列表
        """
        try:
            executor = self.process_executor if use_processes else self.thread_executor
            
            # 提交所有任务
            futures = []
            for video_path in video_paths:
                future = executor.submit(process_func, video_path, **kwargs)
                futures.append(future)
            
            # 收集结果
            results = []
            for i, future in enumerate(futures):
                try:
                    result = future.result(timeout=300)  # 5分钟超时
                    results.append(result)
                except Exception as e:
                    logger.error(f"处理视频 {video_paths[i]} 失败: {e}")
                    results.append(None)
            
            logger.info(f"并行处理完成，成功: {len([r for r in results if r is not None])}/{len(video_paths)}")
            return results
            
        except Exception as e:
            logger.error(f"并行处理失败: {e}")
            return [None] * len(video_paths)
    
    def cleanup(self):
        """清理资源"""
        try:
            self.thread_executor.shutdown(wait=True)
            self.process_executor.shutdown(wait=True)
            logger.info("并行处理器资源清理完成")
        except Exception as e:
            logger.error(f"资源清理失败: {e}")
    
    def __del__(self):
        """析构函数"""
        self.cleanup()
```

### 第二阶段: 安全加固 (Day 4)

#### 2.1 创建安全文件上传模块
```python
# backend/security/file_upload.py
import os
import hashlib
import time
import magic
from pathlib import Path
from typing import Optional, Set
import logging

logger = logging.getLogger(__name__)

class SecureFileUpload:
    """安全文件上传处理器"""
    
    # 允许的MIME类型
    ALLOWED_MIME_TYPES: Set[str] = {
        'video/mp4',
        'video/avi', 
        'video/quicktime',
        'video/x-msvideo',
        'video/webm',
        'video/x-matroska'
    }
    
    # 允许的文件扩展名
    ALLOWED_EXTENSIONS: Set[str] = {
        '.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv'
    }
    
    # 最大文件大小 (500MB)
    MAX_FILE_SIZE: int = 500 * 1024 * 1024
    
    @classmethod
    def validate_file(cls, file_content: bytes, filename: str) -> bool:
        """
        验证上传文件的安全性
        
        Args:
            file_content: 文件内容
            filename: 文件名
            
        Returns:
            验证通过返回True，否则抛出异常
            
        Raises:
            ValueError: 验证失败时抛出
        """
        # 检查文件大小
        if len(file_content) > cls.MAX_FILE_SIZE:
            raise ValueError(f"文件过大: {len(file_content)} bytes，最大允许: {cls.MAX_FILE_SIZE} bytes")
        
        # 检查文件名安全性
        if not cls._is_safe_filename(filename):
            raise ValueError(f"不安全的文件名: {filename}")
        
        # 检查文件扩展名
        ext = Path(filename).suffix.lower()
        if ext not in cls.ALLOWED_EXTENSIONS:
            raise ValueError(f"不支持的文件扩展名: {ext}")
        
        # 检查MIME类型
        try:
            mime_type = magic.from_buffer(file_content, mime=True)
            if mime_type not in cls.ALLOWED_MIME_TYPES:
                raise ValueError(f"不支持的文件类型: {mime_type}")
        except Exception as e:
            logger.warning(f"MIME类型检测失败: {e}")
            # 如果MIME检测失败，仅依赖扩展名检查
        
        return True
    
    @classmethod
    def _is_safe_filename(cls, filename: str) -> bool:
        """
        检查文件名是否安全
        
        Args:
            filename: 文件名
            
        Returns:
            安全返回True，否则返回False
        """
        # 检查空文件名
        if not filename or filename.strip() == '':
            return False
        
        # 检查路径遍历攻击
        if '..' in filename or '/' in filename or '\\' in filename:
            return False
        
        # 检查特殊字符
        dangerous_chars = ['<', '>', ':', '"', '|', '?', '*', '\0']
        if any(char in filename for char in dangerous_chars):
            return False
        
        # 检查文件名长度
        if len(filename) > 255:
            return False
        
        return True
    
    @classmethod
    def generate_safe_filename(cls, original_filename: str) -> str:
        """
        生成安全的文件名
        
        Args:
            original_filename: 原始文件名
            
        Returns:
            安全的文件名
        """
        # 提取文件名和扩展名
        path = Path(original_filename)
        name = path.stem
        ext = path.suffix.lower()
        
        # 生成时间戳和哈希
        timestamp = int(time.time())
        hash_part = hashlib.md5(name.encode('utf-8')).hexdigest()[:8]
        
        # 组合安全文件名
        safe_filename = f"{hash_part}_{timestamp}{ext}"
        
        logger.info(f"生成安全文件名: {original_filename} -> {safe_filename}")
        return safe_filename
    
    @classmethod
    def save_uploaded_file(cls, file_content: bytes, original_filename: str, 
                          upload_dir: str) -> str:
        """
        安全保存上传文件
        
        Args:
            file_content: 文件内容
            original_filename: 原始文件名
            upload_dir: 上传目录
            
        Returns:
            保存的文件路径
            
        Raises:
            ValueError: 文件验证失败
            OSError: 文件保存失败
        """
        # 验证文件
        cls.validate_file(file_content, original_filename)
        
        # 生成安全文件名
        safe_filename = cls.generate_safe_filename(original_filename)
        
        # 确保上传目录存在
        os.makedirs(upload_dir, exist_ok=True)
        
        # 构建完整路径
        file_path = os.path.join(upload_dir, safe_filename)
        
        # 保存文件
        try:
            with open(file_path, 'wb') as f:
                f.write(file_content)
            
            logger.info(f"文件保存成功: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"文件保存失败: {e}")
            raise OSError(f"文件保存失败: {e}")
```

---

**创建日期**: 2024年12月
**更新日期**: 2024年12月
**版本**: 1.0
**状态**: 草案
