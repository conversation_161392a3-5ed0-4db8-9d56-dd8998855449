#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IntelliCutAgent 2.0 - 智能视频处理系统
主入口文件
"""

import os
import sys
import logging

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from backend.agent.modules.cli_interface import CLIInterface


def setup_logging():
    """设置日志"""
    # 确保logs目录存在
    logs_dir = os.path.join(project_root, "logs")
    os.makedirs(logs_dir, exist_ok=True)
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(os.path.join(logs_dir, "intellicut.log"), encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )


def main():
    """主函数"""
    try:
        # 设置日志
        setup_logging()
        
        # 启动CLI界面
        cli = CLIInterface()
        cli.run()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序启动失败: {e}")
        logging.error(f"程序启动失败: {e}")


if __name__ == "__main__":
    main()
