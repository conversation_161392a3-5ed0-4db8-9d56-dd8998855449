# IntelliCutAgent - Main Entry Point

import logging
import asyncio
import argparse
import json
import os
from typing import Dict, Any, Optional, Union

# 导入Agent的核心组件
from backend.agent_coordinator import AgentCoordinator

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IntelliCutAgentCore:
    """智能混剪代理的核心业务逻辑和组件协调器。"""
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化 IntelliCutAgent 核心
        
        Args:
            config: 配置信息
        """
        logger.info("正在初始化 IntelliCutAgent 核心...")
        
        # 使用 AgentCoordinator 作为核心组件协调器
        self.coordinator = AgentCoordinator(config)

        # 添加缺失的属性映射，用于测试和外部访问
        self.material_manager = self.coordinator.material_manager
        self.content_analyzer = self.coordinator.content_analyzer
        self.smart_editor = self.coordinator.smart_editor
        self.platform_adapter = self.coordinator.platform_adapter
        self.batch_publisher = self.coordinator.batch_publisher

        logger.info("IntelliCutAgent 核心初始化完毕。")

    async def process_request(self, user_command: Union[str, Dict[str, Any]], config_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        处理来自用户或系统的单个混剪请求。
        
        Args:
            user_command: 用户命令，可以是字符串或字典
            config_data: 配置数据
            
        Returns:
            处理结果
        """
        logger.info(f"接收到请求: 命令='{user_command}', 配置='{config_data is not None}'")
        
        # 使用 AgentCoordinator 处理请求
        result = await self.coordinator.process_request(user_command, config_data)
        
        return result

async def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="IntelliCutAgent - 智能混剪代理")
    parser.add_argument("--mode", choices=["api", "cli", "demo"], default="demo", help="运行模式: api, cli 或 demo")
    parser.add_argument("--config", type=str, help="配置文件路径")
    parser.add_argument("--host", type=str, default="127.0.0.1", help="API服务器主机地址")
    parser.add_argument("--port", type=int, default=8000, help="API服务器端口")
    args = parser.parse_args()
    
    # 加载配置
    config = {}
    if args.config and os.path.exists(args.config):
        try:
            with open(args.config, 'r', encoding='utf-8') as f:
                config = json.load(f)
            logger.info(f"已加载配置文件: {args.config}")
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
    
    # 初始化代理核心
    agent = IntelliCutAgentCore(config)
    
    # 根据运行模式执行不同的操作
    if args.mode == "api":
        # 启动API服务器
        logger.info(f"启动API服务器: {args.host}:{args.port}")
        from backend.agent.user_interface.api_server import run_api_server
        await run_api_server(agent_core_param=agent, host=args.host, port=args.port)
    elif args.mode == "cli":
        # 启动CLI交互循环
        logger.info("启动CLI交互循环")
        from backend.agent.user_interface.cli_interface import run_cli_interface
        await run_cli_interface(agent_core=agent)
    else:
        # 演示模式
        logger.info("\n--- 开始处理示例请求: 创建精彩集锦 ---")
        command = {
            "action": "create_and_publish_video",
            "params": {
                "material_path": "demo/sample_video.mp4",
                "platforms": ["douyin", "bilibili"],
                "edit_rules": {
                    "duration": "30s",
                    "style": "fast_paced",
                    "transitions": ["fade", "wipe"],
                    "effects": ["zoom", "slow_motion"]
                },
                "title": "精彩动作集锦",
                "description": "由IntelliCutAgent自动生成的精彩动作集锦",
                "tags": ["动作", "精彩", "AI生成"]
            }
        }
        
        result = await agent.process_request(command, {})
        logger.info(f"请求处理结果: {result.get('status')} - {result.get('message')}")
        
        if result.get("publish_results"):
            for platform, platform_result in result["publish_results"].items():
                logger.info(f"  平台 {platform} 发布结果: {platform_result.get('status')}")
                if platform_result.get("url"):
                    logger.info(f"    视频URL: {platform_result.get('url')}")
        
        logger.info("IntelliCutAgent 演示运行结束。")

if __name__ == "__main__":
    asyncio.run(main())

