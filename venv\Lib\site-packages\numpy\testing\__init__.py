"""Common test support for all numpy test scripts.

This single module should provide all the common functionality for numpy tests
in a single location, so that test scripts can just import it and work right
away.

"""
from unittest import TestCase

from . import _private
from . import overrides

__all__ = (
    _private.utils.__all__ + ['TestCase', 'overrides']
)

from numpy._pytesttester import PytestTester
test = PytestTester(__name__)
del PytestTester
