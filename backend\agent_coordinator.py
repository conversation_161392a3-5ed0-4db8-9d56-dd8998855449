#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IntelliCutAgent 代理协调器
负责协调各个引擎和模块之间的交互，提供统一的接口
"""

import asyncio
import json
import logging
import os
from datetime import datetime
from typing import Any, Dict, Optional, Union

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

from backend.agent.action_execution_engine.task_executor import TaskExecutor
from backend.agent.action_execution_engine.tool_interface import ToolInterface
from backend.agent.batch_publisher.batch_publisher import BatchPublisher
from backend.agent.content_analyzer.content_analyzer import ContentAnalyzer
from backend.agent.decision_planning_engine.goal_processor import GoalProcessor
from backend.agent.decision_planning_engine.resource_allocator import ResourceAllocator
from backend.agent.decision_planning_engine.strategy_selector import StrategySelector
from backend.agent.decision_planning_engine.task_planner import TaskPlanner
from backend.agent.evaluation_feedback_engine.feedback_collector import FeedbackCollector
from backend.agent.evaluation_feedback_engine.performance_evaluator import PerformanceEvaluator
from backend.agent.evaluation_feedback_engine.report_generator import ReportGenerator
from backend.agent.knowledge_base.media_feature_store import MediaFeatureStore
from backend.agent.knowledge_base.memory_manager import MemoryManager
from backend.agent.knowledge_base.rule_store import RuleStore
from backend.agent.knowledge_base.user_profile_store import UserProfileStore
from backend.agent.learning_engine.feedback_processor import FeedbackProcessor
from backend.agent.learning_engine.model_updater import ModelUpdater
from backend.agent.learning_engine.trend_analyzer import TrendAnalyzer
from backend.agent.material_manager.material_manager import MaterialManager
from backend.agent.perception_engine.audio_analyzer import AudioAnalyzer

# 导入各个引擎和模块
from backend.agent.perception_engine.input_parser import InputParser
from backend.agent.perception_engine.metadata_extractor import MetadataExtractor
from backend.agent.perception_engine.video_analyzer import VideoAnalyzer
from backend.agent.platform_adapter.platform_adapter import PlatformAdapter
from backend.agent.smart_editor.smart_editor import SmartEditor


class AgentCoordinator:
    """
    IntelliCutAgent 代理协调器
    负责协调各个引擎和模块之间的交互，提供统一的接口
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化代理协调器

        Args:
            config: 配置信息，包括各个模块的配置
        """
        logger.info("正在初始化 AgentCoordinator...")
        self.config = config or {}
        self.session_id = f"session_{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # 初始化各个引擎和模块
        self._init_components()

        # 注册业务流程
        self._register_workflows()

        logger.info("AgentCoordinator 初始化完成")

    def _init_components(self):
        """初始化各个组件"""
        # 1. 初始化工具和底层服务
        self.tool_interface = ToolInterface()

        # 2. 初始化知识库组件
        self.memory_manager = MemoryManager()
        self.rule_store = RuleStore()
        self.user_profile_store = UserProfileStore()
        self.media_feature_store = MediaFeatureStore()

        # 3. 初始化感知引擎组件
        self.input_parser = InputParser()
        self.video_analyzer = VideoAnalyzer(tool_interface=self.tool_interface, feature_store=self.media_feature_store)
        self.audio_analyzer = AudioAnalyzer(tool_interface=self.tool_interface, feature_store=self.media_feature_store)
        self.metadata_extractor = MetadataExtractor(tool_interface=self.tool_interface)

        # 4. 初始化学习引擎组件
        self.feedback_processor = FeedbackProcessor(
            rule_store=self.rule_store, user_profile_store=self.user_profile_store
        )
        self.model_updater = ModelUpdater()
        self.trend_analyzer = TrendAnalyzer(
            media_feature_store=self.media_feature_store, feedback_processor=self.feedback_processor
        )

        # 5. 初始化决策与规划引擎组件
        self.resource_allocator = ResourceAllocator()
        self.goal_processor = GoalProcessor(user_profile_store=self.user_profile_store)
        self.task_planner = TaskPlanner(rule_store=self.rule_store, media_feature_store=self.media_feature_store)
        self.strategy_selector = StrategySelector(
            rule_store=self.rule_store, user_profile_store=self.user_profile_store, trend_analyzer=self.trend_analyzer
        )

        # 6. 初始化行动与执行引擎组件
        self.task_executor = TaskExecutor(
            tool_interface=self.tool_interface, resource_allocator=self.resource_allocator
        )

        # 7. 初始化评估与反馈引擎组件
        self.feedback_collector = FeedbackCollector(feedback_storage=self.memory_manager)
        self.performance_evaluator = PerformanceEvaluator(
            rule_store=self.rule_store, user_profile_store=self.user_profile_store
        )
        self.report_generator = ReportGenerator()

        # 8. 初始化业务模块
        self.material_manager = MaterialManager()
        self.content_analyzer = ContentAnalyzer()
        self.platform_adapter = PlatformAdapter()
        self.smart_editor = SmartEditor()
        self.batch_publisher = BatchPublisher()

    def _register_workflows(self):
        """注册业务流程"""
        self.workflows = {
            "create_and_publish_video": self._workflow_create_and_publish_video,
            "analyze_video": self._workflow_analyze_video,
            "analyze_platform_potential": self._workflow_analyze_platform_potential,
            "analyze_video_revenue": self._workflow_analyze_video_revenue,
            "generate_optimization_strategy": self._workflow_generate_optimization_strategy,
            "track_revenue_trends": self._workflow_track_revenue_trends,
            "batch_process_videos": self._workflow_batch_process_videos,
            "system_maintenance": self._workflow_system_maintenance,
        }

    async def process_request(
        self, request: Union[str, Dict[str, Any]], config_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        处理请求

        Args:
            request: 请求内容，可以是字符串或字典
            config_data: 配置数据

        Returns:
            处理结果
        """
        logger.info("接收到请求: {request}")

        # 解析请求
        if isinstance(request, str):
            parsed_request = self.input_parser.parse_user_command(request)
        else:
            parsed_request = request

        action = parsed_request.get("action")
        params = parsed_request.get("params", {})

        # 执行对应的工作流
        if action in self.workflows:
            logger.info("执行工作流: {action}")
            result = await self.workflows[action](params, config_data)
            return result
        else:
            logger.warning(f"未知工作流: {action}")
            return {"status": "error", "message": "未知工作流: {action}"}

    async def _workflow_create_and_publish_video(
        self, params: Dict[str, Any], config_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        创建并发布视频工作流

        Args:
            params: 参数
            config_data: 配置数据

        Returns:
            处理结果
        """
        logger.info("开始执行创建并发布视频工作流")

        material_path = params.get("material_path")
        platforms_to_publish = params.get("platforms", [])
        edit_rules = params.get("edit_rules", {})
        title = params.get("title", "Default Video Title")
        description = params.get("description", "Default video description.")
        tags = params.get("tags", ["AI", "Generated"])

        if not material_path:
            logger.error("缺少素材路径。")
            return {"status": "error", "message": "缺少素材路径，无法创建和发布视频。"}

        try:
            # 1. 素材管理：上传素材
            logger.info("步骤1: 上传素材...")
            uploaded_material = self.material_manager.upload_material(
                file_path=material_path, material_type="video", tags=tags
            )

            if not uploaded_material:
                logger.error("素材上传失败。")
                return {"status": "error", "message": "素材上传失败。"}
            material_id = uploaded_material["id"]
            logger.info("素材上传成功，ID: {material_id}")

            # 2. 内容分析：分析素材
            logger.info("步骤2: 分析素材内容...")
            analysis_results = self.content_analyzer.analyze_video(material_path)
            if not analysis_results:
                raise Exception("内容分析失败。")
            logger.info(f"素材分析完成: {analysis_results.get('speech_to_text', 'N/A')[:30]}...")

            # 3. 智能剪辑：根据分析结果和规则剪辑视频
            logger.info("步骤3: 智能剪辑视频...")
            edited_video_path = self.smart_editor.auto_edit_video(
                material_ids=[material_id], analysis_results=analysis_results, edit_rules=edit_rules
            )
            if not edited_video_path:
                raise Exception("视频剪辑失败。")
            logger.info(f"视频剪辑完成，输出路径: {edited_video_path}")

            # 4. 平台适配：为每个目标平台生成适配的视频和元数据
            final_publish_results = {}
            for platform in platforms_to_publish:
                logger.info("步骤4: 为平台 {platform} 适配视频并生成元数据...")
                # 模拟平台适配，实际可能生成不同分辨率/格式的视频
                adapted_video_path = self.platform_adapter.adapt_for_platform(edited_video_path, platform)
                if not adapted_video_path:
                    logger.warning(f"平台 {platform} 视频适配失败，跳过发布。")
                    continue

                video_info = {"title": title, "description": description, "tags": tags}
                platform_metadata = self.platform_adapter.generate_metadata(platform=platform, video_info=video_info)
                if not platform_metadata:
                    logger.warning("平台 {platform} 元数据生成失败，跳过发布。")
                    continue

                # 5. 批量发布：发布到平台
                logger.info("步骤5: 发布视频到 {platform}...")
                # 确保adapted_video_path是字符串
                if isinstance(adapted_video_path, dict):
                    video_path_str = adapted_video_path.get("output_path", str(adapted_video_path))
                else:
                    video_path_str = str(adapted_video_path)

                publish_result = self.batch_publisher.publish_to_platforms(
                    video_path=video_path_str, platforms=[platform], metadata=platform_metadata
                )
                final_publish_results[platform] = publish_result.get(platform)
                platform_result = publish_result.get(platform)
                if platform_result:
                    logger.info(f"发布到 {platform} 结果: {platform_result.get('status')}")
                else:
                    logger.warning("发布到 {platform} 失败：无结果返回")

            # 6. 评估与反馈：收集性能数据
            logger.info("步骤6: 收集性能数据...")
            performance_data = {
                "video_id": material_id,
                "edit_time": 0,  # 实际应该记录编辑时间
                "platforms": platforms_to_publish,
                "publish_results": final_publish_results,
            }
            # 模拟性能评估
            if hasattr(self.performance_evaluator, "evaluate_performance"):
                self.performance_evaluator.evaluate_performance(performance_data)
            else:
                logger.info("性能评估器未实现，跳过性能评估")

            logger.info("创建并发布视频工作流执行完毕。")
            return {
                "status": "success",
                "message": "视频创作与发布流程成功完成。",
                "publish_results": final_publish_results,
            }

        except Exception as e:
            logger.error(f"视频创作与发布流程中发生错误: {e}")
            return {"status": "error", "message": "视频创作与发布流程失败: {e}"}

    async def _workflow_analyze_video(
        self, params: Dict[str, Any], config_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        分析视频工作流

        Args:
            params: 参数
            config_data: 配置数据

        Returns:
            处理结果
        """
        logger.info("开始执行分析视频工作流")

        video_path = params.get("video_path")
        analysis_types = params.get("analysis_types", ["basic", "scene_detection"])

        if not video_path:
            logger.error("缺少视频路径。")
            return {"status": "error", "message": "缺少视频路径，无法分析视频。"}

        try:
            # 分析视频
            logger.info("分析视频: {video_path}, 分析类型: {analysis_types}")
            analysis_results = self.video_analyzer.analyze_video(video_path, analysis_types)

            if not analysis_results:
                logger.error("视频分析失败。")
                return {"status": "error", "message": "视频分析失败。"}

            logger.info("视频分析完成。")
            return {"status": "success", "message": "视频分析成功。", "analysis_results": analysis_results}

        except Exception as e:
            logger.error(f"视频分析过程中发生错误: {e}")
            return {"status": "error", "message": "视频分析失败: {e}"}

    async def _workflow_analyze_platform_potential(
        self, params: Dict[str, Any], config_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        分析平台收益潜力工作流

        Args:
            params: 参数
            config_data: 配置数据

        Returns:
            处理结果
        """
        logger.info("开始执行分析平台收益潜力工作流")

        platforms = params.get("platforms", [])
        content_types = params.get("content_types", [])
        time_period = params.get("time_period", "monthly")

        try:
            # 创建收益优化器
            optimizer = self._create_revenue_optimizer()

            # 分析平台收益潜力
            if optimizer and hasattr(optimizer, "analyze_platform_revenue_potential"):
                result = optimizer.analyze_platform_revenue_potential(
                    platforms=platforms, content_types=content_types, time_period=time_period
                )
            else:
                # 模拟结果
                result = {
                    "platform_revenue_potential": {
                        platform: {"name": platform, "total_potential": 1000.0, "average_revenue_per_video": 50.0}
                        for platform in platforms
                    }
                }

            logger.info("平台收益潜力分析完成。")
            return {"status": "success", "message": "平台收益潜力分析成功。", "result": result}

        except Exception as e:
            logger.error(f"平台收益潜力分析过程中发生错误: {e}")
            return {"status": "error", "message": "平台收益潜力分析失败: {e}"}

    async def _workflow_analyze_video_revenue(
        self, params: Dict[str, Any], config_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        分析视频收益表现工作流

        Args:
            params: 参数
            config_data: 配置数据

        Returns:
            处理结果
        """
        logger.info("开始执行分析视频收益表现工作流")

        video_id = params.get("video_id")
        platform = params.get("platform")
        content_type = params.get("content_type")
        metrics_file = params.get("metrics_file")

        if not video_id or not platform or not content_type:
            logger.error("缺少必要参数。")
            return {"status": "error", "message": "缺少必要参数，无法分析视频收益表现。"}

        if not metrics_file or not os.path.exists(metrics_file):
            logger.error(f"指标文件不存在: {metrics_file}")
            return {"status": "error", "message": "指标文件不存在: {metrics_file}"}

        try:
            # 读取指标文件
            with open(metrics_file, "r", encoding="utf-8") as f:
                metrics_data = json.load(f)

            # 创建收益优化器
            optimizer = self._create_revenue_optimizer()

            # 准备视频数据
            video_data = {
                "video_id": video_id,
                "platform": platform,
                "content_type": content_type,
                "metrics": metrics_data,
            }

            # 分析视频收益
            if optimizer and hasattr(optimizer, "analyze_video_revenue_performance"):
                result = optimizer.analyze_video_revenue_performance(video_data)
            else:
                # 模拟结果
                result = {
                    "revenue_performance": {"total_revenue": 500.0, "average_rpm": 2.5, "performance_score": 85.0}
                }

            logger.info("视频收益表现分析完成。")
            return {"status": "success", "message": "视频收益表现分析成功。", "result": result}

        except Exception as e:
            logger.error(f"视频收益表现分析过程中发生错误: {e}")
            return {"status": "error", "message": "视频收益表现分析失败: {e}"}

    async def _workflow_generate_optimization_strategy(
        self, params: Dict[str, Any], config_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        生成收益优化策略工作流

        Args:
            params: 参数
            config_data: 配置数据

        Returns:
            处理结果
        """
        logger.info("开始执行生成收益优化策略工作流")

        user_id = params.get("user_id")
        platforms = params.get("platforms", [])
        content_preferences = params.get("content_preferences", [])

        try:
            # 创建收益优化器
            optimizer = self._create_revenue_optimizer()

            # 生成优化策略
            if optimizer and hasattr(optimizer, "generate_revenue_optimization_strategy"):
                result = optimizer.generate_revenue_optimization_strategy(
                    user_id=user_id, target_platforms=platforms, content_preferences=content_preferences
                )
            else:
                # 模拟结果
                result = {
                    "optimization_strategy": {
                        "recommended_platforms": platforms[:2] if len(platforms) > 2 else platforms,
                        "content_suggestions": ["增加互动性", "优化标题"],
                        "posting_schedule": "每周3次",
                    }
                }

            logger.info("收益优化策略生成完成。")
            return {"status": "success", "message": "收益优化策略生成成功。", "result": result}

        except Exception as e:
            logger.error(f"收益优化策略生成过程中发生错误: {e}")
            return {"status": "error", "message": "收益优化策略生成失败: {e}"}

    async def _workflow_track_revenue_trends(
        self, params: Dict[str, Any], config_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        跟踪收益趋势工作流

        Args:
            params: 参数
            config_data: 配置数据

        Returns:
            处理结果
        """
        logger.info("开始执行跟踪收益趋势工作流")

        user_id = params.get("user_id")
        time_periods = params.get("time_periods", ["weekly", "monthly", "quarterly"])
        platforms = params.get("platforms", [])

        try:
            # 创建收益优化器
            optimizer = self._create_revenue_optimizer()

            # 跟踪收益趋势
            if optimizer and hasattr(optimizer, "track_revenue_trends"):
                result = optimizer.track_revenue_trends(user_id=user_id, time_periods=time_periods, platforms=platforms)
            else:
                # 模拟结果
                result = {
                    "revenue_trends": {
                        "trend_direction": "上升",
                        "growth_rate": 15.5,
                        "predictions": ["下月预计增长20%"],
                    }
                }

            logger.info("收益趋势跟踪完成。")
            return {"status": "success", "message": "收益趋势跟踪成功。", "result": result}

        except Exception as e:
            logger.error(f"收益趋势跟踪过程中发生错误: {e}")
            return {"status": "error", "message": "收益趋势跟踪失败: {e}"}

    async def _workflow_batch_process_videos(
        self, params: Dict[str, Any], config_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        批量处理视频工作流

        Args:
            params: 参数
            config_data: 配置数据

        Returns:
            处理结果
        """
        logger.info("开始执行批量处理视频工作流")

        video_paths = params.get("video_paths", [])
        process_type = params.get("process_type", "edit")
        edit_rules = params.get("edit_rules", {})
        platforms = params.get("platforms", [])

        if not video_paths:
            logger.error("缺少视频路径列表。")
            return {"status": "error", "message": "缺少视频路径列表，无法批量处理视频。"}

        try:
            results = []

            for video_path in video_paths:
                logger.info("处理视频: {video_path}")

                # 分析视频
                analysis_results = self.video_analyzer.analyze_video(video_path, ["basic", "scene_detection"])

                if process_type == "edit" or process_type == "edit_and_publish":
                    # 编辑视频
                    edited_video_path = self.smart_editor.auto_edit_video(
                        material_ids=[video_path], analysis_results=analysis_results, edit_rules=edit_rules
                    )

                    if not edited_video_path:
                        logger.warning(f"视频 {video_path} 编辑失败，跳过。")
                        results.append({"video_path": video_path, "status": "error", "message": "视频编辑失败。"})
                        continue

                    if process_type == "edit_and_publish":
                        # 发布视频
                        publish_results = {}

                        for platform in platforms:
                            # 适配平台
                            adapted_video_path = self.platform_adapter.adapt_for_platform(edited_video_path, platform)
                            if not adapted_video_path:
                                logger.warning(f"视频 {video_path} 适配平台 {platform} 失败，跳过。")
                                continue

                            # 生成元数据
                            video_info = {
                                "title": "自动生成视频 - {os.path.basename(video_path)}",
                                "description": "由IntelliCutAgent自动生成的视频",
                                "tags": ["AI", "自动生成"],
                            }
                            metadata = self.platform_adapter.generate_metadata(platform=platform, video_info=video_info)

                            # 发布视频
                            # 确保adapted_video_path是字符串
                            if isinstance(adapted_video_path, dict):
                                video_path_str = adapted_video_path.get("output_path", str(adapted_video_path))
                            else:
                                video_path_str = str(adapted_video_path)

                            publish_result = self.batch_publisher.publish_to_platforms(
                                video_path=video_path_str, platforms=[platform], metadata=metadata
                            )

                            publish_results[platform] = publish_result.get(platform)

                        results.append(
                            {
                                "video_path": video_path,
                                "edited_video_path": edited_video_path,
                                "status": "success",
                                "publish_results": publish_results,
                            }
                        )
                    else:
                        results.append(
                            {"video_path": video_path, "edited_video_path": edited_video_path, "status": "success"}
                        )
                else:
                    # 仅分析
                    results.append(
                        {"video_path": video_path, "analysis_results": analysis_results, "status": "success"}
                    )

            logger.info("批量处理视频完成。")
            return {"status": "success", "message": "批量处理视频成功。", "results": results}

        except Exception as e:
            logger.error(f"批量处理视频过程中发生错误: {e}")
            return {"status": "error", "message": "批量处理视频失败: {e}"}

    async def _workflow_system_maintenance(
        self, params: Dict[str, Any], config_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        系统维护工作流

        Args:
            params: 参数
            config_data: 配置数据

        Returns:
            处理结果
        """
        logger.info("开始执行系统维护工作流")

        maintenance_type = params.get("maintenance_type", "full")
        days_to_keep = params.get("days_to_keep", 30)

        try:
            results = {}

            # 清理缓存
            if maintenance_type in ["full", "cache"]:
                logger.info("清理缓存...")
                cache_result = self.trend_analyzer.clear_cache()
                results["cache_cleanup"] = cache_result

            # 清理旧数据
            if maintenance_type in ["full", "data"]:
                logger.info("清理旧数据...")
                data_result = self.feedback_processor.clear_old_data(days_to_keep=days_to_keep)
                results["data_cleanup"] = data_result

            # 更新模型
            if maintenance_type in ["full", "model"]:
                logger.info("更新模型...")
                if hasattr(self.model_updater, "update_models"):
                    model_result = self.model_updater.update_models()
                else:
                    model_result = {"status": "success", "message": "模型更新器未实现"}
                results["model_update"] = model_result

            logger.info("系统维护完成。")
            return {"status": "success", "message": "系统维护成功。", "results": results}

        except Exception as e:
            logger.error(f"系统维护过程中发生错误: {e}")
            return {"status": "error", "message": "系统维护失败: {e}"}

    def _create_revenue_optimizer(self):
        """创建收益优化器"""
        if hasattr(self.trend_analyzer, "revenue_optimizer"):
            return getattr(self.trend_analyzer, "revenue_optimizer", None)
        else:
            logger.warning("TrendAnalyzer没有revenue_optimizer属性，返回None")
            return None


if __name__ == "__main__":
    # 测试代码
    async def test_coordinator():
        coordinator = AgentCoordinator()

        # 测试创建并发布视频
        request = {
            "action": "create_and_publish_video",
            "params": {
                "material_path": "/path/to/test_video.mp4",
                "platforms": ["douyin", "bilibili"],
                "edit_rules": {"duration": "60s", "style": "fast_paced"},
                "title": "测试视频",
                "description": "这是一个测试视频",
                "tags": ["测试", "AI"],
            },
        }

        result = await coordinator.process_request(request)
        print("处理结果: {result}")

    # 运行测试
    asyncio.run(test_coordinator())
