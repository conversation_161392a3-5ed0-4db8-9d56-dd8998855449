#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复 agent_controller.py 的缩进问题
"""

import os
import re


def fix_agent_controller():
    """修复 agent_controller.py 的缩进问题"""
    file_path = "backend/agent/agent_controller.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复方法定义的缩进
        fixes = [
            # 修复方法定义缺少缩进
            (r'^def (_[a-zA-Z_][a-zA-Z0-9_]*\([^)]*\)[^:]*:)', r'    def \1'),
            
            # 修复方法体缩进
            (r'^    def (_[a-zA-Z_][a-zA-Z0-9_]*\([^)]*\)[^:]*:)\n([a-zA-Z_])', r'    def \1\n        \2'),
            
            # 修复字符串中的错误
            (r'"缺少目标平台参数f"', r'"缺少目标平台参数"'),
            (r'", f\'.join\(platforms\)"', r'", ".join(platforms)"'),
            (r'"比较不同平台的收益f"', r'"比较不同平台的收益"'),
            (r'"生成 \{platform\} 平台的收益报告f"', r'f"生成 {platform} 平台的收益报告"'),
            (r'"获取任务历史成功f"', r'"获取任务历史成功"'),
            (r'key=lambda x: x\.get\("start_time", "f"\)', r'key=lambda x: x.get("start_time", "")'),
        ]
        
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        # 手动修复特定的缩进问题
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # 如果是方法定义后面的第一行，且没有正确缩进
            if i > 0 and lines[i-1].strip().startswith('def _') and lines[i-1].endswith(':'):
                if line and not line.startswith('    ') and not line.startswith('\t'):
                    line = '        ' + line.lstrip()
            
            # 如果在方法内部，确保正确缩进
            elif i > 0 and any(lines[j].strip().startswith('def _') and lines[j].endswith(':') for j in range(max(0, i-20), i)):
                # 检查是否在方法内部
                in_method = False
                for j in range(i-1, -1, -1):
                    if lines[j].strip().startswith('def _') and lines[j].endswith(':'):
                        in_method = True
                        break
                    elif lines[j].strip().startswith('def ') and not lines[j].strip().startswith('def _'):
                        break
                
                if in_method and line and not line.startswith('    ') and not line.startswith('\t') and not line.strip().startswith('def'):
                    line = '        ' + line.lstrip()
            
            fixed_lines.append(line)
        
        content = '\n'.join(fixed_lines)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 修复完成: {file_path}")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")


if __name__ == "__main__":
    fix_agent_controller()
