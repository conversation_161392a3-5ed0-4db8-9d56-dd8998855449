"""
类型存根文件，用于帮助 Pylance 识别 moviepy.editor 模块
"""

from typing import Callable
from typing import List
from typing import Optional
from typing import Tuple
from typing import Union

class VideoFileClip:
    """VideoFileClip 类的类型存根"""

    duration: float
    fps: float
    size: Tuple[int, int]

    def __init__(
        self,
        filename: str,
        audio: bool = True,
        audio_buffersize: int = 200000,
        audio_fps: int = 44100,
        audio_nbytes: int = 2,
        fps_source: str = "tbr",
        pixel_format: Optional[str] = None,
        target_resolution: Optional[Tuple[int, int]] = None,
        resize_algorithm: str = "bicubic",
        audio_codec: str = "libmp3lame",
        verbose: bool = False,
        threads: int = None,
        logger: str = "bar",
    ):
        pass

    def subclip(self, t_start: float = 0, t_end: Optional[float] = None) -> "VideoFileClip":
        pass

    def fx(self, func: Callable, *args, **kwargs) -> "VideoFileClip":
        pass

    def set_duration(self, duration: float) -> "VideoFileClip":
        pass

    def set_fps(self, fps: float) -> "VideoFileClip":
        pass

    def set_audio(self, audioclip: "AudioClip") -> "VideoFileClip":
        pass

    def without_audio(self) -> "VideoFileClip":
        pass

    def write_videofile(
        self,
        filename: str,
        fps: Optional[float] = None,
        codec: str = "libx264",
        bitrate: Optional[str] = None,
        audio: bool = True,
        audio_fps: int = 44100,
        preset: str = "medium",
        audio_nbytes: int = 4,
        audio_codec: str = "libmp3lame",
        audio_bitrate: Optional[str] = None,
        audio_bufsize: int = 2000,
        temp_audiofile: Optional[str] = None,
        remove_temp: bool = True,
        write_logfile: bool = False,
        verbose: bool = True,
        threads: Optional[int] = None,
        ffmpeg_params: Optional[List[str]] = None,
        logger: str = "bar",
    ) -> None:
        pass

    def close(self) -> None:
        pass

class AudioFileClip:
    """AudioFileClip 类的类型存根"""

    duration: float

    def __init__(
        self, filename: str, buffersize: int = 200000, fps: Optional[int] = None, nbytes: int = 2, verbose: bool = False
    ):
        pass

class AudioClip:
    """AudioClip 类的类型存根"""

    duration: float

    def __init__(
        self, make_frame: Optional[Callable] = None, duration: Optional[float] = None, fps: Optional[int] = None
    ):
        pass

class CompositeVideoClip:
    """CompositeVideoClip 类的类型存根"""

    duration: float

    def __init__(
        self,
        clips: List[VideoFileClip],
        size: Optional[Tuple[int, int]] = None,
        bg_color: Optional[Union[Tuple[int, int, int], str]] = None,
        use_bgclip: bool = False,
        ismask: bool = False,
    ):
        pass

    def write_videofile(
        self,
        filename: str,
        fps: Optional[float] = None,
        codec: str = "libx264",
        bitrate: Optional[str] = None,
        audio: bool = True,
        audio_fps: int = 44100,
        preset: str = "medium",
        audio_nbytes: int = 4,
        audio_codec: str = "libmp3lame",
        audio_bitrate: Optional[str] = None,
        audio_bufsize: int = 2000,
        temp_audiofile: Optional[str] = None,
        remove_temp: bool = True,
        write_logfile: bool = False,
        verbose: bool = True,
        threads: Optional[int] = None,
        ffmpeg_params: Optional[List[str]] = None,
        logger: str = "bar",
    ) -> None:
        pass

class concatenate_videoclips:
    """concatenate_videoclips 函数的类型存根"""

    def __init__(
        self,
        clips: List[VideoFileClip],
        method: str = "chain",
        transition: Optional[Callable] = None,
        bg_color: Optional[Union[Tuple[int, int, int], str]] = None,
        padding: int = 0,
        padding_color: Optional[Union[Tuple[int, int, int], str]] = None,
    ) -> None:
        pass

# 导出类型
__all__ = ["VideoFileClip", "AudioFileClip", "AudioClip", "CompositeVideoClip", "concatenate_videoclips"]
