"""
并行视频处理性能测试
"""

import os
import shutil
import sys
import tempfile
import time
import unittest
from unittest.mock import MagicMock
from unittest.mock import patch

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from backend.agent.smart_editor.parallel_processor import ParallelVideoProcessor
from backend.agent.smart_editor.video_editor import VideoEditor


class TestParallelProcessing(unittest.TestCase):
    """并行视频处理性能测试"""

    def setUp(self):
        """测试前的准备工作"""
        self.temp_dir = tempfile.mkdtemp()
        self.video_editor = VideoEditor(temp_dir=self.temp_dir, auto_cleanup=False)

        # 创建测试资源目录
        self.test_resources_dir = os.path.join(self.temp_dir, "test_resources")
        os.makedirs(self.test_resources_dir, exist_ok=True)

    def tearDown(self):
        """测试后的清理工作"""
        # 清理临时文件
        self.video_editor.cleanup_temp_files()

        # 删除临时目录
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    @patch("backend.agent.smart_editor.parallel_processor.ParallelVideoProcessor.process_video_parallel")
    @patch("backend.agent.smart_editor.video_editor.VideoEditor.apply_effect")
    def test_parallel_vs_sequential_performance(self, mock_apply_effect, mock_process_parallel):
        """测试并行处理与顺序处理的性能对比"""
        # 设置模拟对象
        mock_apply_effect.return_value = MagicMock()
        mock_process_parallel.return_value = True

        # 创建测试视频文件
        test_video_path = os.path.join(self.test_resources_dir, "test_video.mp4")
        with open(test_video_path, "w") as f:
            f.write("dummy video content")

        # 创建输出路径
        os.path.join(self.test_resources_dir, "output_sequential.mp4")
        output_path_parallel = os.path.join(self.test_resources_dir, "output_parallel.mp4")

        # 测试顺序处理性能
        start_time = time.time()
        self.video_editor.apply_effect(MagicMock(), "brightness", factor=1.5)
        time.time() - start_time

        # 测试并行处理性能
        start_time = time.time()
        self.video_editor.apply_effect_parallel(test_video_path, output_path_parallel, "brightness", factor=1.5)
        time.time() - start_time

        # 验证结果
        mock_apply_effect.assert_called_once()
        mock_process_parallel.assert_called_once()

        print("\n性能对比:")
        print("顺序处理时间: {sequential_time:.6f}秒")
        print("并行处理时间: {parallel_time:.6f}秒")
        print("加速比: {sequential_time / parallel_time:.2f}x")

    @patch("backend.agent.smart_editor.parallel_processor.mp.VideoFileClip")
    def test_video_splitting(self, mock_video_file_clip):
        """测试视频分块功能"""
        # 设置模拟对象
        mock_clip = MagicMock()
        mock_clip.duration = 100.0  # 100秒的视频
        mock_video_file_clip.return_value = mock_clip

        # 创建测试视频文件
        test_video_path = os.path.join(self.test_resources_dir, "test_video.mp4")
        with open(test_video_path, "w") as f:
            f.write("dummy video content")

        # 创建并行处理器
        processor = ParallelVideoProcessor(temp_dir=self.temp_dir)

        # 测试视频分块
        chunks = processor.split_video(test_video_path, chunk_count=10)

        # 验证结果
        self.assertEqual(len(chunks), 10)
        self.assertEqual(chunks[0].start_time, 0.0)
        self.assertEqual(chunks[0].end_time, 10.0)
        self.assertEqual(chunks[9].start_time, 90.0)
        self.assertEqual(chunks[9].end_time, 100.0)

    def test_large_video_processing(self):
        """测试大型视频处理功能"""
        # 创建测试视频文件
        test_video_path = os.path.join(self.test_resources_dir, "test_video.mp4")
        with open(test_video_path, "w") as f:
            f.write("dummy video content")

        # 创建输出路径
        output_path = os.path.join(self.test_resources_dir, "output.mp4")

        # 定义操作列表
        operations = [
            {"type": "effect", "name": "brightness", "factor": 1.5},
            {"type": "effect", "name": "contrast", "factor": 1.2},
            {"type": "speed", "factor": 1.5},
        ]

        # 模拟处理大型视频
        with patch("backend.agent.smart_editor.video_editor.VideoEditor.apply_effect_parallel") as mock_parallel:
            with patch("backend.agent.smart_editor.video_editor.VideoEditor.load_video") as mock_load:
                with patch("backend.agent.smart_editor.video_editor.VideoEditor.save_video") as mock_save:
                    # 设置模拟对象
                    mock_parallel.return_value = True
                    mock_video = MagicMock()
                    mock_video.fx.return_value = MagicMock()
                    mock_load.return_value = mock_video
                    mock_save.return_value = True

                    # 测试大型视频处理
                    result = self.video_editor.process_large_video(test_video_path, output_path, operations)

                    # 验证结果
                    self.assertTrue(result)
                    self.assertEqual(mock_parallel.call_count, 2)  # 两个特效操作
                    self.assertEqual(mock_load.call_count, 1)  # 一个速度操作
                    self.assertEqual(mock_save.call_count, 1)  # 一个保存操作


if __name__ == "__main__":
    unittest.main()
