"""
类型存根文件，用于帮助 Pylance 识别 moviepy.video.io.ffmpeg_tools 模块
"""

from typing import Any, Optional


def ffmpeg_extract_subclip(filename: str, t1: float, t2: float, targetname: Optional[str] = None) -> str:
    """从视频文件中提取子剪辑"""


def ffmpeg_resize(filename: str, output_filename: str, size: Any) -> None:
    """调整视频大小"""


def ffmpeg_merge_video_audio(
    video: str,
    audio: str,
    output: str,
    vcodec: str = "copy",
    acodec: str = "copy",
    ffmpeg_output: bool = False,
    verbose: bool = True,
) -> None:
    """合并视频和音频"""


# 导出所有函数
__all__ = ["ffmpeg_extract_subclip", "ffmpeg_resize", "ffmpeg_merge_video_audio"]
