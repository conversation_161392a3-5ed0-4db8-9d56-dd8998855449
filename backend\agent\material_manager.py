#!/usr/bin/env python3
"""
MaterialManager - 素材管理器
负责素材的收集、整理、预处理和管理
"""

import asyncio
import logging
import os
import shutil
from pathlib import Path
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class MaterialManager:
    """素材管理器：负责素材的收集、整理、预处理和管理"""

    def __init__(self, workspace_dir: str = None):
        """
        初始化素材管理器
        
        Args:
            workspace_dir: 工作空间目录
        """
        logger.info("初始化 MaterialManager...")
        
        self.workspace_dir = workspace_dir or os.path.join(os.getcwd(), "workspace")
        self.materials_dir = os.path.join(self.workspace_dir, "materials")
        self.processed_dir = os.path.join(self.workspace_dir, "processed")
        
        # 创建必要的目录
        os.makedirs(self.materials_dir, exist_ok=True)
        os.makedirs(self.processed_dir, exist_ok=True)
        
        # 支持的文件格式
        self.supported_video_formats = {'.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv'}
        self.supported_audio_formats = {'.mp3', '.wav', '.aac', '.flac', '.ogg'}
        self.supported_image_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.gif'}
        
        logger.info(f"MaterialManager 初始化完成。工作空间: {self.workspace_dir}")

    async def process_material(self, material_path: str) -> Dict[str, Any]:
        """
        处理素材
        
        Args:
            material_path: 素材路径（可以是文件或目录）
            
        Returns:
            处理结果
        """
        logger.info(f"开始处理素材: {material_path}")
        
        try:
            if not os.path.exists(material_path):
                return {
                    "status": "error",
                    "message": f"素材路径不存在: {material_path}"
                }
            
            processed_files = []
            
            if os.path.isfile(material_path):
                # 处理单个文件
                result = await self._process_single_file(material_path)
                if result.get("status") == "success":
                    processed_files.append(result.get("processed_path"))
            elif os.path.isdir(material_path):
                # 处理目录中的所有文件
                for root, dirs, files in os.walk(material_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        if self._is_supported_file(file_path):
                            result = await self._process_single_file(file_path)
                            if result.get("status") == "success":
                                processed_files.append(result.get("processed_path"))
            
            if not processed_files:
                return {
                    "status": "error",
                    "message": "没有找到可处理的素材文件"
                }
            
            return {
                "status": "success",
                "message": f"成功处理 {len(processed_files)} 个素材文件",
                "processed_files": processed_files,
                "file_count": len(processed_files)
            }
            
        except Exception as e:
            logger.error(f"处理素材时发生错误: {e}")
            return {
                "status": "error",
                "message": f"处理素材时发生错误: {str(e)}"
            }

    async def _process_single_file(self, file_path: str) -> Dict[str, Any]:
        """
        处理单个文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            处理结果
        """
        logger.info(f"处理单个文件: {file_path}")
        
        try:
            if not self._is_supported_file(file_path):
                return {
                    "status": "error",
                    "message": f"不支持的文件格式: {file_path}"
                }
            
            # 生成处理后的文件路径
            file_name = os.path.basename(file_path)
            processed_path = os.path.join(self.processed_dir, file_name)
            
            # 复制文件到处理目录
            shutil.copy2(file_path, processed_path)
            
            # 获取文件信息
            file_info = await self._get_file_info(processed_path)
            
            logger.info(f"文件处理完成: {processed_path}")
            
            return {
                "status": "success",
                "message": "文件处理完成",
                "original_path": file_path,
                "processed_path": processed_path,
                "file_info": file_info
            }
            
        except Exception as e:
            logger.error(f"处理文件 {file_path} 时发生错误: {e}")
            return {
                "status": "error",
                "message": f"处理文件时发生错误: {str(e)}"
            }

    def _is_supported_file(self, file_path: str) -> bool:
        """
        检查文件是否为支持的格式
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否支持
        """
        file_ext = Path(file_path).suffix.lower()
        return file_ext in (
            self.supported_video_formats | 
            self.supported_audio_formats | 
            self.supported_image_formats
        )

    async def _get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息
        """
        try:
            stat = os.stat(file_path)
            file_ext = Path(file_path).suffix.lower()
            
            file_info = {
                "file_name": os.path.basename(file_path),
                "file_size": stat.st_size,
                "file_extension": file_ext,
                "created_time": stat.st_ctime,
                "modified_time": stat.st_mtime
            }
            
            # 根据文件类型添加特定信息
            if file_ext in self.supported_video_formats:
                file_info["file_type"] = "video"
                # 这里可以添加视频特定信息的获取逻辑
                file_info["duration"] = 0  # 模拟数据
                file_info["resolution"] = "1920x1080"  # 模拟数据
                file_info["fps"] = 30  # 模拟数据
            elif file_ext in self.supported_audio_formats:
                file_info["file_type"] = "audio"
                # 这里可以添加音频特定信息的获取逻辑
                file_info["duration"] = 0  # 模拟数据
                file_info["sample_rate"] = 44100  # 模拟数据
                file_info["channels"] = 2  # 模拟数据
            elif file_ext in self.supported_image_formats:
                file_info["file_type"] = "image"
                # 这里可以添加图片特定信息的获取逻辑
                file_info["width"] = 1920  # 模拟数据
                file_info["height"] = 1080  # 模拟数据
            else:
                file_info["file_type"] = "unknown"
            
            return file_info
            
        except Exception as e:
            logger.error(f"获取文件信息时发生错误: {e}")
            return {
                "file_name": os.path.basename(file_path),
                "file_type": "unknown",
                "error": str(e)
            }

    async def organize_materials(self, materials: List[str]) -> Dict[str, Any]:
        """
        整理素材
        
        Args:
            materials: 素材文件列表
            
        Returns:
            整理结果
        """
        logger.info(f"开始整理 {len(materials)} 个素材")
        
        try:
            organized = {
                "videos": [],
                "audios": [],
                "images": []
            }
            
            for material in materials:
                if not os.path.exists(material):
                    continue
                
                file_ext = Path(material).suffix.lower()
                
                if file_ext in self.supported_video_formats:
                    organized["videos"].append(material)
                elif file_ext in self.supported_audio_formats:
                    organized["audios"].append(material)
                elif file_ext in self.supported_image_formats:
                    organized["images"].append(material)
            
            logger.info(f"素材整理完成: 视频{len(organized['videos'])}个, "
                       f"音频{len(organized['audios'])}个, "
                       f"图片{len(organized['images'])}个")
            
            return {
                "status": "success",
                "message": "素材整理完成",
                "organized_materials": organized,
                "total_count": len(materials)
            }
            
        except Exception as e:
            logger.error(f"整理素材时发生错误: {e}")
            return {
                "status": "error",
                "message": f"整理素材时发生错误: {str(e)}"
            }

    async def cleanup_workspace(self) -> Dict[str, Any]:
        """
        清理工作空间
        
        Returns:
            清理结果
        """
        logger.info("开始清理工作空间")
        
        try:
            # 清理处理目录
            if os.path.exists(self.processed_dir):
                shutil.rmtree(self.processed_dir)
                os.makedirs(self.processed_dir, exist_ok=True)
            
            logger.info("工作空间清理完成")
            
            return {
                "status": "success",
                "message": "工作空间清理完成"
            }
            
        except Exception as e:
            logger.error(f"清理工作空间时发生错误: {e}")
            return {
                "status": "error",
                "message": f"清理工作空间时发生错误: {str(e)}"
            }


if __name__ == "__main__":
    # 简单测试
    async def test():
        manager = MaterialManager()
        result = await manager.process_material("demo/sample_video.mp4")
        print(f"测试结果: {result}")
    
    asyncio.run(test())
