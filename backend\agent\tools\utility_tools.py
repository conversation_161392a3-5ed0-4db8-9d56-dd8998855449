# backend.agent.tools.utility_tools

import datetime
import hashlib
import json
import logging
import os
import random
import shutil
import string
from typing import Any, Dict, List, Optional, Union

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


class FileHelper:
    """
    文件操作辅助工具，提供文件管理、元数据处理等功能。
    """

    def __init__(self, base_dir: str = None):
        """
        初始化文件辅助工具。

        Args:
            base_dir: 基础目录
        """
        self.base_dir = base_dir or os.getcwd()
        logger.info("FileHelper 初始化完成。基础目录: {self.base_dir}")

    def ensure_directory(self, directory: str) -> str:
        """
        确保目录存在，如果不存在则创建。

        Args:
            directory: 目录路径，可以是相对于基础目录的路径

        Returns:
            完整的目录路径
        """
        if not os.path.isabs(directory):
            directory = os.path.join(self.base_dir, directory)

        if not os.path.exists(directory):
            os.makedirs(directory)
            logger.info("创建目录: {directory}")

        return directory

    def list_files(self, directory: str, pattern: str = "*", recursive: bool = False) -> List[str]:
        """
        列出目录中的文件。

        Args:
            directory: 目录路径，可以是相对于基础目录的路径
            pattern: 文件匹配模式
            recursive: 是否递归搜索子目录

        Returns:
            文件路径列表
        """
        import glob

        if not os.path.isabs(directory):
            directory = os.path.join(self.base_dir, directory)

        if recursive:
            search_pattern = os.path.join(directory, "**", pattern)
            files = glob.glob(search_pattern, recursive=True)
        else:
            search_pattern = os.path.join(directory, pattern)
            files = glob.glob(search_pattern)

        # 过滤出文件（排除目录）
        files = [f for f in files if os.path.isfile(f)]

        logger.info("在 {directory} 中找到 {len(files)} 个文件 (模式: {pattern}, 递归: {recursive})")
        return files

    def copy_file(self, source: str, destination: str, overwrite: bool = True) -> str:
        """
        复制文件。

        Args:
            source: 源文件路径
            destination: 目标文件路径
            overwrite: 如果目标文件已存在，是否覆盖

        Returns:
            目标文件路径
        """
        if not os.path.isabs(source):
            source = os.path.join(self.base_dir, source)

        if not os.path.isabs(destination):
            destination = os.path.join(self.base_dir, destination)

        # 确保目标目录存在
        destination_dir = os.path.dirname(destination)
        if not os.path.exists(destination_dir):
            os.makedirs(destination_dir)

        # 检查目标文件是否已存在
        if os.path.exists(destination) and not overwrite:
            logger.warning("目标文件已存在且不允许覆盖: {destination}")
            return destination

        # 复制文件
        shutil.copy2(source, destination)
        logger.info("文件复制完成: {source} -> {destination}")

        return destination

    def move_file(self, source: str, destination: str, overwrite: bool = True) -> str:
        """
        移动文件。

        Args:
            source: 源文件路径
            destination: 目标文件路径
            overwrite: 如果目标文件已存在，是否覆盖

        Returns:
            目标文件路径
        """
        if not os.path.isabs(source):
            source = os.path.join(self.base_dir, source)

        if not os.path.isabs(destination):
            destination = os.path.join(self.base_dir, destination)

        # 确保目标目录存在
        destination_dir = os.path.dirname(destination)
        if not os.path.exists(destination_dir):
            os.makedirs(destination_dir)

        # 检查目标文件是否已存在
        if os.path.exists(destination) and not overwrite:
            logger.warning("目标文件已存在且不允许覆盖: {destination}")
            return destination

        # 如果目标文件已存在且允许覆盖，先删除目标文件
        if os.path.exists(destination) and overwrite:
            os.remove(destination)

        # 移动文件
        shutil.move(source, destination)
        logger.info("文件移动完成: {source} -> {destination}")

        return destination

    def delete_file(self, file_path: str, secure: bool = False) -> bool:
        """
        删除文件。

        Args:
            file_path: 文件路径
            secure: 是否安全删除（覆盖文件内容后再删除）

        Returns:
            是否成功删除
        """
        if not os.path.isabs(file_path):
            file_path = os.path.join(self.base_dir, file_path)

        if not os.path.exists(file_path):
            logger.warning("要删除的文件不存在: {file_path}")
            return False

        if secure:
            # 安全删除：先用随机数据覆盖文件内容
            try:
                file_size = os.path.getsize(file_path)
                with open(file_path, "wb") as f:
                    f.write(os.urandom(file_size))
            except Exception as e:
                logger.warning("安全删除时覆盖文件内容失败: {e}")

        # 删除文件
        try:
            os.remove(file_path)
            logger.info("文件删除完成: {file_path}")
            return True
        except Exception as e:
            logger.error("文件删除失败: {file_path}, 错误: {e}")
            return False

    def get_file_metadata(self, file_path: str) -> Dict[str, Any]:
        """
        获取文件元数据。

        Args:
            file_path: 文件路径

        Returns:
            文件元数据字典
        """
        if not os.path.isabs(file_path):
            file_path = os.path.join(self.base_dir, file_path)

        if not os.path.exists(file_path):
            logger.warning(f"要获取元数据的文件不存在: {file_path}")
            return {}

        try:
            stat_info = os.stat(file_path)

            # 计算文件哈希值
            md5_hash = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    md5_hash.update(chunk)

            metadata = {
                "path": file_path,
                "filename": os.path.basename(file_path),
                "directory": os.path.dirname(file_path),
                "size": stat_info.st_size,
                "size_human": self._format_size(stat_info.st_size),
                "created_time": datetime.datetime.fromtimestamp(stat_info.st_ctime).isoformat(),
                "modified_time": datetime.datetime.fromtimestamp(stat_info.st_mtime).isoformat(),
                "accessed_time": datetime.datetime.fromtimestamp(stat_info.st_atime).isoformat(),
                "extension": os.path.splitext(file_path)[1].lower(),
                "md5": md5_hash.hexdigest(),
            }

            logger.info("获取文件元数据完成: {file_path}")
            return metadata

        except Exception as e:
            logger.error(f"获取文件元数据失败: {file_path}, 错误: {e}")
            return {}

    def _format_size(self, size_bytes: int) -> str:
        """将字节大小格式化为人类可读的形式"""
        for unit in ["B", "KB", "MB", "GB", "TB"]:
            if size_bytes < 1024.0:
                return "{size_bytes:.2f} {unit}"
            size_bytes /= 1024.0
        return "{size_bytes:.2f} PB"

    def read_json(self, file_path: str) -> Dict[str, Any]:
        """
        读取JSON文件。

        Args:
            file_path: 文件路径

        Returns:
            JSON数据字典
        """
        if not os.path.isabs(file_path):
            file_path = os.path.join(self.base_dir, file_path)

        if not os.path.exists(file_path):
            logger.warning(f"要读取的JSON文件不存在: {file_path}")
            return {}

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            logger.info("JSON文件读取完成: {file_path}")
            return data
        except json.JSONDecodeError as e:
            logger.error(f"JSON文件解析失败: {file_path}, 错误: {e}")
            return {}
        except Exception as e:
            logger.error(f"JSON文件读取失败: {file_path}, 错误: {e}")
            return {}

    def write_json(self, file_path: str, data: Dict[str, Any], indent: int = 4) -> bool:
        """
        写入JSON文件。

        Args:
            file_path: 文件路径
            data: 要写入的数据
            indent: 缩进空格数

        Returns:
            是否成功写入
        """
        if not os.path.isabs(file_path):
            file_path = os.path.join(self.base_dir, file_path)

        # 确保目录存在
        directory = os.path.dirname(file_path)
        if not os.path.exists(directory):
            os.makedirs(directory)

        try:
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=indent)
            logger.info("JSON文件写入完成: {file_path}")
            return True
        except Exception as e:
            logger.error("JSON文件写入失败: {file_path}, 错误: {e}")
            return False

    def read_text(self, file_path: str, encoding: str = "utf-8") -> str:
        """
        读取文本文件。

        Args:
            file_path: 文件路径
            encoding: 文件编码

        Returns:
            文件内容
        """
        if not os.path.isabs(file_path):
            file_path = os.path.join(self.base_dir, file_path)

        if not os.path.exists(file_path):
            logger.warning("要读取的文本文件不存在: {file_path}")
            return ""

        try:
            with open(file_path, "r", encoding=encoding) as f:
                content = f.read()
            logger.info("文本文件读取完成: {file_path}")
            return content
        except UnicodeDecodeError:
            logger.warning("文件编码不匹配，尝试使用二进制模式读取: {file_path}")
            try:
                with open(file_path, "rb") as f:
                    content = f.read().decode(encoding, errors="replace")
                return content
            except Exception as e:
                logger.error("二进制模式读取失败: {file_path}, 错误: {e}")
                return ""
        except Exception as e:
            logger.error("文本文件读取失败: {file_path}, 错误: {e}")
            return ""

    def write_text(self, file_path: str, content: str, encoding: str = "utf-8") -> bool:
        """
        写入文本文件。

        Args:
            file_path: 文件路径
            content: 要写入的内容
            encoding: 文件编码

        Returns:
            是否成功写入
        """
        if not os.path.isabs(file_path):
            file_path = os.path.join(self.base_dir, file_path)

        # 确保目录存在
        directory = os.path.dirname(file_path)
        if not os.path.exists(directory):
            os.makedirs(directory)

        try:
            with open(file_path, "w", encoding=encoding) as f:
                f.write(content)
            logger.info("文本文件写入完成: {file_path}")
            return True
        except Exception as e:
            logger.error("文本文件写入失败: {file_path}, 错误: {e}")
            return False


class ConfigManager:
    """
    配置管理工具，提供配置加载、保存等功能。
    """

    def __init__(self, config_dir: str = None, default_config: Dict[str, Any] = None):
        """
        初始化配置管理工具。

        Args:
            config_dir: 配置文件目录
            default_config: 默认配置
        """
        self.config_dir = config_dir or os.path.join(os.getcwd(), "config")
        self.default_config = default_config or {}
        self.config = {}

        # 确保配置目录存在
        if not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)

        logger.info("ConfigManager 初始化完成。配置目录: {self.config_dir}")

    def load_config(self, config_name: str = "default") -> Dict[str, Any]:
        """
        加载配置。

        Args:
            config_name: 配置名称

        Returns:
            配置字典
        """
        config_path = os.path.join(self.config_dir, "{config_name}.json")

        if not os.path.exists(config_path):
            logger.warning("配置文件不存在: {config_path}，将使用默认配置")
            self.config = self.default_config.copy()
            return self.config

        try:
            with open(config_path, "r", encoding="utf-8") as f:
                self.config = json.load(f)
            logger.info("配置加载完成: {config_path}")
            return self.config
        except json.JSONDecodeError as e:
            logger.error("配置文件解析失败: {config_path}, 错误: {e}")
            self.config = self.default_config.copy()
            return self.config
        except Exception as e:
            logger.error("配置文件加载失败: {config_path}, 错误: {e}")
            self.config = self.default_config.copy()
            return self.config

    def save_config(self, config_name: str = "default") -> bool:
        """
        保存配置。

        Args:
            config_name: 配置名称

        Returns:
            是否成功保存
        """
        config_path = os.path.join(self.config_dir, "{config_name}.json")

        try:
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
            logger.info("配置保存完成: {config_path}")
            return True
        except Exception as e:
            logger.error("配置保存失败: {config_path}, 错误: {e}")
            return False

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置项。

        Args:
            key: 配置项键名
            default: 默认值

        Returns:
            配置项值
        """
        return self.config.get(key, default)

    def set(self, key: str, value: Any) -> None:
        """
        设置配置项。

        Args:
            key: 配置项键名
            value: 配置项值
        """
        self.config[key] = value

    def update(self, updates: Dict[str, Any]) -> None:
        """
        批量更新配置项。

        Args:
            updates: 要更新的配置项字典
        """
        self.config.update(updates)

    def reset(self) -> None:
        """重置为默认配置"""
        self.config = self.default_config.copy()
        logger.info("配置已重置为默认值")


class IDGenerator:
    """
    ID生成器，提供各种ID生成方法。
    """

    @staticmethod
    def generate_uuid() -> str:
        """
        生成UUID。

        Returns:
            UUID字符串
        """
        import uuid

        return str(uuid.uuid4())

    @staticmethod
    def generate_short_id(length: int = 8) -> str:
        """
        生成短ID。

        Args:
            length: ID长度

        Returns:
            短ID字符串
        """
        chars = string.ascii_letters + string.digits
        return "".join(random.choice(chars) for _ in range(length))

    @staticmethod
    def generate_timestamp_id(prefix: str = "") -> str:
        """
        生成基于时间戳的ID。

        Args:
            prefix: ID前缀

        Returns:
            时间戳ID字符串
        """
        timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S%")
        random_suffix = "".join(random.choice(string.digits) for _ in range(4))
        return "{prefix}{timestamp}{random_suffix}"

    @staticmethod
    def generate_hash_id(data: Union[str, bytes], length: int = 16) -> str:
        """
        生成基于哈希的ID。

        Args:
            data: 用于生成哈希的数据
            length: ID长度

        Returns:
            哈希ID字符串
        """
        if isinstance(data, str):
            data = data.encode("utf-8")

        hash_obj = hashlib.sha256(data)
        return hash_obj.hexdigest()[:length]


class TaskManager:
    """
    任务管理器，提供任务创建、状态跟踪等功能。
    """

    def __init__(self, tasks_dir: str = None):
        """
        初始化任务管理器。

        Args:
            tasks_dir: 任务数据目录
        """
        self.tasks_dir = tasks_dir or os.path.join(os.getcwd(), "tasks")
        self.tasks = {}

        # 确保任务目录存在
        if not os.path.exists(self.tasks_dir):
            os.makedirs(self.tasks_dir)

        # 加载现有任务
        self._load_tasks()

        logger.info("TaskManager 初始化完成。任务目录: {self.tasks_dir}, 已加载 {len(self.tasks)} 个任务")

    def _load_tasks(self) -> None:
        """加载现有任务"""
        task_files = [f for f in os.listdir(self.tasks_dir) if f.endswith(".json")]

        for task_file in task_files:
            try:
                task_path = os.path.join(self.tasks_dir, task_file)
                with open(task_path, "r", encoding="utf-8") as f:
                    task_data = json.load(f)

                task_id = os.path.splitext(task_file)[0]
                self.tasks[task_id] = task_data
            except Exception as e:
                logger.error("加载任务文件失败: {task_file}, 错误: {e}")

    def create_task(self, task_type: str, params: Dict[str, Any] = None) -> str:
        """
        创建新任务。

        Args:
            task_type: 任务类型
            params: 任务参数

        Returns:
            任务ID
        """
        task_id = IDGenerator.generate_timestamp_id(f"{task_type}_")

        task_data = {
            "id": task_id,
            "type": task_type,
            "params": params or {},
            "status": "created",
            "progress": 0,
            "created_at": datetime.datetime.now().isoformat(),
            "updated_at": datetime.datetime.now().isoformat(),
            "result": None,
            "error": None,
        }

        self.tasks[task_id] = task_data
        self._save_task(task_id)

        logger.info("创建任务: {task_id}, 类型: {task_type}")
        return task_id

    def update_task_status(
        self, task_id: str, status: str, progress: float = None, result: Any = None, error: str = None
    ) -> bool:
        """
        更新任务状态。

        Args:
            task_id: 任务ID
            status: 任务状态
            progress: 任务进度 (0-100)
            result: 任务结果
            error: 错误信息

        Returns:
            是否成功更新
        """
        if task_id not in self.tasks:
            logger.warning("要更新的任务不存在: {task_id}")
            return False

        task = self.tasks[task_id]
        task["status"] = status
        task["updated_at"] = datetime.datetime.now().isoformat()

        if progress is not None:
            task["progress"] = progress

        if result is not None:
            task["result"] = result

        if error is not None:
            task["error"] = error

        self._save_task(task_id)

        logger.info("更新任务状态: {task_id}, 状态: {status}, 进度: {progress}")
        return True

    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务信息。

        Args:
            task_id: 任务ID

        Returns:
            任务信息字典
        """
        return self.tasks.get(task_id)

    def list_tasks(self, task_type: str = None, status: str = None) -> List[Dict[str, Any]]:
        """
        列出任务。

        Args:
            task_type: 过滤的任务类型
            status: 过滤的任务状态

        Returns:
            任务列表
        """
        tasks = list(self.tasks.values())

        if task_type:
            tasks = [t for t in tasks if t["type"] == task_type]

        if status:
            tasks = [t for t in tasks if t["status"] == status]

        # 按创建时间排序
        tasks.sort(key=lambda t: t["created_at"], reverse=True)

        return tasks

    def delete_task(self, task_id: str) -> bool:
        """
        删除任务。

        Args:
            task_id: 任务ID

        Returns:
            是否成功删除
        """
        if task_id not in self.tasks:
            logger.warning("要删除的任务不存在: {task_id}")
            return False

        # 删除任务文件
        task_path = os.path.join(self.tasks_dir, "{task_id}.json")
        if os.path.exists(task_path):
            try:
                os.remove(task_path)
            except Exception as e:
                logger.error("删除任务文件失败: {task_path}, 错误: {e}")

        # 从内存中删除任务
        del self.tasks[task_id]

        logger.info("删除任务: {task_id}")
        return True

    def _save_task(self, task_id: str) -> bool:
        """
        保存任务到文件。

        Args:
            task_id: 任务ID

        Returns:
            是否成功保存
        """
        task_path = os.path.join(self.tasks_dir, "{task_id}.json")

        try:
            with open(task_path, "w", encoding="utf-8") as f:
                json.dump(self.tasks[task_id], f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            logger.error("保存任务文件失败: {task_path}, 错误: {e}")
            return False
