{"xigua": {"trending_api": "https://api.example.com/xigua/trending", "revenue_api": "https://api.example.com/xigua/revenue", "category_api": "https://api.example.com/xigua/categories", "auth_required": true, "rate_limit": 100, "data_fields": ["title", "author", "play_count", "like_count", "comment_count", "share_count", "estimated_revenue"], "auth_config": {"cookie": "${XIGUA_COOKIE}", "credentials_path": "${XIGUA_CREDENTIALS_PATH}"}, "endpoints": {"video_upload": "https://api.example.com/xigua/upload", "video_publish": "https://api.example.com/xigua/publish", "video_delete": "https://api.example.com/xigua/delete", "video_analytics": "https://api.example.com/xigua/analytics", "user_profile": "https://api.example.com/xigua/user", "revenue_data": "https://api.example.com/xigua/revenue", "comment_list": "https://api.example.com/xigua/comments", "comment_reply": "https://api.example.com/xigua/reply"}}, "toutiao": {"trending_api": "https://api.example.com/toutiao/trending", "revenue_api": "https://api.example.com/toutiao/revenue", "category_api": "https://api.example.com/toutiao/categories", "auth_required": true, "rate_limit": 120, "data_fields": ["title", "author", "view_count", "like_count", "comment_count", "share_count", "estimated_revenue"], "auth_config": {"cookie": "${TOUTIAO_COOKIE}", "credentials_path": "${TOUTIAO_CREDENTIALS_PATH}"}, "endpoints": {"article_publish": "https://api.example.com/toutiao/article/publish", "video_upload": "https://api.example.com/toutiao/video/upload", "video_publish": "https://api.example.com/toutiao/video/publish", "content_delete": "https://api.example.com/toutiao/content/delete", "content_analytics": "https://api.example.com/toutiao/analytics", "user_profile": "https://api.example.com/toutiao/user", "revenue_data": "https://api.example.com/toutiao/revenue", "comment_list": "https://api.example.com/toutiao/comments", "comment_reply": "https://api.example.com/toutiao/reply"}}, "xiaohongshu": {"trending_api": "https://api.example.com/xiaohongshu/trending", "revenue_api": "https://api.example.com/xiaohongshu/revenue", "category_api": "https://api.example.com/xiaohongshu/categories", "auth_required": true, "rate_limit": 100, "data_fields": ["title", "author", "view_count", "like_count", "collect_count", "comment_count", "share_count", "estimated_revenue"], "auth_config": {"cookie": "${XIAOHONGSHU_COOKIE}", "credentials_path": "${XIAOHONGSHU_CREDENTIALS_PATH}"}, "endpoints": {"note_publish": "https://api.example.com/xiaohongshu/note/publish", "note_delete": "https://api.example.com/xiaohongshu/note/delete", "note_analytics": "https://api.example.com/xiaohongshu/analytics", "user_profile": "https://api.example.com/xiaohongshu/user", "revenue_data": "https://api.example.com/xiaohongshu/revenue", "comment_list": "https://api.example.com/xiaohongshu/comments", "comment_reply": "https://api.example.com/xiaohongshu/reply"}}, "youtube": {"trending_api": "https://api.example.com/youtube/trending", "revenue_api": "https://api.example.com/youtube/revenue", "category_api": "https://api.example.com/youtube/categories", "auth_required": true, "rate_limit": 100, "data_fields": ["title", "author", "play_count", "like_count", "comment_count", "share_count", "estimated_revenue"], "auth_config": {"api_key": "${YOUTUBE_API_KEY}", "client_id": "${YOUTUBE_CLIENT_ID}", "client_secret": "${YOUTUBE_CLIENT_SECRET}", "refresh_token": "${YOUTUBE_REFRESH_TOKEN}", "token_refresh_url": "https://oauth2.googleapis.com/token"}, "endpoints": {"video_upload": "https://www.googleapis.com/upload/youtube/v3/videos", "video_publish": "https://www.googleapis.com/youtube/v3/videos", "video_delete": "https://www.googleapis.com/youtube/v3/videos", "video_analytics": "https://youtubeanalytics.googleapis.com/v2/reports", "user_profile": "https://www.googleapis.com/youtube/v3/channels", "comment_list": "https://www.googleapis.com/youtube/v3/commentThreads", "comment_reply": "https://www.googleapis.com/youtube/v3/comments"}}}