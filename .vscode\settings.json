{"zencoder.enableRepoIndexing": true, "python.defaultInterpreterPath": "c:/Users/<USER>/Desktop/IntelliCutAgent/venv/Scripts/python.exe", "python.analysis.extraPaths": ["c:/Users/<USER>/Desktop/IntelliCutAgent", "c:/Users/<USER>/Desktop/IntelliCutAgent/venv/Lib/site-packages", "c:/Users/<USER>/Desktop/IntelliCutAgent/backend/agent/smart_editor/moviepy-stubs"], "python.envFile": "${workspaceFolder}/.env", "python.analysis.diagnosticMode": "workspace", "python.analysis.typeCheckingMode": "basic", "python.analysis.autoImportCompletions": true, "python.analysis.indexing": true, "python.analysis.autoSearchPaths": true, "python.analysis.stubPath": "c:/Users/<USER>/Desktop/IntelliCutAgent/backend/agent/smart_editor/moviepy-stubs", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.analysis.diagnosticSeverityOverrides": {"reportMissingImports": "none", "reportArgumentType": "warning", "reportCallIssue": "warning"}}