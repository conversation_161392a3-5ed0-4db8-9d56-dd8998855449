import datetime
import json
import logging
import os
import threading
import time
import uuid
from typing import Any
from typing import Dict
from typing import List

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

class BatchPublisher:
    """
    批量发布器：支持将剪辑好的视频批量发布到多个平台，并管理发布状态和数据。
    """

    def __init__(self, config_dir: str = None, data_dir: str = None):
        """
        初始化批量发布器。

        Args:
            config_dir: 配置文件目录，默认为当前目录下的 'config/platforms'
            data_dir: 数据存储目录，默认为当前目录下的 'data/publish'
        """
        self.config_dir = config_dir or os.path.join(os.getcwd(), "config", "platforms")
        self.data_dir = data_dir or os.path.join(os.getcwd(), "data", "publish")

        # 确保目录存在
        os.makedirs(self.config_dir, exist_ok=True)
        os.makedirs(self.data_dir, exist_ok=True)

        # 发布任务存储
        self.tasks_file = os.path.join(self.data_dir, "publish_tasks.json")
        self.tasks = self._load_tasks()

        # 平台配置
        self.platform_configs = self._load_platform_configs()

        # 支持的平台列表
        self.supported_platforms = list(self.platform_configs.keys())

        # 发布任务线程
        self.publish_threads = {}

        logger.info("批量发布器初始化完成。支持的平台: {self.supported_platforms}")

    def _load_tasks(self) -> Dict[str, Any]:
        """加载发布任务数据"""
        if os.path.exists(self.tasks_file):
            try:
                with open(self.tasks_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载发布任务数据失败: {e}")
        return {}

    def _save_tasks(self):
        """保存发布任务数据"""
        try:
            with open(self.tasks_file, "w", encoding="utf-8") as f:
                json.dump(self.tasks, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error("保存发布任务数据失败: {e}")

    def _load_platform_configs(self) -> Dict[str, Any]:
        """加载平台配置"""
        configs = {}

        # 默认平台配置
        default_configs = {
            "douyin": {
                "display_name": "抖音",
                "api_base_url": "https://open.douyin.com/api/",
                "auth_url": "https://open.douyin.com/platform/oauth/connect/",
                "upload_url": "https://open.douyin.com/api/video/upload",
                "publish_url": "https://open.douyin.com/api/video/publish",
                "max_duration": 180,  # 秒
                "max_file_size": 500,  # MB
                "supported_formats": ["mp4"],
                "aspect_ratios": ["9:16", "1:1"],
                "max_resolution": "1080p",
            },
            "kuaishou": {
                "display_name": "快手",
                "api_base_url": "https://open.kuaishou.com/openapi/",
                "auth_url": "https://open.kuaishou.com/oauth2/authorize",
                "upload_url": "https://open.kuaishou.com/openapi/video/upload",
                "publish_url": "https://open.kuaishou.com/openapi/video/publish",
                "max_duration": 57,  # 秒
                "max_file_size": 500,  # MB
                "supported_formats": ["mp4"],
                "aspect_ratios": ["9:16", "1:1"],
                "max_resolution": "1080p",
            },
            "bilibili": {
                "display_name": "哔哩哔哩",
                "api_base_url": "https://api.bilibili.com/x/",
                "auth_url": "https://api.bilibili.com/x/passport-login/oauth2/authorize",
                "upload_url": "https://api.bilibili.com/x/upload/video",
                "publish_url": "https://api.bilibili.com/x/upload/complete",
                "max_duration": 1800,  # 秒
                "max_file_size": 2048,  # MB
                "supported_formats": ["mp4", "flv"],
                "aspect_ratios": ["16:9", "4:3", "1:1"],
                "max_resolution": "4K",
            },
            "weibo": {
                "display_name": "微博",
                "api_base_url": "https://api.weibo.com/2/",
                "auth_url": "https://api.weibo.com/oauth2/authorize",
                "upload_url": "https://api.weibo.com/2/statuses/upload_url_text.json",
                "publish_url": "https://api.weibo.com/2/statuses/share.json",
                "max_duration": 300,  # 秒
                "max_file_size": 500,  # MB
                "supported_formats": ["mp4"],
                "aspect_ratios": ["16:9", "1:1", "9:16"],
                "max_resolution": "1080p",
            },
            "xiaohongshu": {
                "display_name": "小红书",
                "api_base_url": "https://ark.xiaohongshu.com/ark/open_api/v3/",
                "auth_url": "https://ark.xiaohongshu.com/ark/open_api/v3/oauth2/authorize",
                "upload_url": "https://ark.xiaohongshu.com/ark/open_api/v3/content/upload",
                "publish_url": "https://ark.xiaohongshu.com/ark/open_api/v3/content/publish",
                "max_duration": 300,  # 秒
                "max_file_size": 500,  # MB
                "supported_formats": ["mp4"],
                "aspect_ratios": ["9:16", "1:1"],
                "max_resolution": "1080p",
            },
            "youtube": {
                "display_name": "YouTube",
                "api_base_url": "https://www.googleapis.com/youtube/v3/",
                "auth_url": "https://accounts.google.com/o/oauth2/auth",
                "upload_url": "https://www.googleapis.com/upload/youtube/v3/videos",
                "publish_url": "https://www.googleapis.com/youtube/v3/videos",
                "max_duration": 43200,  # 秒 (12小时)
                "max_file_size": 128000,  # MB (128GB)
                "supported_formats": ["mp4", "mov", "avi", "wmv", "flv", "3gp", "webm"],
                "aspect_ratios": ["16:9", "4:3"],
                "max_resolution": "8K",
            },
            "tiktok": {
                "display_name": "TikTok",
                "api_base_url": "https://open-api.tiktok.com/",
                "auth_url": "https://open-api.tiktok.com/platform/oauth/connect/",
                "upload_url": "https://open-api.tiktok.com/video/upload/",
                "publish_url": "https://open-api.tiktok.com/video/publish/",
                "max_duration": 180,  # 秒
                "max_file_size": 500,  # MB
                "supported_formats": ["mp4"],
                "aspect_ratios": ["9:16"],
                "max_resolution": "1080p",
            },
        }

        # 加载自定义平台配置
        for platform in default_configs.keys():
            config_file = os.path.join(self.config_dir, "{platform}.json")
            if os.path.exists(config_file):
                try:
                    with open(config_file, "r", encoding="utf-8") as f:
                        custom_config = json.load(f)
                    # 合并默认配置和自定义配置
                    configs[platform] = {**default_configs[platform], **custom_config}
                except Exception as e:
                    logger.error("加载平台 {platform} 配置失败: {e}")
                    configs[platform] = default_configs[platform]
            else:
                configs[platform] = default_configs[platform]

        return configs

    def publish_to_platforms(self, video_path: str, platforms: List[str], metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        将视频批量发布到指定平台。

        Args:
            video_path: 视频文件路径
            platforms: 目标平台列表
            metadata: 发布元数据，包含标题、描述、标签等

        Returns:
            发布结果字典，包含每个平台的发布状态
        """
        if not os.path.exists(video_path):
            logger.error(f"视频文件不存在: {video_path}")
            return {"status": "error", "message": "视频文件不存在: {video_path}"}

        # 验证平台
        valid_platforms = [p for p in platforms if p in self.supported_platforms]
        if not valid_platforms:
            logger.error(f"没有有效的发布平台: {platforms}")
            return {"status": "error", "message": f"没有有效的发布平台: {platforms}"}

        # 创建发布任务
        task_id = str(uuid.uuid4())
        task = {
            "id": task_id,
            "video_path": video_path,
            "platforms": valid_platforms,
            "metadata": metadata,
            "status": "pending",
            "created_at": datetime.datetime.now().isoformat(),
            "updated_at": datetime.datetime.now().isoformat(),
            "results": {},
        }

        # 保存任务
        self.tasks[task_id] = task
        self._save_tasks()

        # 启动发布线程
        thread = threading.Thread(target=self._publish_task, args=(task_id,))
        thread.daemon = True
        thread.start()
        self.publish_threads[task_id] = thread

        logger.info(f"已创建发布任务 {task_id}，目标平台: {valid_platforms}")

        # 返回初始结果
        results = {}
        for platform in valid_platforms:
            results[platform] = {"status": "pending", "message": "发布任务已创建，正在处理中"}

        return results

    def _publish_task(self, task_id: str):
        """
        执行发布任务

        Args:
            task_id: 任务ID
        """
        if task_id not in self.tasks:
            logger.error("发布任务不存在: {task_id}")
            return

        task = self.tasks[task_id]
        task["video_path"]
        platforms = task["platforms"]
        task["metadata"]

        # 更新任务状态
        task["status"] = "processing"
        task["updated_at"] = datetime.datetime.now().isoformat()
        self._save_tasks()

        # 处理每个平台
        for platform in platforms:
            try:
                logger.info("开始发布到平台 {platform}...")

                # 更新平台状态
                if platform not in task["results"]:
                    task["results"][platform] = {"status": "processing", "message": "正在处理中"}
                    self._save_tasks()

                # 模拟发布过程
                # 在实际实现中，这里会调用平台的API进行视频上传和发布
                time.sleep(2)  # 模拟上传和处理时间

                # 生成模拟的发布结果
                video_id = "video_{platform}_{int(time.time())}"
                video_url = "https://{platform}.com/watch?v={video_id}"

                # 更新平台结果
                task["results"][platform] = {
                    "status": "completed",
                    "video_id": video_id,
                    "url": video_url,
                    "message": "发布成功",
                    "completed_at": datetime.datetime.now().isoformat(),
                }

                logger.info("平台 {platform} 发布成功: {video_url}")

            except Exception as e:
                logger.error("平台 {platform} 发布失败: {e}")
                task["results"][platform] = {
                    "status": "failed",
                    "message": "发布失败: {str(e)}",
                    "completed_at": datetime.datetime.now().isoformat(),
                }

            # 保存任务状态
            self._save_tasks()

        # 检查所有平台是否完成
        all_completed = all(result.get("status") in ["completed", "failed"] for result in task["results"].values())

        if all_completed:
            # 更新任务状态
            task["status"] = "completed"
            task["updated_at"] = datetime.datetime.now().isoformat()
            task["completed_at"] = datetime.datetime.now().isoformat()
            self._save_tasks()

            logger.info("发布任务 {task_id} 已完成")

    def check_publish_status(self, task_id: str) -> Dict[str, Any]:
        """
        检查发布任务状态

        Args:
            task_id: 任务ID

        Returns:
            任务状态字典
        """
        if task_id not in self.tasks:
            logger.error(f"发布任务不存在: {task_id}")
            return {"status": "error", "message": f"发布任务不存在: {task_id}"}

        task = self.tasks[task_id]

        # 返回任务状态
        return {
            "id": task_id,
            "status": task["status"],
            "created_at": task["created_at"],
            "updated_at": task["updated_at"],
            "completed_at": task.get("completed_at"),
            "platforms": task["platforms"],
            "results": task["results"],
        }

    def get_all_tasks(self, status: str = None, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取所有发布任务

        Args:
            status: 过滤状态，可选值: pending, processing, completed, failed
            limit: 返回的最大任务数量

        Returns:
            任务列表
        """
        tasks = list(self.tasks.values())

        # 按状态过滤
        if status:
            tasks = [task for task in tasks if task["status"] == status]

        # 按创建时间排序
        tasks.sort(key=lambda x: x["created_at"], reverse=True)

        # 限制数量
        tasks = tasks[:limit]

        return tasks

    def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """
        取消发布任务

        Args:
            task_id: 任务ID

        Returns:
            操作结果
        """
        if task_id not in self.tasks:
            logger.error(f"发布任务不存在: {task_id}")
            return {"status": "error", "message": "发布任务不存在: {task_id}"}

        task = self.tasks[task_id]

        # 只能取消待处理或处理中的任务
        if task["status"] not in ["pending", "processing"]:
            logger.error(f"无法取消已完成的任务: {task_id}")
            return {"status": "error", "message": "无法取消已完成的任务: {task_id}"}

        # 更新任务状态
        task["status"] = "cancelled"
        task["updated_at"] = datetime.datetime.now().isoformat()
        self._save_tasks()

        logger.info(f"发布任务 {task_id} 已取消")

        return {"status": "success", "message": "发布任务 {task_id} 已取消"}

    def delete_task(self, task_id: str) -> Dict[str, Any]:
        """
        删除发布任务

        Args:
            task_id: 任务ID

        Returns:
            操作结果
        """
        if task_id not in self.tasks:
            logger.error(f"发布任务不存在: {task_id}")
            return {"status": "error", "message": "发布任务不存在: {task_id}"}

        # 删除任务
        del self.tasks[task_id]
        self._save_tasks()

        logger.info(f"发布任务 {task_id} 已删除")

        return {"status": "success", "message": "发布任务 {task_id} 已删除"}

    def get_platform_info(self, platform: str) -> Dict[str, Any]:
        """
        获取平台信息

        Args:
            platform: 平台名称

        Returns:
            平台信息字典
        """
        if platform not in self.platform_configs:
            logger.error(f"平台不存在: {platform}")
            return {"status": "error", "message": "平台不存在: {platform}"}

        return self.platform_configs[platform]

    def get_supported_platforms(self) -> List[Dict[str, Any]]:
        """
        获取支持的平台列表

        Returns:
            平台列表
        """
        platforms = []
        for platform, config in self.platform_configs.items():
            platforms.append(
                {
                    "id": platform,
                    "name": config["display_name"],
                    "max_duration": config["max_duration"],
                    "max_file_size": config["max_file_size"],
                    "supported_formats": config["supported_formats"],
                    "aspect_ratios": config["aspect_ratios"],
                }
            )

        return platforms
