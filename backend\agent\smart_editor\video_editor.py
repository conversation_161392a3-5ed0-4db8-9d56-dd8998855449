#!/usr/bin/env python3
"""
video_editor module
视频编辑器类，提供视频剪辑、特效添加、转场效果等功能。
"""

import atexit
import logging
import os
import shutil
import tempfile
import traceback
from typing import Any, Dict, List, Optional, Tuple

import cv2
import moviepy.editor as mp
import moviepy.video.fx.all as vfx
import numpy as np

from backend.agent.smart_editor.parallel_processor import ParallelVideoProcessor
from backend.agent.smart_editor.scene_detection import ()
    extract_scenes_content,
    extract_scenes_edge,
    extract_scenes_threshold)

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


class VideoEditor:
    """
    视频编辑器类，提供视频剪辑、特效添加、转场效果等功能。
    """

    def __init__(self, temp_dir: Optional[str] = None, auto_cleanup: bool = True):
        """
        初始化视频编辑器。
        Args:
            temp_dir: 临时文件目录，如果为 None 则使用系统临时目录
            auto_cleanup: 是否在退出时自动清理临时文件
        """
        if temp_dir is None:
            self.temp_dir = os.path.join(tempfile.gettempdir(), "intellicut_temp")
        else:
            self.temp_dir = temp_dir
        os.makedirs(self.temp_dir, exist_ok=True)
        self.supported_formats = [".mp4", ".avi", ".mov", ".mkv", ".webm", ".flv"]
        self.supported_effects = {}
            "brightness": self._apply_brightness,
            "contrast": self._apply_contrast,
            "saturation": self._apply_saturation,
            "speed": self._apply_speed,
            "reverse": self._apply_reverse,
            "mirror": self._apply_mirror,
            "black_white": self._apply_black_white,
            "blur": self._apply_blur,
            "sharpen": self._apply_sharpen,
            "noise": self._apply_noise,
            "vignette": self._apply_vignette,
            "rotate": self._apply_rotate,
            "sepia": self._apply_sepia,  # 新增：复古褐色效果
            "edge_detect": self._apply_edge_detect,  # 新增：边缘检测效果
            "pixelate": self._apply_pixelate,  # 新增：像素化效果
            "time_symmetry": self._apply_time_symmetry,  # 新增：时间对称效果
        }
        self.supported_transitions = {}
            "cut": self._apply_cut_transition,
            "fade": self._apply_fade_transition,
            "wipe": self._apply_wipe_transition,
            "dissolve": self._apply_dissolve_transition,
            "zoom": self._apply_zoom_transition,
            "slide": self._apply_slide_transition}
        self.temp_files = []
        self.auto_cleanup = auto_cleanup
        if auto_cleanup:
            atexit.register(self.cleanup_temp_files)
        logger.info(f"视频编辑器初始化完成，临时目录: {self.temp_dir}")
    def load_video(self, video_path: str) -> Optional[mp.VideoFileClip]:
        if not os.path.exists(video_path):
        logger.error(f"视频文件不存在: {video_path}")
        return None
        _, ext = os.path.splitext(video_path)
        if ext.lower() not in self.supported_formats:
        logger.error(f"不支持的视频格式: {ext}")
        return None
        try:
        video_clip = mp.VideoFileClip(video_path)
        logger.info(f"视频加载成功: {video_path}, 时长: {video_clip.duration}秒, 分辨率: {video_clip.size}")
        return video_clip
        except Exception:
        logger.error("操作失败")
        return None
    def cut_video(self, video_clip: mp.VideoFileClip, start_time: float, end_time: float) -> Optional[mp.VideoFileClip]:
        try:
        if start_time < 0:
            start_time = 0
        if end_time > video_clip.duration:
            end_time = video_clip.duration
        if start_time >= end_time:
            logger.error(f"无效的时间范围: {start_time} - {end_time}")
            return None
        cut_clip = video_clip.subclip(start_time, end_time)
        logger.info(f"视频剪切成功: {start_time}秒 - {end_time}秒, 时长: {cut_clip.duration}秒")
        return cut_clip
        except Exception:
        logger.error("操作失败")
        return None
def extract_scenes():
        self, video_path: str, threshold: float = 30.0, min_scene_length: float = 1.0, method: str = "threshold"
        ) -> List[Tuple[float, float]]:
        try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.error(f"无法打开视频: {video_path}")
            return []
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        frame_count / fps
        logger.info(f"视频信息: FPS={fps}, 总帧数={frame_count}, 时长={duration}秒")
        if method == "content":
            logger.info("使用基于内容的场景检测方法")
            scenes = extract_scenes_content(cap, fps, threshold, min_scene_length)
        elif method == "edge":
            logger.info("使用基于边缘的场景检测方法")
            scenes = extract_scenes_edge(cap, fps, threshold, min_scene_length)
        else:
            logger.info("使用基于阈值的场景检测方法")
            scenes = extract_scenes_threshold(cap, fps, threshold, min_scene_length)
        cap.release()
        logger.info(f"场景检测完成，共检测到 {len(scenes)} 个场景")
        return scenes
        except Exception:
        logger.error("操作失败")
        logger.error(traceback.format_exc())
        return []
def extract_highlights():
        self, video_path: str, duration: float = 30.0, scene_threshold: float = 30.0, min_scene_length: float = 1.0
        ) -> Optional[mp.VideoFileClip]:
        try:
        video_clip = self.load_video(video_path)
        if video_clip is None:
            return None
        if video_clip.duration <= duration:
            logger.info(f"视频时长 ({video_clip.duration}秒) 小于目标时长 ({duration}秒)，返回原视频")
            return video_clip
        scenes = self.extract_scenes(video_path, scene_threshold, min_scene_length)
        if not scenes:
            logger.warning("未检测到场景，使用简单剪切方法")
            return self.cut_video(video_clip, 0, min(duration, video_clip.duration))
        scene_scores = []
        for i, (start_time, end_time) in enumerate(scenes):
            if end_time is None:
                continue
            scene_length = end_time - start_time
            length_score = min(scene_length / 5.0, 1.0)  # 长度得分，最高1分
            position_score = 1.0 - abs(2 * (start_time / video_clip.duration) - 1)  # 位置得分，中间位置得分高
            total_score = length_score * 0.5 + position_score * 0.5
            scene_scores.append((i, total_score))
        scene_scores.sort(key=lambda x: x[1], reverse=True)
        selected_scenes = []
        current_duration = 0
        for i, score in scene_scores:
            start_time, end_time = scenes[i]
            if end_time is None:
                continue
            scene_length = end_time - start_time
            if current_duration + scene_length <= duration:
                selected_scenes.append((start_time, end_time))
                current_duration += scene_length
            if current_duration >= duration:
                break
        selected_scenes.sort()
        clips = []
        for start_time, end_time in selected_scenes:
            clip = video_clip.subclip(start_time, end_time)
            clips.append(clip)
        if not clips:
            logger.warning("未选中任何场景，使用简单剪切方法")
            return self.cut_video(video_clip, 0, min(duration, video_clip.duration))
        final_clip = mp.concatenate_videoclips(clips)
        if final_clip.duration < duration:
            remaining_duration = duration - final_clip.duration
            start_clip = video_clip.subclip(0, min(remaining_duration, video_clip.duration))
            final_clip = mp.concatenate_videoclips([start_clip, final_clip])
        logger.info(f"精彩片段提取成功，时长: {final_clip.duration}秒")
        return final_clip
        except Exception:
        logger.error("操作失败")
        return None
    def apply_effect(self, video_clip: mp.VideoFileClip, effect: str, **params) -> Optional[mp.VideoFileClip]:
        if effect not in self.supported_effects:
        logger.error(f"不支持的特效: {effect}")
        return None
        try:
        effect_func = self.supported_effects[effect]
        result_clip = effect_func(video_clip, **params)
        logger.info(f"特效应用成功: {effect}")
        return result_clip
        except Exception:
        logger.error("操作失败")
        return None
def apply_transition():
        self, clip1: mp.VideoFileClip, clip2: mp.VideoFileClip, transition: str, duration: float = 1.0, **params
        ) -> Optional[mp.VideoFileClip]:
        if transition not in self.supported_transitions:
        logger.error(f"不支持的转场效果: {transition}")
        return None
        try:
        transition_func = self.supported_transitions[transition]
        result_clip = transition_func(clip1, clip2, duration, **params)
        logger.info(f"转场效果应用成功: {transition}, 时长: {duration}秒")
        return result_clip
        except Exception:
        logger.error("操作失败")
        return None
def add_audio()
        self,:
        video_clip: mp.VideoFileClip,
        audio_path: str,
        volume: float = 1.0,
        start_time: float = 0,
        loop: bool = False) -> Optional[mp.VideoFileClip]:
        if not os.path.exists(audio_path):
        logger.error(f"音频文件不存在: {audio_path}")
        return None
        try:
        audio_clip = mp.AudioFileClip(audio_path)
        audio_clip = audio_clip.volumex(volume)
        if loop and audio_clip.duration < video_clip.duration:
            loop_count = int(video_clip.duration / audio_clip.duration) + 1
            audio_clips = [audio_clip] * loop_count
            audio_clip = mp.concatenate_audioclips(audio_clips)
        audio_clip = audio_clip.subclip(0, min(audio_clip.duration, video_clip.duration - start_time))
        if start_time > 0:
            silence = mp.AudioClip(lambda t: 0, duration=start_time)
            audio_clip = mp.concatenate_audioclips([silence, audio_clip])
        if audio_clip.duration < video_clip.duration:
            silence = mp.AudioClip(lambda t: 0, duration=video_clip.duration - audio_clip.duration)
            audio_clip = mp.concatenate_audioclips([audio_clip, silence])
        result_clip = video_clip.set_audio(audio_clip)
        logger.info(f"音频添加成功: {audio_path}, 音量: {volume}")
        return result_clip
        except Exception:
        logger.error("操作失败")
        return None
def add_text()
        self,:
        video_clip: mp.VideoFileClip,
        text: str,
        font: str = "Arial",
        fontsize: int = 30,
        color: str = "white",
        position: Tuple[str, str] = ("center", "bottom"),
        start_time: float = 0,
        end_time: Optional[float] = None) -> Optional[mp.VideoFileClip]:
        try:
        if end_time is None:
            end_time = video_clip.duration
        text_clip = mp.TextClip(text, font=font, fontsize=fontsize, color=color)
        h_pos, v_pos = position
        if h_pos == "left":
            x_pos = 20
        elif h_pos == "right":
            x_pos = video_clip.size[0] - text_clip.size[0] - 20
        else:  # center
            x_pos = (video_clip.size[0] - text_clip.size[0]) // 2
        if v_pos == "top":
            y_pos = 20
        elif v_pos == "bottom":
            y_pos = video_clip.size[1] - text_clip.size[1] - 20
        else:  # center
            y_pos = (video_clip.size[1] - text_clip.size[1]) // 2
        actual_end_time = end_time if end_time is not None else video_clip.duration
        text_clip = ()
            text_clip.set_position((x_pos, y_pos)).set_duration(actual_end_time - start_time).set_start(start_time)
        )
        result_clip = mp.CompositeVideoClip([video_clip, text_clip]):
        logger.info(f"文本添加成功: {text}, 位置: {position}, 时间: {start_time}秒 - {end_time}秒")
        return result_clip
        except Exception:
        logger.error("操作失败")
        return None
def add_watermark()
        self,:
        video_clip: mp.VideoFileClip,
        watermark_path: str,
        position: Tuple[str, str] = ("right", "bottom"),
        opacity: float = 0.5) -> Optional[mp.VideoFileClip]:
        if not os.path.exists(watermark_path):
        logger.error(f"水印图片不存在: {watermark_path}")
        return None
        try:
        watermark = mp.ImageClip(watermark_path).set_opacity(opacity)
        h_pos, v_pos = position
        if h_pos == "left":
            x_pos = 20
        elif h_pos == "right":
            x_pos = video_clip.size[0] - watermark.size[0] - 20
        else:  # center
            x_pos = (video_clip.size[0] - watermark.size[0]) // 2
        if v_pos == "top":
            y_pos = 20
        elif v_pos == "bottom":
            y_pos = video_clip.size[1] - watermark.size[1] - 20
        else:  # center
            y_pos = (video_clip.size[1] - watermark.size[1]) // 2
        watermark = watermark.set_position((x_pos, y_pos)).set_duration(video_clip.duration)
        result_clip = mp.CompositeVideoClip([video_clip, watermark])
        logger.info(f"水印添加成功: {watermark_path}, 位置: {position}, 不透明度: {opacity}")
        return result_clip
        except Exception:
        logger.error("操作失败")
        return None
def save_video()
        self,:
        video_clip: mp.VideoFileClip,
        output_path: str,
        codec: str = "libx264",
        bitrate: str = "5000k",
        audio_codec: str = "aac",
        audio_bitrate: str = "192k",
        preset: str = "medium",
        threads: int = 4,
        use_temp: bool = True) -> bool:
        try:
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
        if use_temp:
            temp_output = os.path.join(self.temp_dir, f"temp_{os.path.basename(output_path)}")
            video_clip.write_videofile()
                temp_output,
                codec=codec,
                bitrate=bitrate,
                audio_codec=audio_codec,
                audio_bitrate=audio_bitrate,
                preset=preset,
                threads=threads)
            shutil.move(temp_output, output_path)
            logger.info(f"视频保存成功(使用临时文件): {output_path}")
        else:
            video_clip.write_videofile()
                output_path,
                codec=codec,
                bitrate=bitrate,
                audio_codec=audio_codec,
                audio_bitrate=audio_bitrate,
                preset=preset,
                threads=threads)
            logger.info(f"视频保存成功: {output_path}")
        return True
        except Exception:
        logger.error("操作失败")
        return False
def merge_videos()
        self,:
        video_clips: List[mp.VideoFileClip],
        transitions: Optional[List[str]] = None,
        transition_duration: float = 1.0) -> Optional[mp.VideoFileClip]:
        if not video_clips:
        logger.error("视频列表为空")
        return None
        if len(video_clips) == 1:
        return video_clips[0]
        try:
        if transitions is None:
            transitions = ["cut"] * (len(video_clips) - 1)
        if len(transitions) != len(video_clips) - 1:
            logger.warning(f"转场效果数量 ({len(transitions)}) 与视频数量 ({len(video_clips)}) 不匹配，使用硬切")
            transitions = ["cut"] * (len(video_clips) - 1)
        result_clip = video_clips[0]
        for i in range(1, len(video_clips)):
            transition = transitions[i - 1]
            clip = video_clips[i]
            if transition == "cut" or transition not in self.supported_transitions:
                result_clip = mp.concatenate_videoclips([result_clip, clip])
            else:
                result_clip = self.apply_transition(result_clip, clip, transition, transition_duration)
                if result_clip is None:
                    logger.error(f"转场效果应用失败: {transition}，使用硬切")
                    result_clip = mp.concatenate_videoclips([result_clip, clip])
        logger.info(f"视频合并成功，共 {len(video_clips)} 个视频，时长: {result_clip.duration}秒")
        return result_clip
        except Exception:
        logger.error("操作失败")
        return None
def resize_video()
        self,:
        video_clip: mp.VideoFileClip,
        width: Optional[int] = None,
        height: Optional[int] = None,
        keep_aspect_ratio: bool = True) -> Optional[mp.VideoFileClip]:
        try:
        orig_width, orig_height = video_clip.size
        if width is None and height is None:
            return video_clip
        if keep_aspect_ratio:
            if width is None:
                width = int(height * orig_width / orig_height)
            elif height is None:
                height = int(width * orig_height / orig_width)
            else:
                target_ratio = width / height
                orig_ratio = orig_width / orig_height
                if target_ratio > orig_ratio:
                    width = int(height * orig_ratio)
                else:
                    height = int(width / orig_ratio)
        result_clip = video_clip.resize(newsize=(width, height))
        logger.info(f"视频大小调整成功: {orig_width}x{orig_height} -> {width}x{height}")
        return result_clip
        except Exception:
        logger.error("操作失败")
        return None
def crop_video():
        self, video_clip: mp.VideoFileClip, x1: int, y1: int, x2: int, y2: int
        ) -> Optional[mp.VideoFileClip]:
        try:
        orig_width, orig_height = video_clip.size
        x1 = max(0, min(x1, orig_width - 1))
        y1 = max(0, min(y1, orig_height - 1))
        x2 = max(x1 + 1, min(x2, orig_width))
        y2 = max(y1 + 1, min(y2, orig_height))
        width = x2 - x1
        height = y2 - y1
        result_clip = video_clip.crop(x1=x1, y1=y1, width=width, height=height)
        logger.info(f"视频裁剪成功: ({x1}, {y1}) - ({x2}, {y2}), 大小: {width}x{height}")
        return result_clip
        except Exception:
        logger.error("操作失败")
        return None
    def _apply_brightness(self, video_clip: mp.VideoFileClip, factor: float = 1.5) -> mp.VideoFileClip:
        return video_clip.fx(vfx.colorx, factor)
    def _apply_contrast(self, video_clip: mp.VideoFileClip, factor: float = 1.5) -> mp.VideoFileClip:
        return video_clip.fx(vfx.colorx, factor)
    def _apply_saturation(self, video_clip: mp.VideoFileClip, factor: float = 1.5) -> mp.VideoFileClip:
    def _adjust_saturation(image):
        hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
        hsv[:, :, 1] = np.clip(hsv[:, :, 1] * factor, 0, 255).astype(np.uint8)
        return cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)
        return video_clip.fl_image(_adjust_saturation)
    def _apply_speed(self, video_clip: mp.VideoFileClip, factor: float = 1.5) -> mp.VideoFileClip:
        return video_clip.fx(vfx.speedx, factor)
    def _apply_reverse(self, video_clip: mp.VideoFileClip, **kwargs) -> mp.VideoFileClip:
        _ = kwargs  # 忽略未使用的参数
        return video_clip.fx(vfx.time_mirror)
    def _apply_mirror(self, video_clip: mp.VideoFileClip, axis: str = "x", **kwargs) -> mp.VideoFileClip:
        _ = kwargs  # 忽略未使用的参数
        if axis == "x":
        return video_clip.fx(vfx.mirror_x)
        elif axis == "y":
        return video_clip.fx(vfx.mirror_y)
        else:
        return video_clip
    def _apply_black_white(self, video_clip: mp.VideoFileClip, **kwargs) -> mp.VideoFileClip:
        _ = kwargs  # 忽略未使用的参数
        return video_clip.fx(vfx.blackwhite)
    def _apply_blur(self, video_clip: mp.VideoFileClip, radius: float = 5.0, **kwargs) -> mp.VideoFileClip:
        _ = kwargs  # 忽略未使用的参数
    def _blur(image):
        return cv2.GaussianBlur(image, (0, 0), radius)
        return video_clip.fl_image(_blur)
    def _apply_sharpen(self, video_clip: mp.VideoFileClip, **kwargs) -> mp.VideoFileClip:
        _ = kwargs  # 忽略未使用的参数
    def _sharpen(image):
        kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]])
        return cv2.filter2D(image, -1, kernel)
        return video_clip.fl_image(_sharpen)
    def _apply_noise(self, video_clip: mp.VideoFileClip, amount: float = 0.1, **kwargs) -> mp.VideoFileClip:
        _ = kwargs  # 忽略未使用的参数
    def _add_noise(image):
        noise = np.random.normal(0, amount * 255, image.shape).astype(np.uint8)
        noisy_image = np.clip(image + noise, 0, 255).astype(np.uint8)
        return noisy_image
        return video_clip.fl_image(_add_noise)
    def _apply_vignette(self, video_clip: mp.VideoFileClip, **kwargs) -> mp.VideoFileClip:
        _ = kwargs  # 忽略未使用的参数
    def _add_vignette(image):
        height, width = image.shape[:2]
        x = np.linspace(-1, 1, width)
        y = np.linspace(-1, 1, height)
        x_grid, y_grid = np.meshgrid(x, y)
        radius = np.sqrt(x_grid**2 + y_grid**2)
        vignette = np.clip(1 - radius, 0, 1)
        vignette = vignette.reshape(height, width, 1)
        result = image * vignette
        return result.astype(np.uint8)
        return video_clip.fl_image(_add_vignette)
    def _apply_rotate(self, video_clip: mp.VideoFileClip, angle: float = 90, **kwargs) -> mp.VideoFileClip:
        _ = kwargs  # 忽略未使用的参数
        return video_clip.rotate(angle)
    def _apply_sepia(self, video_clip: mp.VideoFileClip, **kwargs) -> mp.VideoFileClip:
        _ = kwargs  # 忽略未使用的参数
    def _sepia_effect(image):
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        normalized_gray = np.array(gray, np.float32) / 255
        sepia = np.ones(image.shape)
        sepia[:, :, 0] *= 153  # B
        sepia[:, :, 1] *= 134  # G
        sepia[:, :, 2] *= 100  # R
        sepia[:, :, 0] *= normalized_gray  # B
        sepia[:, :, 1] *= normalized_gray  # G
        sepia[:, :, 2] *= normalized_gray  # R
        return np.array(sepia, dtype=np.uint8)
        return video_clip.fl_image(_sepia_effect)
def _apply_edge_detect():
        self, video_clip: mp.VideoFileClip, threshold1: float = 100, threshold2: float = 200, **kwargs
        ) -> mp.VideoFileClip:
        _ = kwargs  # 忽略未使用的参数
    def _edge_detect(image):
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        edges = cv2.Canny(gray, threshold1, threshold2)
        return cv2.cvtColor(edges, cv2.COLOR_GRAY2RGB)
        return video_clip.fl_image(_edge_detect)
    def _apply_pixelate(self, video_clip: mp.VideoFileClip, pixel_size: int = 20, **kwargs) -> mp.VideoFileClip:
        _ = kwargs  # 忽略未使用的参数
    def _pixelate(image):
        height, width = image.shape[:2]
        temp = cv2.resize(image, (width // pixel_size, height // pixel_size), interpolation=cv2.INTER_LINEAR)
        return cv2.resize(temp, (width, height), interpolation=cv2.INTER_NEAREST)
        return video_clip.fl_image(_pixelate)
    def _apply_time_symmetry(self, video_clip: mp.VideoFileClip, **kwargs) -> mp.VideoFileClip:
        _ = kwargs  # 忽略未使用的参数
        reversed_clip = video_clip.fx(vfx.time_mirror)
        return mp.concatenate_videoclips([video_clip, reversed_clip])
def _apply_cut_transition():
        self, clip1: mp.VideoFileClip, clip2: mp.VideoFileClip, duration: float = 1.0, **kwargs
        ) -> mp.VideoFileClip:
        _ = kwargs  # 忽略未使用的参数
        return mp.concatenate_videoclips([clip1, clip2])
def _apply_fade_transition():
        self, clip1: mp.VideoFileClip, clip2: mp.VideoFileClip, duration: float = 1.0, **kwargs
        ) -> mp.VideoFileClip:
        _ = kwargs  # 忽略未使用的参数
        duration = min(duration, clip1.duration, clip2.duration)
        clip1_fadeout = clip1.copy().set_duration(clip1.duration)
        clip1_fadeout = clip1_fadeout.crossfadeout(duration)
        clip2_fadein = clip2.copy().set_duration(clip2.duration)
        clip2_fadein = clip2_fadein.crossfadein(duration)
        clip1_part = clip1.subclip(0, clip1.duration - duration)
        clip2_part = clip2.subclip(duration, clip2.duration)
        overlap = mp.CompositeVideoClip()
        []
            clip1.subclip(clip1.duration - duration, clip1.duration).crossfadeout(duration),
            clip2.subclip(0, duration).crossfadein(duration)]
        )
        return mp.concatenate_videoclips([clip1_part, overlap, clip2_part])
def _apply_wipe_transition():
        self, clip1: mp.VideoFileClip, clip2: mp.VideoFileClip, duration: float = 1.0, direction: str = "left", **kwargs
        ) -> mp.VideoFileClip:
        _ = kwargs  # 忽略未使用的参数
        duration = min(duration, clip1.duration, clip2.duration)
    def make_mask(t):
        progress = t / duration
        if direction == "left":
            return np.ones((clip1.h, clip1.w)) * (progress >= (1 - np.linspace(0, 1, clip1.w)))
        elif direction == "right":
            return np.ones((clip1.h, clip1.w)) * (progress >= np.linspace(0, 1, clip1.w))
        elif direction == "up":
            return np.ones((clip1.h, clip1.w)) * (progress >= (1 - np.linspace(0, 1, clip1.h))[:, np.newaxis])
        elif direction == "down":
            return np.ones((clip1.h, clip1.w)) * (progress >= np.linspace(0, 1, clip1.h)[:, np.newaxis])
        else:
            return np.ones((clip1.h, clip1.w)) * (progress >= 0.5)
        mask_clip = mp.VideoClip(make_mask, duration=duration)
        transition_clip = mp.CompositeVideoClip()
        [clip1.subclip(clip1.duration - duration, clip1.duration), clip2.subclip(0, duration).set_mask(mask_clip)]
        )
        return mp.concatenate_videoclips()
        [clip1.subclip(0, clip1.duration - duration), transition_clip, clip2.subclip(duration, clip2.duration)]
        )
def _apply_dissolve_transition():
        self, clip1: mp.VideoFileClip, clip2: mp.VideoFileClip, duration: float = 1.0, **kwargs
        ) -> mp.VideoFileClip:
        _ = kwargs  # 忽略未使用的参数
        duration = min(duration, clip1.duration, clip2.duration)
        transition_clip = mp.CompositeVideoClip()
        []
            clip1.subclip(clip1.duration - duration, clip1.duration).crossfadeout(duration),
            clip2.subclip(0, duration).crossfadein(duration)]
        )
        return mp.concatenate_videoclips()
        [clip1.subclip(0, clip1.duration - duration), transition_clip, clip2.subclip(duration, clip2.duration)]
        )
def _apply_zoom_transition():
        self, clip1: mp.VideoFileClip, clip2: mp.VideoFileClip, duration: float = 1.0, zoom_in: bool = True, **kwargs
        ) -> mp.VideoFileClip:
        _ = kwargs  # 忽略未使用的参数
        duration = min(duration, clip1.duration, clip2.duration)
        if zoom_in:
        clip1_zoom = clip1.subclip(clip1.duration - duration, clip1.duration)
        clip1_zoom = clip1_zoom.fx(vfx.resize, lambda t: 1 + t / duration)
        clip1_zoom = clip1_zoom.set_position("center")
        transition_clip = mp.CompositeVideoClip()
            [clip1_zoom, clip2.subclip(0, duration).set_opacity(lambda t: t / duration)], size=clip1.size
        )
        else:
        clip2_zoom = clip2.subclip(0, duration)
        clip2_zoom = clip2_zoom.fx(vfx.resize, lambda t: 2 - t / duration)
        clip2_zoom = clip2_zoom.set_position("center")
        transition_clip = mp.CompositeVideoClip()
            []
                clip1.subclip(clip1.duration - duration, clip1.duration).set_opacity(lambda t: 1 - t / duration),
                clip2_zoom],
            size=clip1.size)
        return mp.concatenate_videoclips()
        [clip1.subclip(0, clip1.duration - duration), transition_clip, clip2.subclip(duration, clip2.duration)]
        )
def _apply_slide_transition():
        self, clip1: mp.VideoFileClip, clip2: mp.VideoFileClip, duration: float = 1.0, direction: str = "left", **kwargs
        ) -> mp.VideoFileClip:
        _ = kwargs  # 忽略未使用的参数
        duration = min(duration, clip1.duration, clip2.duration)
        if direction == "left":
        def pos_clip1(t):
            return ("center", "center")
        def pos_clip2(t):
            return (clip1.w * (1 - t / duration), "center")
        elif direction == "right":
        def pos_clip1(t):
            return ("center", "center")
        def pos_clip2(t):
            return (-clip1.w * (1 - t / duration), "center")
        elif direction == "up":
        def pos_clip1(t):
            return ("center", "center")
        def pos_clip2(t):
            return ("center", clip1.h * (1 - t / duration))
        elif direction == "down":
        def pos_clip1(t):
            return ("center", "center")
        def pos_clip2(t):
            return ("center", -clip1.h * (1 - t / duration))
        else:
        def pos_clip1(t):
            return ("center", "center")
        def pos_clip2(t):
            return (clip1.w * (1 - t / duration), "center")
        clip1_part = clip1.subclip(clip1.duration - duration, clip1.duration).set_position(pos_clip1)
        clip2_part = clip2.subclip(0, duration).set_position(pos_clip2)
        transition_clip = mp.CompositeVideoClip([clip1_part, clip2_part], size=clip1.size)
        return mp.concatenate_videoclips()
        [clip1.subclip(0, clip1.duration - duration), transition_clip, clip2.subclip(duration, clip2.duration)]
        )
    def register_temp_file(self, file_path: str) -> None:
        if os.path.exists(file_path):
        self.temp_files.append(file_path)
        logger.debug(f"注册临时文件: {file_path}")
    def cleanup_temp_files(self) -> None:
        count = 0
        for file_path in self.temp_files:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                count += 1
                logger.debug(f"已删除临时文件: {file_path}")
        except Exception:
            logger.warning("操作失败")
        try:
        if os.path.exists(self.temp_dir) and not os.listdir(self.temp_dir):
            os.rmdir(self.temp_dir)
            logger.info(f"已删除空的临时目录: {self.temp_dir}")
        except Exception:
        logger.warning("操作失败")
def apply_effect_parallel():
        self, video_path: str, output_path: str, effect: str, max_workers: Optional[int] = None, **params
        ) -> bool:
        try:
        if effect not in self.supported_effects:
            logger.error(f"不支持的特效: {effect}")
            return False
        processor = ParallelVideoProcessor(temp_dir=self.temp_dir, max_workers=max_workers)
        result = processor.process_video_parallel()
            video_path=video_path,
            output_path=output_path,
            process_func=processor.apply_effect_to_chunks,
            effect_name=effect,
            **params)
        return result
        except Exception:
        logger.error("操作失败")
        logger.error(traceback.format_exc())
        return False
def process_large_video():
        self, video_path: str, output_path: str, operations: List[Dict[str, Any]], max_workers: Optional[int] = None
        ) -> bool:
        try:
        if not operations:
            logger.error("操作列表为空")
            return False
        temp_input = video_path
        temp_output = None
        for i, operation in enumerate(operations):
            op_type = operation.get("type")
            if i < len(operations) - 1:
                temp_fd, temp_output = tempfile.mkstemp(prefix=f"temp_op_{i}_", suffix=".mp4", dir=self.temp_dir)
                os.close(temp_fd)  # 关闭文件描述符，只保留路径
                self.temp_files.append(temp_output)
            else:
                temp_output = output_path
            if op_type == "cut":
                start_time = operation.get("start_time", 0)
                end_time = operation.get("end_time")
                video = self.load_video(temp_input)
                if video is None:
                    return False
                if end_time is not None:
                    cut_video = self.cut_video(video, start_time, end_time)
                else:
                    cut_video = self.cut_video(video, start_time, video.duration)
                if cut_video is None:
                    video.close()
                    return False
                result = self.save_video(cut_video, temp_output)
                video.close()
                cut_video.close()
                if not result:
                    return False
            elif op_type == "effect":
                effect_name = operation.get("name")
                if not effect_name:
                    logger.error("未指定特效名称f")
                    return False
                effect_params = {k: v for k, v in operation.items() if k not in ["type", "name"]}
                result = self.apply_effect_parallel()
                    temp_input, temp_output, effect_name, max_workers, **effect_params
                ):
                if not result:
                    return False
            elif op_type == "speed":
                factor = operation.get("factor", 1.0)
                video = self.load_video(temp_input)
                if video is None:
                    return False
                speed_video = video.fx(vfx.speedx, factor)
                result = self.save_video(speed_video, temp_output)
                video.close()
                speed_video.close()
                if not result:
                    return False
            elif op_type == "extract_highlights":
                duration = operation.get("duration", 30.0)
                threshold = operation.get("threshold", 30.0)
                highlight_clip = self.extract_highlights(temp_input, duration, threshold, min_scene_length=1.0)
                if highlight_clip is None:
                    return False
                result = self.save_video(highlight_clip, temp_output)
                highlight_clip.close()
                if not result:
                    return False
            else:
                logger.error(f"不支持的操作类型: {op_type}")
                return False
            if i < len(operations) - 1:
                temp_input = temp_output
        logger.info(f"大型视频处理完成: {output_path}")
        return True
        except Exception:
        logger.error("操作失败")
        logger.error(traceback.format_exc())
        return False
        finally:
        len(self.temp_files)
        self.temp_files = []
        logger.info(f"清理完成，共删除 {file_count} 个临时文件")