# backend.agent.smart_editor.video_editor

import os
import logging
import tempfile
import subprocess
import numpy as np
from typing import List, Dict, Any, Tuple, Optional, Union, TYPE_CHECKING

import cv2

# 处理 moviepy 导入 - 添加类型忽略注释
import moviepy.editor as mp  # type: ignore
from moviepy.video.io.ffmpeg_tools import ffmpeg_extract_subclip  # type: ignore
from moviepy.video.fx import all as vfx  # type: ignore
from moviepy.audio.fx import all as afx  # type: ignore

# 导入自定义模块
# 注意：这里使用延迟导入，避免循环导入问题
# from backend.agent.smart_editor.video_summarizer import VideoSummarizer
# from backend.agent.smart_editor.parallel_processor import ParallelVideoProcessor

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VideoEditor:
    """
    视频编辑器类，提供视频剪辑、特效添加、转场效果等功能。
    """
    
    def __init__(self, temp_dir: Optional[str] = None, auto_cleanup: bool = True):
        """
        初始化视频编辑器。
        
        Args:
            temp_dir: 临时文件目录，如果为 None 则使用系统临时目录
            auto_cleanup: 是否在退出时自动清理临时文件
        """
        if temp_dir is None:
            self.temp_dir = os.path.join(tempfile.gettempdir(), 'intellicut_temp')
        else:
            self.temp_dir = temp_dir
        
        # 确保临时目录存在
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # 支持的视频格式
        self.supported_formats = ['.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv']
        
        # 支持的特效
        self.supported_effects = {
            'brightness': self._apply_brightness,
            'contrast': self._apply_contrast,
            'saturation': self._apply_saturation,
            'speed': self._apply_speed,
            'reverse': self._apply_reverse,
            'mirror': self._apply_mirror,
            'black_white': self._apply_black_white,
            'blur': self._apply_blur,
            'sharpen': self._apply_sharpen,
            'noise': self._apply_noise,
            'vignette': self._apply_vignette,
            'rotate': self._apply_rotate,
            'sepia': self._apply_sepia,           # 新增：复古褐色效果
            'edge_detect': self._apply_edge_detect, # 新增：边缘检测效果
            'pixelate': self._apply_pixelate,     # 新增：像素化效果
            'time_symmetry': self._apply_time_symmetry  # 新增：时间对称效果
        }
        
        # 支持的转场效果
        self.supported_transitions = {
            'cut': self._apply_cut_transition,
            'fade': self._apply_fade_transition,
            'wipe': self._apply_wipe_transition,
            'dissolve': self._apply_dissolve_transition,
            'zoom': self._apply_zoom_transition,
            'slide': self._apply_slide_transition
        }
        
        # 临时文件列表，用于跟踪和清理
        self.temp_files = []
        
        # 设置自动清理
        self.auto_cleanup = auto_cleanup
        if auto_cleanup:
            import atexit
            atexit.register(self.cleanup_temp_files)
        
        logger.info(f"视频编辑器初始化完成，临时目录: {self.temp_dir}")
    
    def load_video(self, video_path: str) -> Optional[mp.VideoFileClip]:
        """
        加载视频文件。
        
        Args:
            video_path: 视频文件路径
            
        Returns:
            视频剪辑对象，如果加载失败则返回 None
        """
        if not os.path.exists(video_path):
            logger.error(f"视频文件不存在: {video_path}")
            return None
        
        # 检查文件格式
        _, ext = os.path.splitext(video_path)
        if ext.lower() not in self.supported_formats:
            logger.error(f"不支持的视频格式: {ext}")
            return None
        
        try:
            # 加载视频
            video_clip = mp.VideoFileClip(video_path)
            logger.info(f"视频加载成功: {video_path}, 时长: {video_clip.duration}秒, 分辨率: {video_clip.size}")
            return video_clip
        except Exception as e:
            logger.error(f"视频加载失败: {e}")
            return None
    
    def cut_video(self, video_clip: mp.VideoFileClip, start_time: float, end_time: float) -> Optional[mp.VideoFileClip]:
        """
        剪切视频片段。
        
        Args:
            video_clip: 视频剪辑对象
            start_time: 开始时间（秒）
            end_time: 结束时间（秒）
            
        Returns:
            剪切后的视频剪辑对象，如果剪切失败则返回 None
        """
        try:
            # 确保时间范围有效
            if start_time < 0:
                start_time = 0
            if end_time > video_clip.duration:
                end_time = video_clip.duration
            if start_time >= end_time:
                logger.error(f"无效的时间范围: {start_time} - {end_time}")
                return None
            
            # 剪切视频
            cut_clip = video_clip.subclip(start_time, end_time)
            logger.info(f"视频剪切成功: {start_time}秒 - {end_time}秒, 时长: {cut_clip.duration}秒")
            return cut_clip
        except Exception as e:
            logger.error(f"视频剪切失败: {e}")
            return None
    
    def extract_scenes(self, video_path: str, threshold: float = 30.0, min_scene_length: float = 1.0,
                       method: str = 'threshold') -> List[Tuple[float, float]]:
        """
        提取视频场景。
        
        Args:
            video_path: 视频文件路径
            threshold: 场景变化阈值，值越大，检测到的场景越少
            min_scene_length: 最小场景长度（秒）
            method: 场景检测方法，可选 'threshold'（基于阈值）, 'content'（基于内容）, 'edge'（基于边缘）
            
        Returns:
            场景列表，每个场景为 (开始时间, 结束时间) 元组
        """
        try:
            # 导入场景检测模块
            from backend.agent.smart_editor.scene_detection import (
                extract_scenes_threshold, 
                extract_scenes_content, 
                extract_scenes_edge
            )
            
            # 打开视频
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                logger.error(f"无法打开视频: {video_path}")
                return []
            
            # 获取视频信息
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = frame_count / fps
            
            logger.info(f"视频信息: FPS={fps}, 总帧数={frame_count}, 时长={duration}秒")
            
            # 根据方法选择场景检测算法
            if method == 'content':
                logger.info("使用基于内容的场景检测方法")
                scenes = extract_scenes_content(cap, fps, threshold, min_scene_length)
            elif method == 'edge':
                logger.info("使用基于边缘的场景检测方法")
                scenes = extract_scenes_edge(cap, fps, threshold, min_scene_length)
            else:
                logger.info("使用基于阈值的场景检测方法")
                scenes = extract_scenes_threshold(cap, fps, threshold, min_scene_length)
            
            # 释放资源
            cap.release()
            
            logger.info(f"场景检测完成，共检测到 {len(scenes)} 个场景")
            return scenes
        except Exception as e:
            logger.error(f"场景检测失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return []
    
    def extract_highlights(self, video_path: str, duration: float = 30.0, 
                          scene_threshold: float = 30.0, min_scene_length: float = 1.0) -> Optional[mp.VideoFileClip]:
        """
        提取视频精彩片段。
        
        Args:
            video_path: 视频文件路径
            duration: 目标视频时长（秒）
            scene_threshold: 场景变化阈值
            min_scene_length: 最小场景长度（秒）
            
        Returns:
            精彩片段视频剪辑对象，如果提取失败则返回 None
        """
        try:
            # 加载视频
            video_clip = self.load_video(video_path)
            if video_clip is None:
                return None
            
            # 如果视频时长小于目标时长，直接返回原视频
            if video_clip.duration <= duration:
                logger.info(f"视频时长 ({video_clip.duration}秒) 小于目标时长 ({duration}秒)，返回原视频")
                return video_clip
            
            # 提取场景
            scenes = self.extract_scenes(video_path, scene_threshold, min_scene_length)
            if not scenes:
                logger.warning(f"未检测到场景，使用简单剪切方法")
                return self.cut_video(video_clip, 0, min(duration, video_clip.duration))
            
            # 计算场景得分
            scene_scores = []
            for i, (start_time, end_time) in enumerate(scenes):
                # 确保end_time不是None
                if end_time is None:
                    continue
                scene_length = end_time - start_time
                
                # 简单评分策略：场景长度、场景位置、场景变化程度
                length_score = min(scene_length / 5.0, 1.0)  # 长度得分，最高1分
                position_score = 1.0 - abs(2 * (start_time / video_clip.duration) - 1)  # 位置得分，中间位置得分高
                
                # 总分
                total_score = length_score * 0.5 + position_score * 0.5
                scene_scores.append((i, total_score))
            
            # 按得分排序
            scene_scores.sort(key=lambda x: x[1], reverse=True)
            
            # 选择得分最高的场景，直到达到目标时长
            selected_scenes = []
            current_duration = 0
            
            for i, score in scene_scores:
                start_time, end_time = scenes[i]
                # 确保end_time不是None
                if end_time is None:
                    continue
                scene_length = end_time - start_time
                
                if current_duration + scene_length <= duration:
                    selected_scenes.append((start_time, end_time))
                    current_duration += scene_length
                
                if current_duration >= duration:
                    break
            
            # 按时间顺序排序
            selected_scenes.sort()
            
            # 提取选中的场景
            clips = []
            for start_time, end_time in selected_scenes:
                clip = video_clip.subclip(start_time, end_time)
                clips.append(clip)
            
            # 如果没有选中任何场景，使用简单剪切方法
            if not clips:
                logger.warning(f"未选中任何场景，使用简单剪切方法")
                return self.cut_video(video_clip, 0, min(duration, video_clip.duration))
            
            # 合并场景
            final_clip = mp.concatenate_videoclips(clips)
            
            # 如果合并后的视频时长仍小于目标时长，添加原视频的开头部分
            if final_clip.duration < duration:
                remaining_duration = duration - final_clip.duration
                start_clip = video_clip.subclip(0, min(remaining_duration, video_clip.duration))
                final_clip = mp.concatenate_videoclips([start_clip, final_clip])
            
            logger.info(f"精彩片段提取成功，时长: {final_clip.duration}秒")
            return final_clip
        except Exception as e:
            logger.error(f"精彩片段提取失败: {e}")
            return None
    
    def apply_effect(self, video_clip: mp.VideoFileClip, effect: str, **params) -> Optional[mp.VideoFileClip]:
        """
        应用视频特效。
        
        Args:
            video_clip: 视频剪辑对象
            effect: 特效名称
            **params: 特效参数
            
        Returns:
            应用特效后的视频剪辑对象，如果应用失败则返回 None
        """
        if effect not in self.supported_effects:
            logger.error(f"不支持的特效: {effect}")
            return None
        
        try:
            # 应用特效
            effect_func = self.supported_effects[effect]
            result_clip = effect_func(video_clip, **params)
            
            logger.info(f"特效应用成功: {effect}")
            return result_clip
        except Exception as e:
            logger.error(f"特效应用失败: {effect}, 错误: {e}")
            return None
    
    def apply_transition(self, clip1: mp.VideoFileClip, clip2: mp.VideoFileClip, 
                        transition: str, duration: float = 1.0, **params) -> Optional[mp.VideoFileClip]:
        """
        应用转场效果。
        
        Args:
            clip1: 第一个视频剪辑对象
            clip2: 第二个视频剪辑对象
            transition: 转场效果名称
            duration: 转场时长（秒）
            **params: 转场参数
            
        Returns:
            应用转场效果后的视频剪辑对象，如果应用失败则返回 None
        """
        if transition not in self.supported_transitions:
            logger.error(f"不支持的转场效果: {transition}")
            return None
        
        try:
            # 应用转场效果
            transition_func = self.supported_transitions[transition]
            result_clip = transition_func(clip1, clip2, duration, **params)
            
            logger.info(f"转场效果应用成功: {transition}, 时长: {duration}秒")
            return result_clip
        except Exception as e:
            logger.error(f"转场效果应用失败: {transition}, 错误: {e}")
            return None
    
    def add_audio(self, video_clip: mp.VideoFileClip, audio_path: str, 
                 volume: float = 1.0, start_time: float = 0, loop: bool = False) -> Optional[mp.VideoFileClip]:
        """
        添加音频。
        
        Args:
            video_clip: 视频剪辑对象
            audio_path: 音频文件路径
            volume: 音量（0.0-1.0）
            start_time: 开始时间（秒）
            loop: 是否循环播放
            
        Returns:
            添加音频后的视频剪辑对象，如果添加失败则返回 None
        """
        if not os.path.exists(audio_path):
            logger.error(f"音频文件不存在: {audio_path}")
            return None
        
        try:
            # 加载音频
            audio_clip = mp.AudioFileClip(audio_path)
            
            # 调整音量
            audio_clip = audio_clip.volumex(volume)
            
            # 如果需要循环播放
            if loop and audio_clip.duration < video_clip.duration:
                # 计算需要循环的次数
                loop_count = int(video_clip.duration / audio_clip.duration) + 1
                audio_clips = [audio_clip] * loop_count
                audio_clip = mp.concatenate_audioclips(audio_clips)
            
            # 裁剪音频长度与视频一致
            audio_clip = audio_clip.subclip(0, min(audio_clip.duration, video_clip.duration - start_time))
            
            # 创建静音片段（如果有开始时间）
            if start_time > 0:
                silence = mp.AudioClip(lambda t: 0, duration=start_time)
                audio_clip = mp.concatenate_audioclips([silence, audio_clip])
            
            # 如果音频长度仍小于视频长度，添加静音片段
            if audio_clip.duration < video_clip.duration:
                silence = mp.AudioClip(lambda t: 0, duration=video_clip.duration - audio_clip.duration)
                audio_clip = mp.concatenate_audioclips([audio_clip, silence])
            
            # 设置音频
            result_clip = video_clip.set_audio(audio_clip)
            
            logger.info(f"音频添加成功: {audio_path}, 音量: {volume}")
            return result_clip
        except Exception as e:
            logger.error(f"音频添加失败: {e}")
            return None
    
    def add_text(self, video_clip: mp.VideoFileClip, text: str, font: str = 'Arial', 
                fontsize: int = 30, color: str = 'white', position: Tuple[str, str] = ('center', 'bottom'), 
                start_time: float = 0, end_time: Optional[float] = None) -> Optional[mp.VideoFileClip]:
        """
        添加文本。
        
        Args:
            video_clip: 视频剪辑对象
            text: 文本内容
            font: 字体
            fontsize: 字体大小
            color: 字体颜色
            position: 位置，格式为 (水平位置, 垂直位置)，水平位置可选 'left', 'center', 'right'，垂直位置可选 'top', 'center', 'bottom'
            start_time: 开始时间（秒）
            end_time: 结束时间（秒），如果为 None 则持续到视频结束
            
        Returns:
            添加文本后的视频剪辑对象，如果添加失败则返回 None
        """
        try:
            # 设置结束时间
            if end_time is None:
                end_time = video_clip.duration
            
            # 创建文本剪辑
            text_clip = mp.TextClip(text, font=font, fontsize=fontsize, color=color)
            
            # 设置文本位置
            h_pos, v_pos = position
            
            if h_pos == 'left':
                x_pos = 20
            elif h_pos == 'right':
                x_pos = video_clip.size[0] - text_clip.size[0] - 20
            else:  # center
                x_pos = (video_clip.size[0] - text_clip.size[0]) // 2
            
            if v_pos == 'top':
                y_pos = 20
            elif v_pos == 'bottom':
                y_pos = video_clip.size[1] - text_clip.size[1] - 20
            else:  # center
                y_pos = (video_clip.size[1] - text_clip.size[1]) // 2
            
            # 设置文本时长和位置
            # 如果end_time为None，则使用视频的总时长
            actual_end_time = end_time if end_time is not None else video_clip.duration
            text_clip = text_clip.set_position((x_pos, y_pos)).set_duration(actual_end_time - start_time).set_start(start_time)
            
            # 合成视频
            result_clip = mp.CompositeVideoClip([video_clip, text_clip])
            
            logger.info(f"文本添加成功: {text}, 位置: {position}, 时间: {start_time}秒 - {end_time}秒")
            return result_clip
        except Exception as e:
            logger.error(f"文本添加失败: {e}")
            return None
    
    def add_watermark(self, video_clip: mp.VideoFileClip, watermark_path: str, 
                     position: Tuple[str, str] = ('right', 'bottom'), opacity: float = 0.5) -> Optional[mp.VideoFileClip]:
        """
        添加水印。
        
        Args:
            video_clip: 视频剪辑对象
            watermark_path: 水印图片路径
            position: 位置，格式为 (水平位置, 垂直位置)，水平位置可选 'left', 'center', 'right'，垂直位置可选 'top', 'center', 'bottom'
            opacity: 不透明度（0.0-1.0）
            
        Returns:
            添加水印后的视频剪辑对象，如果添加失败则返回 None
        """
        if not os.path.exists(watermark_path):
            logger.error(f"水印图片不存在: {watermark_path}")
            return None
        
        try:
            # 加载水印图片
            watermark = mp.ImageClip(watermark_path).set_opacity(opacity)
            
            # 设置水印位置
            h_pos, v_pos = position
            
            if h_pos == 'left':
                x_pos = 20
            elif h_pos == 'right':
                x_pos = video_clip.size[0] - watermark.size[0] - 20
            else:  # center
                x_pos = (video_clip.size[0] - watermark.size[0]) // 2
            
            if v_pos == 'top':
                y_pos = 20
            elif v_pos == 'bottom':
                y_pos = video_clip.size[1] - watermark.size[1] - 20
            else:  # center
                y_pos = (video_clip.size[1] - watermark.size[1]) // 2
            
            # 设置水印时长和位置
            watermark = watermark.set_position((x_pos, y_pos)).set_duration(video_clip.duration)
            
            # 合成视频
            result_clip = mp.CompositeVideoClip([video_clip, watermark])
            
            logger.info(f"水印添加成功: {watermark_path}, 位置: {position}, 不透明度: {opacity}")
            return result_clip
        except Exception as e:
            logger.error(f"水印添加失败: {e}")
            return None
    
    def save_video(self, video_clip: mp.VideoFileClip, output_path: str, 
                  codec: str = 'libx264', bitrate: str = '5000k', 
                  audio_codec: str = 'aac', audio_bitrate: str = '192k', 
                  preset: str = 'medium', threads: int = 4, 
                  use_temp: bool = True) -> bool:
        """
        保存视频。
        
        Args:
            video_clip: 视频剪辑对象
            output_path: 输出文件路径
            codec: 视频编码器
            bitrate: 视频比特率
            audio_codec: 音频编码器
            audio_bitrate: 音频比特率
            preset: 编码预设，可选 'ultrafast', 'superfast', 'veryfast', 'faster', 'fast', 'medium', 'slow', 'slower', 'veryslow'
            threads: 编码线程数
            use_temp: 是否使用临时文件
            
        Returns:
            是否成功保存
        """
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
            
            # 使用临时文件保存，然后移动到目标位置
            if use_temp:
                temp_output = os.path.join(self.temp_dir, f"temp_{os.path.basename(output_path)}")
                
                # 保存视频到临时文件
                video_clip.write_videofile(
                    temp_output,
                    codec=codec,
                    bitrate=bitrate,
                    audio_codec=audio_codec,
                    audio_bitrate=audio_bitrate,
                    preset=preset,
                    threads=threads
                )
                
                # 移动到目标位置
                import shutil
                shutil.move(temp_output, output_path)
                logger.info(f"视频保存成功(使用临时文件): {output_path}")
            else:
                # 直接保存视频
                video_clip.write_videofile(
                    output_path,
                    codec=codec,
                    bitrate=bitrate,
                    audio_codec=audio_codec,
                    audio_bitrate=audio_bitrate,
                    preset=preset,
                    threads=threads
                )
                logger.info(f"视频保存成功: {output_path}")
                
            return True
        except Exception as e:
            logger.error(f"视频保存失败: {e}")
            return False
    
    def merge_videos(self, video_clips: List[mp.VideoFileClip], 
                    transitions: Optional[List[str]] = None, transition_duration: float = 1.0) -> Optional[mp.VideoFileClip]:
        """
        合并多个视频。
        
        Args:
            video_clips: 视频剪辑对象列表
            transitions: 转场效果列表，如果为 None 则使用硬切
            transition_duration: 转场时长（秒）
            
        Returns:
            合并后的视频剪辑对象，如果合并失败则返回 None
        """
        if not video_clips:
            logger.error("视频列表为空")
            return None
        
        # 如果只有一个视频，直接返回
        if len(video_clips) == 1:
            return video_clips[0]
        
        try:
            # 如果没有指定转场效果，使用硬切
            if transitions is None:
                transitions = ['cut'] * (len(video_clips) - 1)
            
            # 确保转场效果数量正确
            if len(transitions) != len(video_clips) - 1:
                logger.warning(f"转场效果数量 ({len(transitions)}) 与视频数量 ({len(video_clips)}) 不匹配，使用硬切")
                transitions = ['cut'] * (len(video_clips) - 1)
            
            # 合并视频
            result_clip = video_clips[0]
            
            for i in range(1, len(video_clips)):
                # 应用转场效果
                transition = transitions[i-1]
                clip = video_clips[i]
                
                if transition == 'cut' or transition not in self.supported_transitions:
                    # 硬切
                    result_clip = mp.concatenate_videoclips([result_clip, clip])
                else:
                    # 应用转场效果
                    result_clip = self.apply_transition(result_clip, clip, transition, transition_duration)
                    if result_clip is None:
                        logger.error(f"转场效果应用失败: {transition}，使用硬切")
                        result_clip = mp.concatenate_videoclips([result_clip, clip])
            
            logger.info(f"视频合并成功，共 {len(video_clips)} 个视频，时长: {result_clip.duration}秒")
            return result_clip
        except Exception as e:
            logger.error(f"视频合并失败: {e}")
            return None
    
    def resize_video(self, video_clip: mp.VideoFileClip, width: Optional[int] = None, height: Optional[int] = None, 
                    keep_aspect_ratio: bool = True) -> Optional[mp.VideoFileClip]:
        """
        调整视频大小。
        
        Args:
            video_clip: 视频剪辑对象
            width: 宽度，如果为 None 则根据高度和宽高比计算
            height: 高度，如果为 None 则根据宽度和宽高比计算
            keep_aspect_ratio: 是否保持宽高比
            
        Returns:
            调整大小后的视频剪辑对象，如果调整失败则返回 None
        """
        try:
            # 获取原始尺寸
            orig_width, orig_height = video_clip.size
            
            # 计算新尺寸
            if width is None and height is None:
                # 如果宽度和高度都为 None，保持原始尺寸
                return video_clip
            
            if keep_aspect_ratio:
                if width is None:
                    # 根据高度和宽高比计算宽度
                    width = int(height * orig_width / orig_height)
                elif height is None:
                    # 根据宽度和宽高比计算高度
                    height = int(width * orig_height / orig_width)
                else:
                    # 如果宽度和高度都指定了，根据宽高比调整
                    target_ratio = width / height
                    orig_ratio = orig_width / orig_height
                    
                    if target_ratio > orig_ratio:
                        # 宽度优先
                        width = int(height * orig_ratio)
                    else:
                        # 高度优先
                        height = int(width / orig_ratio)
            
            # 调整大小
            result_clip = video_clip.resize(newsize=(width, height))
            
            logger.info(f"视频大小调整成功: {orig_width}x{orig_height} -> {width}x{height}")
            return result_clip
        except Exception as e:
            logger.error(f"视频大小调整失败: {e}")
            return None
    
    def crop_video(self, video_clip: mp.VideoFileClip, x1: int, y1: int, x2: int, y2: int) -> Optional[mp.VideoFileClip]:
        """
        裁剪视频。
        
        Args:
            video_clip: 视频剪辑对象
            x1: 左上角 x 坐标
            y1: 左上角 y 坐标
            x2: 右下角 x 坐标
            y2: 右下角 y 坐标
            
        Returns:
            裁剪后的视频剪辑对象，如果裁剪失败则返回 None
        """
        try:
            # 获取原始尺寸
            orig_width, orig_height = video_clip.size
            
            # 确保坐标有效
            x1 = max(0, min(x1, orig_width - 1))
            y1 = max(0, min(y1, orig_height - 1))
            x2 = max(x1 + 1, min(x2, orig_width))
            y2 = max(y1 + 1, min(y2, orig_height))
            
            # 计算裁剪区域
            width = x2 - x1
            height = y2 - y1
            
            # 裁剪视频
            result_clip = video_clip.crop(x1=x1, y1=y1, width=width, height=height)
            
            logger.info(f"视频裁剪成功: ({x1}, {y1}) - ({x2}, {y2}), 大小: {width}x{height}")
            return result_clip
        except Exception as e:
            logger.error(f"视频裁剪失败: {e}")
            return None
    
    def _apply_brightness(self, video_clip: mp.VideoFileClip, factor: float = 1.5) -> mp.VideoFileClip:
        """应用亮度调整特效"""
        return video_clip.fx(vfx.colorx, factor)
    
    def _apply_contrast(self, video_clip: mp.VideoFileClip, factor: float = 1.5) -> mp.VideoFileClip:
        """应用对比度调整特效"""
        return video_clip.fx(vfx.colorx, factor)
    
    def _apply_saturation(self, video_clip: mp.VideoFileClip, factor: float = 1.5) -> mp.VideoFileClip:
        """应用饱和度调整特效"""
        def _adjust_saturation(image):
            hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
            hsv[:, :, 1] = np.clip(hsv[:, :, 1] * factor, 0, 255).astype(np.uint8)
            return cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)
        
        return video_clip.fl_image(_adjust_saturation)
    
    def _apply_speed(self, video_clip: mp.VideoFileClip, factor: float = 1.5) -> mp.VideoFileClip:
        """应用速度调整特效"""
        return video_clip.fx(vfx.speedx, factor)
    
    def _apply_reverse(self, video_clip: mp.VideoFileClip, **kwargs) -> mp.VideoFileClip:
        """应用倒放特效"""
        return video_clip.fx(vfx.time_mirror)
    
    def _apply_mirror(self, video_clip: mp.VideoFileClip, axis: str = 'x', **kwargs) -> mp.VideoFileClip:
        """应用镜像特效"""
        if axis == 'x':
            return video_clip.fx(vfx.mirror_x)
        elif axis == 'y':
            return video_clip.fx(vfx.mirror_y)
        else:
            return video_clip
    
    def _apply_black_white(self, video_clip: mp.VideoFileClip, **kwargs) -> mp.VideoFileClip:
        """应用黑白特效"""
        return video_clip.fx(vfx.blackwhite)
    
    def _apply_blur(self, video_clip: mp.VideoFileClip, radius: float = 5.0, **kwargs) -> mp.VideoFileClip:
        """应用模糊特效"""
        def _blur(image):
            return cv2.GaussianBlur(image, (0, 0), radius)
        
        return video_clip.fl_image(_blur)
    
    def _apply_sharpen(self, video_clip: mp.VideoFileClip, **kwargs) -> mp.VideoFileClip:
        """应用锐化特效"""
        def _sharpen(image):
            kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]])
            return cv2.filter2D(image, -1, kernel)
        
        return video_clip.fl_image(_sharpen)
    
    def _apply_noise(self, video_clip: mp.VideoFileClip, amount: float = 0.1, **kwargs) -> mp.VideoFileClip:
        """应用噪点特效"""
        def _add_noise(image):
            noise = np.random.normal(0, amount * 255, image.shape).astype(np.uint8)
            noisy_image = np.clip(image + noise, 0, 255).astype(np.uint8)
            return noisy_image
        
        return video_clip.fl_image(_add_noise)
    
    def _apply_vignette(self, video_clip: mp.VideoFileClip, **kwargs) -> mp.VideoFileClip:
        """应用暗角特效"""
        def _add_vignette(image):
            height, width = image.shape[:2]
            
            # 创建径向渐变
            x = np.linspace(-1, 1, width)
            y = np.linspace(-1, 1, height)
            x_grid, y_grid = np.meshgrid(x, y)
            radius = np.sqrt(x_grid**2 + y_grid**2)
            
            # 创建暗角蒙版
            vignette = np.clip(1 - radius, 0, 1)
            vignette = vignette.reshape(height, width, 1)
            
            # 应用暗角
            result = image * vignette
            
            return result.astype(np.uint8)
        
        return video_clip.fl_image(_add_vignette)
    
    def _apply_rotate(self, video_clip: mp.VideoFileClip, angle: float = 90, **kwargs) -> mp.VideoFileClip:
        """应用旋转特效"""
        return video_clip.rotate(angle)
        
    def _apply_sepia(self, video_clip: mp.VideoFileClip, **kwargs) -> mp.VideoFileClip:
        """应用复古褐色特效"""
        def _sepia_effect(image):
            # 将图像转换为灰度
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            normalized_gray = np.array(gray, np.float32) / 255
            
            # 创建复古褐色效果
            sepia = np.ones(image.shape)
            # 设置褐色调
            sepia[:,:,0] *= 153  # B
            sepia[:,:,1] *= 134  # G
            sepia[:,:,2] *= 100  # R
            
            # 应用灰度图像作为强度
            sepia[:,:,0] *= normalized_gray  # B
            sepia[:,:,1] *= normalized_gray  # G
            sepia[:,:,2] *= normalized_gray  # R
            
            return np.array(sepia, dtype=np.uint8)
            
        return video_clip.fl_image(_sepia_effect)
        
    def _apply_edge_detect(self, video_clip: mp.VideoFileClip, threshold1: float = 100, threshold2: float = 200, **kwargs) -> mp.VideoFileClip:
        """应用边缘检测特效"""
        def _edge_detect(image):
            # 转换为灰度
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            # 应用Canny边缘检测
            edges = cv2.Canny(gray, threshold1, threshold2)
            # 转换回RGB
            return cv2.cvtColor(edges, cv2.COLOR_GRAY2RGB)
            
        return video_clip.fl_image(_edge_detect)
        
    def _apply_pixelate(self, video_clip: mp.VideoFileClip, pixel_size: int = 20, **kwargs) -> mp.VideoFileClip:
        """应用像素化特效"""
        def _pixelate(image):
            height, width = image.shape[:2]
            # 缩小图像
            temp = cv2.resize(image, (width // pixel_size, height // pixel_size), 
                             interpolation=cv2.INTER_LINEAR)
            # 放大回原始尺寸
            return cv2.resize(temp, (width, height), interpolation=cv2.INTER_NEAREST)
            
        return video_clip.fl_image(_pixelate)
        
    def _apply_time_symmetry(self, video_clip: mp.VideoFileClip, **kwargs) -> mp.VideoFileClip:
        """应用时间对称特效 - 正放+倒放"""
        # 创建倒放剪辑
        reversed_clip = video_clip.fx(vfx.time_mirror)
        # 合并正放和倒放
        return mp.concatenate_videoclips([video_clip, reversed_clip])
    
    def _apply_cut_transition(self, clip1: mp.VideoFileClip, clip2: mp.VideoFileClip, 
                             duration: float = 1.0, **kwargs) -> mp.VideoFileClip:
        """应用硬切转场"""
        return mp.concatenate_videoclips([clip1, clip2])
    
    def _apply_fade_transition(self, clip1: mp.VideoFileClip, clip2: mp.VideoFileClip, 
                              duration: float = 1.0, **kwargs) -> mp.VideoFileClip:
        """应用淡入淡出转场"""
        # 确保转场时长不超过视频时长
        duration = min(duration, clip1.duration, clip2.duration)
        
        # 创建淡出效果
        clip1_fadeout = clip1.copy().set_duration(clip1.duration)
        clip1_fadeout = clip1_fadeout.crossfadeout(duration)
        
        # 创建淡入效果
        clip2_fadein = clip2.copy().set_duration(clip2.duration)
        clip2_fadein = clip2_fadein.crossfadein(duration)
        
        # 计算重叠部分
        clip1_part = clip1.subclip(0, clip1.duration - duration)
        clip2_part = clip2.subclip(duration, clip2.duration)
        
        # 创建重叠部分
        overlap = mp.CompositeVideoClip([
            clip1.subclip(clip1.duration - duration, clip1.duration).crossfadeout(duration),
            clip2.subclip(0, duration).crossfadein(duration)
        ])
        
        # 合并视频
        return mp.concatenate_videoclips([clip1_part, overlap, clip2_part])
    
    def _apply_wipe_transition(self, clip1: mp.VideoFileClip, clip2: mp.VideoFileClip, 
                              duration: float = 1.0, direction: str = 'left', **kwargs) -> mp.VideoFileClip:
        """应用擦除转场"""
        # 确保转场时长不超过视频时长
        duration = min(duration, clip1.duration, clip2.duration)
        
        # 创建遮罩
        def make_mask(t):
            """创建遮罩"""
            progress = t / duration
            
            if direction == 'left':
                return np.ones((clip1.h, clip1.w)) * (progress >= (1 - np.linspace(0, 1, clip1.w)))
            elif direction == 'right':
                return np.ones((clip1.h, clip1.w)) * (progress >= np.linspace(0, 1, clip1.w))
            elif direction == 'up':
                return np.ones((clip1.h, clip1.w)) * (progress >= (1 - np.linspace(0, 1, clip1.h))[:, np.newaxis])
            elif direction == 'down':
                return np.ones((clip1.h, clip1.w)) * (progress >= np.linspace(0, 1, clip1.h)[:, np.newaxis])
            else:
                return np.ones((clip1.h, clip1.w)) * (progress >= 0.5)
        
        # 创建遮罩剪辑
        mask_clip = mp.VideoClip(make_mask, duration=duration)
        
        # 创建转场剪辑
        transition_clip = mp.CompositeVideoClip([
            clip1.subclip(clip1.duration - duration, clip1.duration),
            clip2.subclip(0, duration).set_mask(mask_clip)
        ])
        
        # 合并视频
        return mp.concatenate_videoclips([
            clip1.subclip(0, clip1.duration - duration),
            transition_clip,
            clip2.subclip(duration, clip2.duration)
        ])
    
    def _apply_dissolve_transition(self, clip1: mp.VideoFileClip, clip2: mp.VideoFileClip, 
                                  duration: float = 1.0, **kwargs) -> mp.VideoFileClip:
        """应用溶解转场"""
        # 确保转场时长不超过视频时长
        duration = min(duration, clip1.duration, clip2.duration)
        
        # 创建转场剪辑
        transition_clip = mp.CompositeVideoClip([
            clip1.subclip(clip1.duration - duration, clip1.duration).crossfadeout(duration),
            clip2.subclip(0, duration).crossfadein(duration)
        ])
        
        # 合并视频
        return mp.concatenate_videoclips([
            clip1.subclip(0, clip1.duration - duration),
            transition_clip,
            clip2.subclip(duration, clip2.duration)
        ])
    
    def _apply_zoom_transition(self, clip1: mp.VideoFileClip, clip2: mp.VideoFileClip, 
                              duration: float = 1.0, zoom_in: bool = True, **kwargs) -> mp.VideoFileClip:
        """应用缩放转场"""
        # 确保转场时长不超过视频时长
        duration = min(duration, clip1.duration, clip2.duration)
        
        # 创建缩放效果
        if zoom_in:
            clip1_zoom = clip1.subclip(clip1.duration - duration, clip1.duration)
            clip1_zoom = clip1_zoom.fx(vfx.resize, lambda t: 1 + t/duration)
            clip1_zoom = clip1_zoom.set_position('center')
            
            # 创建转场剪辑
            transition_clip = mp.CompositeVideoClip([
                clip1_zoom,
                clip2.subclip(0, duration).set_opacity(lambda t: t/duration)
            ], size=clip1.size)
        else:
            clip2_zoom = clip2.subclip(0, duration)
            clip2_zoom = clip2_zoom.fx(vfx.resize, lambda t: 2 - t/duration)
            clip2_zoom = clip2_zoom.set_position('center')
            
            # 创建转场剪辑
            transition_clip = mp.CompositeVideoClip([
                clip1.subclip(clip1.duration - duration, clip1.duration).set_opacity(lambda t: 1 - t/duration),
                clip2_zoom
            ], size=clip1.size)
        
        # 合并视频
        return mp.concatenate_videoclips([
            clip1.subclip(0, clip1.duration - duration),
            transition_clip,
            clip2.subclip(duration, clip2.duration)
        ])
    
    def _apply_slide_transition(self, clip1: mp.VideoFileClip, clip2: mp.VideoFileClip, 
                               duration: float = 1.0, direction: str = 'left', **kwargs) -> mp.VideoFileClip:
        """应用滑动转场"""
        # 确保转场时长不超过视频时长
        duration = min(duration, clip1.duration, clip2.duration)
        
        # 设置滑动方向
        if direction == 'left':
            pos_clip1 = lambda t: ('center', 'center')
            pos_clip2 = lambda t: (clip1.w * (1 - t/duration), 'center')
        elif direction == 'right':
            pos_clip1 = lambda t: ('center', 'center')
            pos_clip2 = lambda t: (-clip1.w * (1 - t/duration), 'center')
        elif direction == 'up':
            pos_clip1 = lambda t: ('center', 'center')
            pos_clip2 = lambda t: ('center', clip1.h * (1 - t/duration))
        elif direction == 'down':
            pos_clip1 = lambda t: ('center', 'center')
            pos_clip2 = lambda t: ('center', -clip1.h * (1 - t/duration))
        else:
            pos_clip1 = lambda t: ('center', 'center')
            pos_clip2 = lambda t: (clip1.w * (1 - t/duration), 'center')
        
        # 创建转场剪辑
        clip1_part = clip1.subclip(clip1.duration - duration, clip1.duration).set_position(pos_clip1)
        clip2_part = clip2.subclip(0, duration).set_position(pos_clip2)
        
        transition_clip = mp.CompositeVideoClip([clip1_part, clip2_part], size=clip1.size)
        
        # 合并视频
        return mp.concatenate_videoclips([
            clip1.subclip(0, clip1.duration - duration),
            transition_clip,
            clip2.subclip(duration, clip2.duration)
        ])
        
    def register_temp_file(self, file_path: str) -> None:
        """
        注册临时文件，用于后续清理。
        
        Args:
            file_path: 临时文件路径
        """
        if os.path.exists(file_path):
            self.temp_files.append(file_path)
            logger.debug(f"注册临时文件: {file_path}")
        
    def cleanup_temp_files(self) -> None:
        """
        清理所有临时文件。
        """
        count = 0
        for file_path in self.temp_files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    count += 1
                    logger.debug(f"已删除临时文件: {file_path}")
            except Exception as e:
                logger.warning(f"删除临时文件失败: {file_path}, 错误: {e}")
                
        # 尝试删除空的临时目录
        try:
            if os.path.exists(self.temp_dir) and not os.listdir(self.temp_dir):
                os.rmdir(self.temp_dir)
                logger.info(f"已删除空的临时目录: {self.temp_dir}")
        except Exception as e:
            logger.warning(f"删除临时目录失败: {self.temp_dir}, 错误: {e}")
                
    # ===== 并行处理方法 =====
    
    def apply_effect_parallel(self, video_path: str, output_path: str, effect: str, 
                            max_workers: Optional[int] = None, **params) -> bool:
        """
        并行应用视频特效，适用于大型视频文件。
        
        Args:
            video_path: 输入视频路径
            output_path: 输出视频路径
            effect: 特效名称
            max_workers: 最大工作进程数，如果为None则使用CPU核心数
            **params: 特效参数
            
        Returns:
            是否成功应用特效
        """
        try:
            # 检查特效是否支持
            if effect not in self.supported_effects:
                logger.error(f"不支持的特效: {effect}")
                return False
                
            # 导入并行处理器
            from backend.agent.smart_editor.parallel_processor import ParallelVideoProcessor
            
            # 创建并行处理器
            processor = ParallelVideoProcessor(temp_dir=self.temp_dir, max_workers=max_workers)
            
            # 并行处理视频
            result = processor.process_video_parallel(
                video_path=video_path,
                output_path=output_path,
                process_func=processor.apply_effect_to_chunks,
                effect_name=effect,
                **params
            )
            
            return result
            
        except Exception as e:
            logger.error(f"并行特效应用失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
            
    def process_large_video(self, video_path: str, output_path: str,
                          operations: List[Dict[str, Any]],
                          max_workers: Optional[int] = None) -> bool:
        """
        处理大型视频文件，支持多种操作的组合。
        
        Args:
            video_path: 输入视频路径
            output_path: 输出视频路径
            operations: 操作列表，每个操作为一个字典，包含 'type' 和其他参数
                支持的操作类型:
                - 'cut': 剪切视频，参数: start_time, end_time
                - 'effect': 应用特效，参数: name, **params
                - 'speed': 调整速度，参数: factor
                - 'extract_highlights': 提取精彩片段，参数: duration, threshold
            max_workers: 最大工作进程数，如果为None则使用CPU核心数
            
        Returns:
            是否成功处理
        """
        try:
            # 检查操作列表
            if not operations:
                logger.error("操作列表为空")
                return False
                
            # 创建临时文件路径
            temp_input = video_path
            temp_output = None
            
            # 逐步应用操作
            for i, operation in enumerate(operations):
                op_type = operation.get('type')
                
                # 创建临时输出路径
                if i < len(operations) - 1:
                    import tempfile
                    temp_fd, temp_output = tempfile.mkstemp(prefix=f"temp_op_{i}_", suffix=".mp4", dir=self.temp_dir)
                    os.close(temp_fd)  # 关闭文件描述符，只保留路径
                    self.temp_files.append(temp_output)
                else:
                    temp_output = output_path
                
                # 根据操作类型处理
                if op_type == 'cut':
                    # 剪切视频
                    start_time = operation.get('start_time', 0)
                    end_time = operation.get('end_time')
                    
                    # 加载视频
                    video = self.load_video(temp_input)
                    if video is None:
                        return False
                        
                    # 剪切视频
                    if end_time is not None:
                        cut_video = self.cut_video(video, start_time, end_time)
                    else:
                        cut_video = self.cut_video(video, start_time, video.duration)
                    if cut_video is None:
                        video.close()
                        return False
                        
                    # 保存视频
                    result = self.save_video(cut_video, temp_output)
                    
                    # 关闭视频
                    video.close()
                    cut_video.close()
                    
                    if not result:
                        return False
                
                elif op_type == 'effect':
                    # 应用特效
                    effect_name = operation.get('name')
                    if not effect_name:
                        logger.error("未指定特效名称")
                        return False
                        
                    # 提取特效参数
                    effect_params = {k: v for k, v in operation.items() if k not in ['type', 'name']}
                    
                    # 并行应用特效
                    result = self.apply_effect_parallel(
                        temp_input, temp_output, 
                        effect_name, max_workers, 
                        **effect_params
                    )
                    
                    if not result:
                        return False
                
                elif op_type == 'speed':
                    # 调整速度
                    factor = operation.get('factor', 1.0)
                    
                    # 加载视频
                    video = self.load_video(temp_input)
                    if video is None:
                        return False
                        
                    # 调整速度
                    speed_video = video.fx(vfx.speedx, factor)
                    
                    # 保存视频
                    result = self.save_video(speed_video, temp_output)
                    
                    # 关闭视频
                    video.close()
                    speed_video.close()
                    
                    if not result:
                        return False
                
                elif op_type == 'extract_highlights':
                    # 提取精彩片段
                    duration = operation.get('duration', 30.0)
                    threshold = operation.get('threshold', 30.0)
                    
                    # 提取精彩片段
                    highlight_clip = self.extract_highlights(
                        temp_input, duration, threshold,
                        min_scene_length=1.0
                    )
                    
                    if highlight_clip is None:
                        return False
                        
                    # 保存视频
                    result = self.save_video(highlight_clip, temp_output)
                    
                    # 关闭视频
                    highlight_clip.close()
                    
                    if not result:
                        return False
                
                else:
                    logger.error(f"不支持的操作类型: {op_type}")
                    return False
                
                # 更新输入路径为当前输出路径
                if i < len(operations) - 1:
                    temp_input = temp_output
            
            logger.info(f"大型视频处理完成: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"大型视频处理失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
            
        # 清空临时文件列表
        file_count = len(self.temp_files)
        self.temp_files = []
        logger.info(f"清理完成，共删除 {file_count} 个临时文件")
