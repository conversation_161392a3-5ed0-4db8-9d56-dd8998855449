"""
视频摘要功能扩展模块
"""

import logging

import moviepy.editor as mp

logger = logging.getLogger(__name__)


def create_video_summary(
    self,
    video_path: str,
    output_path: str,
    duration: float = 30.0,
    threshold: float = 20.0,
    method: str = "motion_based",
) -> bool:
    """
    创建视频摘要

    Args:
        video_path: 视频文件路径
        output_path: 输出文件路径
        duration: 摘要时长（秒）
        threshold: 检测阈值
        method: 摘要方法，可选 'motion_based'（基于运动）, 'scene_based'（基于场景）

    Returns:
        是否成功创建摘要
    """
    try:
        # 导入视频摘要器
        from backend.agent.smart_editor.video_summarizer import VideoSummarizer

        # 加载视频
        video_clip = self.load_video(video_path)
        if video_clip is None:
            logger.error("视频加载失败")
            return False

        # 创建视频摘要器
        summarizer = VideoSummarizer()

        # 根据方法选择摘要策略
        if method == "motion_based":
            # 提取基于运动的片段
            segments = summarizer.summarize_by_motion(video_path, duration, threshold)
        else:
            # 使用现有的场景检测方法
            scenes = self.extract_scenes(video_path, threshold, min_scene_length=1.0)
            segments = scenes[: int(len(scenes) / 2)]  # 简单起见，取前半部分场景

        if not segments:
            logger.warning("未检测到片段，返回原始视频的前段")
            # 使用视频的前段作为摘要
            summary_clip = video_clip.subclip(0, min(duration, video_clip.duration))
        else:
            # 提取选中的片段
            clips = []
            for start_time, end_time in segments:
                if end_time is None or start_time >= video_clip.duration:
                    continue
                # 确保时间范围有效
                end_time = min(end_time, video_clip.duration)
                if end_time <= start_time:
                    continue

                clip = video_clip.subclip(start_time, end_time)
                clips.append(clip)

            # 合并片段
            if not clips:
                logger.warning("未选中任何有效片段，返回原始视频的前段")
                summary_clip = video_clip.subclip(0, min(duration, video_clip.duration))
            else:
                summary_clip = mp.concatenate_videoclips(clips)
                # 添加淡入淡出效果
                summary_clip = summary_clip.fadein(0.5).fadeout(0.5)

        # 保存摘要视频
        result = self.save_video(
            summary_clip, output_path, codec="libx264", bitrate="5000k", audio_codec="aac", audio_bitrate="192k"
        )

        # 关闭视频剪辑
        video_clip.close()
        if summary_clip != video_clip:
            summary_clip.close()

        return result

    except Exception as e:
        logger.error("视频摘要创建失败: {e}")
        import traceback

        logger.error(traceback.format_exc())
        return False


# 将此方法添加到VideoEditor类
def add_summary_method_to_video_editor():
    """将视频摘要方法添加到VideoEditor类"""
    from backend.agent.smart_editor.video_editor import VideoEditor

    VideoEditor.create_video_summary = create_video_summary
    logger.info("视频摘要方法已添加到VideoEditor类")
