#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能剪辑器示例脚本

此脚本演示如何使用智能剪辑器进行视频编辑，包括：
1. 提取视频精彩片段
2. 创建视频合集
3. 为特定平台优化视频
"""

import argparse
import logging
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入智能剪辑器
from backend.agent.smart_editor.smart_editor import SmartEditor

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


def create_highlight(args):
    """创建视频精彩片段"""
    editor = SmartEditor(output_dir=args.output_dir)

    output_path = editor.create_highlight(
        video_path=args.input,
        duration=args.duration,
        style=args.style,
        platform=args.platform,
        add_music=not args.no_music,
        add_text=not args.no_text,
        add_watermark=args.watermark,
    )

    logger.info("精彩片段已创建: {output_path}")
    return output_path


def create_compilation(args):
    """创建视频合集"""
    editor = SmartEditor(output_dir=args.output_dir)

    # 检查输入视频列表
    if not args.input:
        logger.error("未指定输入视频")
        return None

    # 如果输入是目录，获取目录中的所有视频文件
    if os.path.isdir(args.input[0]):
        video_dir = args.input[0]
        video_extensions = [".mp4", ".avi", ".mov", ".mkv", ".webm", ".flv"]
        video_paths = []

        for file in os.listdir(video_dir):
            file_path = os.path.join(video_dir, file)
            if os.path.isfile(file_path) and os.path.splitext(file)[1].lower() in video_extensions:
                video_paths.append(file_path)

        if not video_paths:
            logger.error("目录中没有找到视频文件: {video_dir}")
            return None
    else:
        # 使用指定的视频文件列表
        video_paths = args.input

    output_path = editor.create_compilation(
        video_paths=video_paths,
        duration=args.duration,
        style=args.style,
        platform=args.platform,
        add_music=not args.no_music,
        add_text=not args.no_text,
        add_watermark=args.watermark,
    )

    logger.info("视频合集已创建: {output_path}")
    return output_path


def optimize_for_platform(args):
    """为特定平台优化视频"""
    editor = SmartEditor(output_dir=args.output_dir)

    output_path = editor.optimize_for_platform(
        video_path=args.input, platform=args.platform, add_watermark=args.watermark
    )

    logger.info("视频已优化: {output_path}")
    return output_path


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="智能剪辑器示例脚本")
    subparsers = parser.add_subparsers(dest="command", help="子命令")

    # 创建精彩片段子命令
    highlight_parser = subparsers.add_parser("highlight", help="创建视频精彩片段")
    highlight_parser.add_argument("input", help="输入视频文件路径")
    highlight_parser.add_argument("--duration", type=float, help="目标视频时长（秒）")
    highlight_parser.add_argument("--style", choices=["vlog", "action", "cinematic", "comedy"], help="视频风格")
    highlight_parser.add_argument("--platform", choices=["xigua", "toutiao", "xiaohongshu", "youtube"], help="目标平台")
    highlight_parser.add_argument("--output-dir", help="输出目录")
    highlight_parser.add_argument("--no-music", action="store_true", help="不添加背景音乐")
    highlight_parser.add_argument("--no-text", action="store_true", help="不添加文本")
    highlight_parser.add_argument("--watermark", action="store_true", help="添加水印")

    # 创建视频合集子命令
    compilation_parser = subparsers.add_parser("compilation", help="创建视频合集")
    compilation_parser.add_argument("input", nargs="+", help="输入视频文件路径列表或包含视频的目录")
    compilation_parser.add_argument("--duration", type=float, help="目标视频时长（秒）")
    compilation_parser.add_argument("--style", choices=["vlog", "action", "cinematic", "comedy"], help="视频风格")
    compilation_parser.add_argument(
        "--platform", choices=["xigua", "toutiao", "xiaohongshu", "youtube"], help="目标平台"
    )
    compilation_parser.add_argument("--output-dir", help="输出目录")
    compilation_parser.add_argument("--no-music", action="store_true", help="不添加背景音乐")
    compilation_parser.add_argument("--no-text", action="store_true", help="不添加文本")
    compilation_parser.add_argument("--watermark", action="store_true", help="添加水印")

    # 为特定平台优化视频子命令
    optimize_parser = subparsers.add_parser("optimize", help="为特定平台优化视频")
    optimize_parser.add_argument("input", help="输入视频文件路径")
    optimize_parser.add_argument(
        "--platform", required=True, choices=["xigua", "toutiao", "xiaohongshu", "youtube"], help="目标平台"
    )
    optimize_parser.add_argument("--output-dir", help="输出目录")
    optimize_parser.add_argument("--watermark", action="store_true", help="添加水印")

    args = parser.parse_args()

    if args.command == "highlight":
        create_highlight(args)
    elif args.command == "compilation":
        create_compilation(args)
    elif args.command == "optimize":
        optimize_for_platform(args)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
