#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IntelliCutAgent 配置文件
"""

import os

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# 目录配置
DIRECTORIES = {
    "data": os.path.join(PROJECT_ROOT, "data"),
    "logs": os.path.join(PROJECT_ROOT, "logs"),
    "output": os.path.join(PROJECT_ROOT, "output"),
    "models": os.path.join(PROJECT_ROOT, "models"),
    "workspace": os.path.join(PROJECT_ROOT, "workspace"),
}

# 系统配置
SYSTEM = {
    "name": "IntelliCutAgent",
    "version": "2.0.0",
    "description": "智能视频处理系统",
    "max_task_history": 100,
    "default_video_duration": 60,
    "default_edit_style": "standard"
}

# 支持的平台
SUPPORTED_PLATFORMS = [
    "douyin",
    "bilibili", 
    "youtube",
    "xiaohongshu",
    "toutiao"
]

# 支持的视频格式
SUPPORTED_VIDEO_FORMATS = [
    ".mp4",
    ".avi", 
    ".mov",
    ".mkv",
    ".wmv",
    ".flv"
]

# 支持的音频格式
SUPPORTED_AUDIO_FORMATS = [
    ".mp3",
    ".wav",
    ".aac",
    ".flac",
    ".ogg"
]

# 日志配置
LOGGING = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "file": os.path.join(DIRECTORIES["logs"], "intellicut.log")
}

# 视频处理配置
VIDEO_PROCESSING = {
    "default_resolution": "1920x1080",
    "default_fps": 30,
    "default_bitrate": "5000k",
    "max_file_size": "500MB"
}

# 平台特定配置
PLATFORM_CONFIGS = {
    "douyin": {
        "max_duration": 60,
        "recommended_resolution": "1080x1920",  # 竖屏
        "aspect_ratio": "9:16"
    },
    "bilibili": {
        "max_duration": 600,
        "recommended_resolution": "1920x1080",  # 横屏
        "aspect_ratio": "16:9"
    },
    "youtube": {
        "max_duration": 3600,
        "recommended_resolution": "1920x1080",
        "aspect_ratio": "16:9"
    },
    "xiaohongshu": {
        "max_duration": 90,
        "recommended_resolution": "1080x1920",
        "aspect_ratio": "9:16"
    }
}
