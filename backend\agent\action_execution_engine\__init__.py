# backend.agent.action_execution_engine

# This package is responsible for executing the planned tasks.
# It interacts with various tools and sub-systems to perform actions.

from .task_executor import TaskExecutor
from .tool_interface import ToolInterface  # For interacting with external tools/APIs

# Potentially specific executors for complex tasks if needed
# from .video_editing_executor import VideoEditingExecutor
# from .data_processing_executor import DataProcessingExecutor

__all__ = ["TaskExecutor", "ToolInterface"]
