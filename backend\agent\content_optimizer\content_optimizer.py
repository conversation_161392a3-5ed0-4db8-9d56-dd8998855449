# backend.agent.content_optimizer.content_optimizer

import os
import json
import logging
import time
import random
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from collections import Counter

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ContentOptimizer:
    """
    内容优化器：根据历史数据和市场分析，优化视频内容，提高收益和播放量
    """
    
    def __init__(self, config_dir: str = None, data_dir: str = None):
        """
        初始化内容优化器
        
        Args:
            config_dir: 配置文件目录，默认为当前目录下的 'config'
            data_dir: 数据存储目录，默认为当前目录下的 'data/optimizer'
        """
        self.config_dir = config_dir or os.path.join(os.getcwd(), 'config')
        self.data_dir = data_dir or os.path.join(os.getcwd(), 'data', 'optimizer')
        
        # 确保目录存在
        os.makedirs(self.config_dir, exist_ok=True)
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 加载配置
        self.optimization_config = self._load_optimization_config()
        
        # 加载历史数据
        self.performance_data = self._load_performance_data()
        
        logger.info(f"ContentOptimizer 初始化完成")
    
    def _load_optimization_config(self) -> Dict[str, Any]:
        """
        加载优化配置
        
        Returns:
            优化配置字典
        """
        config_path = os.path.join(self.config_dir, "optimization_config.json")
        
        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载优化配置失败: {e}")
                return self._get_default_optimization_config()
        else:
            logger.info(f"优化配置文件不存在，使用默认配置")
            default_config = self._get_default_optimization_config()
            
            # 保存默认配置
            try:
                with open(config_path, 'w', encoding='utf-8') as f:
                    json.dump(default_config, f, ensure_ascii=False, indent=2)
            except Exception as e:
                logger.error(f"保存默认优化配置失败: {e}")
            
            return default_config
    
    def _get_default_optimization_config(self) -> Dict[str, Any]:
        """
        获取默认优化配置
        
        Returns:
            默认优化配置字典
        """
        return {
            "content_features": {
                "duration": {
                    "weight": 0.15,
                    "optimal_ranges": {
                        "douyin": {"min": 15, "max": 60, "ideal": 30},
                        "kuaishou": {"min": 15, "max": 60, "ideal": 30},
                        "bilibili": {"min": 180, "max": 600, "ideal": 300}
                    }
                },
                "pace": {
                    "weight": 0.1,
                    "optimal_ranges": {
                        "douyin": {"min": 0.8, "max": 1.5, "ideal": 1.2},
                        "kuaishou": {"min": 0.8, "max": 1.5, "ideal": 1.2},
                        "bilibili": {"min": 0.6, "max": 1.2, "ideal": 0.9}
                    }
                },
                "scene_duration": {
                    "weight": 0.1,
                    "optimal_ranges": {
                        "douyin": {"min": 1.5, "max": 3.0, "ideal": 2.0},
                        "kuaishou": {"min": 1.5, "max": 3.0, "ideal": 2.0},
                        "bilibili": {"min": 3.0, "max": 8.0, "ideal": 5.0}
                    }
                },
                "audio_quality": {
                    "weight": 0.15,
                    "thresholds": {
                        "noise_level": {"max": 0.1},
                        "speech_clarity": {"min": 0.7},
                        "music_quality": {"min": 0.6}
                    }
                },
                "visual_quality": {
                    "weight": 0.15,
                    "thresholds": {
                        "brightness": {"min": 0.4, "max": 0.7},
                        "contrast": {"min": 0.4, "max": 0.7},
                        "saturation": {"min": 0.4, "max": 0.7},
                        "sharpness": {"min": 0.5}
                    }
                },
                "engagement_elements": {
                    "weight": 0.2,
                    "elements": {
                        "face_close_up": {"weight": 0.3, "optimal_frequency": 0.2},
                        "action_scenes": {"weight": 0.2, "optimal_frequency": 0.3},
                        "text_overlays": {"weight": 0.2, "optimal_frequency": 0.4},
                        "emotional_moments": {"weight": 0.3, "optimal_frequency": 0.2}
                    }
                },
                "narrative_structure": {
                    "weight": 0.15,
                    "elements": {
                        "hook": {"weight": 0.3, "duration_ratio": 0.1},
                        "buildup": {"weight": 0.2, "duration_ratio": 0.3},
                        "climax": {"weight": 0.3, "duration_ratio": 0.2},
                        "conclusion": {"weight": 0.2, "duration_ratio": 0.1}
                    }
                }
            },
            "platform_specific": {
                "douyin": {
                    "aspect_ratio": "9:16",
                    "resolution": "1080x1920",
                    "preferred_transitions": ["fade", "slide"],
                    "preferred_effects": ["brightness", "saturation"],
                    "music_importance": 0.7
                },
                "kuaishou": {
                    "aspect_ratio": "9:16",
                    "resolution": "1080x1920",
                    "preferred_transitions": ["fade", "wipe"],
                    "preferred_effects": ["contrast", "speed"],
                    "music_importance": 0.6
                },
                "bilibili": {
                    "aspect_ratio": "16:9",
                    "resolution": "1920x1080",
                    "preferred_transitions": ["fade", "dissolve"],
                    "preferred_effects": ["saturation", "blur"],
                    "music_importance": 0.5
                }
            },
            "optimization_levels": {
                "basic": {
                    "features": ["duration", "audio_quality", "visual_quality"],
                    "intensity": 0.5
                },
                "standard": {
                    "features": ["duration", "pace", "scene_duration", "audio_quality", "visual_quality"],
                    "intensity": 0.7
                },
                "advanced": {
                    "features": ["duration", "pace", "scene_duration", "audio_quality", "visual_quality", "engagement_elements"],
                    "intensity": 0.8
                },
                "professional": {
                    "features": ["duration", "pace", "scene_duration", "audio_quality", "visual_quality", "engagement_elements", "narrative_structure"],
                    "intensity": 1.0
                }
            }
        }
    
    def _load_performance_data(self) -> Dict[str, pd.DataFrame]:
        """
        加载性能数据
        
        Returns:
            性能数据字典，键为平台名称，值为DataFrame
        """
        performance_data = {}
        
        # 支持的平台
        platforms = ['douyin', 'kuaishou', 'bilibili']
        
        for platform in platforms:
            data_file = os.path.join(self.data_dir, f"{platform}_performance.csv")
            
            if os.path.exists(data_file):
                try:
                    df = pd.read_csv(data_file)
                    performance_data[platform] = df
                    logger.info(f"已加载 {platform} 平台性能数据: {len(df)} 条记录")
                except Exception as e:
                    logger.error(f"加载 {platform} 平台性能数据失败: {e}")
                    performance_data[platform] = pd.DataFrame()
            else:
                logger.info(f"{platform} 平台性能数据文件不存在")
                performance_data[platform] = pd.DataFrame()
        
        return performance_data
    
    def _save_performance_data(self, platform: str, data: pd.DataFrame) -> None:
        """
        保存性能数据
        
        Args:
            platform: 平台名称
            data: 性能数据
        """
        if data.empty:
            return
        
        data_file = os.path.join(self.data_dir, f"{platform}_performance.csv")
        
        try:
            data.to_csv(data_file, index=False)
            logger.info(f"已保存 {platform} 平台性能数据: {len(data)} 条记录")
        except Exception as e:
            logger.error(f"保存 {platform} 平台性能数据失败: {e}")
    
    def analyze_video_performance(self, video_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析视频性能
        
        Args:
            video_data: 视频数据，包括平台、内容特征和性能指标
            
        Returns:
            性能分析结果
        """
        logger.info(f"分析视频性能: {video_data.get('video_id', 'unknown')}")
        
        # 提取基本信息
        platform = video_data.get('platform')
        video_id = video_data.get('video_id')
        
        if not platform or not video_id:
            logger.error("缺少平台或视频ID信息")
            return {
                "status": "error",
                "message": "缺少平台或视频ID信息"
            }
        
        # 提取内容特征
        content_features = video_data.get('content_features', {})
        
        # 提取性能指标
        performance_metrics = video_data.get('performance_metrics', {})
        
        # 保存数据
        self._save_video_data(platform, video_id, content_features, performance_metrics)
        
        # 分析性能
        performance_analysis = self._analyze_performance(platform, content_features, performance_metrics)
        
        return {
            "status": "success",
            "video_id": video_id,
            "platform": platform,
            "performance_analysis": performance_analysis
        }
    
    def _save_video_data(self, platform: str, video_id: str, 
                        content_features: Dict[str, Any], 
                        performance_metrics: Dict[str, Any]) -> None:
        """
        保存视频数据
        
        Args:
            platform: 平台名称
            video_id: 视频ID
            content_features: 内容特征
            performance_metrics: 性能指标
        """
        # 准备数据
        data = {
            "video_id": video_id,
            "platform": platform,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 添加内容特征
        for key, value in content_features.items():
            data[f"feature_{key}"] = value
        
        # 添加性能指标
        for key, value in performance_metrics.items():
            data[f"metric_{key}"] = value
        
        # 转换为DataFrame
        df = pd.DataFrame([data])
        
        # 如果已有数据，则合并
        if platform in self.performance_data and not self.performance_data[platform].empty:
            # 检查是否已存在该视频的数据
            existing_data = self.performance_data[platform]
            existing_video = existing_data[existing_data['video_id'] == video_id]
            
            if not existing_video.empty:
                # 更新现有数据
                existing_data.loc[existing_data['video_id'] == video_id] = df.iloc[0]
                self.performance_data[platform] = existing_data
            else:
                # 添加新数据
                self.performance_data[platform] = pd.concat([existing_data, df], ignore_index=True)
        else:
            # 创建新数据
            self.performance_data[platform] = df
        
        # 保存数据
        self._save_performance_data(platform, self.performance_data[platform])
    
    def _analyze_performance(self, platform: str, 
                            content_features: Dict[str, Any], 
                            performance_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析性能
        
        Args:
            platform: 平台名称
            content_features: 内容特征
            performance_metrics: 性能指标
            
        Returns:
            性能分析结果
        """
        # 获取平台数据
        platform_data = self.performance_data.get(platform, pd.DataFrame())
        
        if platform_data.empty:
            logger.warning(f"没有 {platform} 平台的历史数据，无法进行比较分析")
            return self._basic_performance_analysis(platform, content_features, performance_metrics)
        
        # 提取性能指标
        views = performance_metrics.get('views', 0)
        likes = performance_metrics.get('likes', 0)
        comments = performance_metrics.get('comments', 0)
        shares = performance_metrics.get('shares', 0)
        revenue = performance_metrics.get('revenue', 0)
        
        # 计算互动率
        engagement_rate = 0
        if views > 0:
            engagement_rate = (likes + comments + shares) / views
        
        # 与历史数据比较
        avg_views = platform_data['metric_views'].mean() if 'metric_views' in platform_data.columns else 0
        avg_likes = platform_data['metric_likes'].mean() if 'metric_likes' in platform_data.columns else 0
        avg_comments = platform_data['metric_comments'].mean() if 'metric_comments' in platform_data.columns else 0
        avg_shares = platform_data['metric_shares'].mean() if 'metric_shares' in platform_data.columns else 0
        avg_revenue = platform_data['metric_revenue'].mean() if 'metric_revenue' in platform_data.columns else 0
        
        # 计算平均互动率
        avg_engagement_rate = 0
        if 'metric_views' in platform_data.columns and platform_data['metric_views'].mean() > 0:
            avg_engagement_rate = (platform_data['metric_likes'].mean() + 
                                  platform_data['metric_comments'].mean() + 
                                  platform_data['metric_shares'].mean()) / platform_data['metric_views'].mean()
        
        # 计算性能百分比 (相对于平均值)
        views_percentage = (views / avg_views * 100) if avg_views > 0 else 0
        likes_percentage = (likes / avg_likes * 100) if avg_likes > 0 else 0
        comments_percentage = (comments / avg_comments * 100) if avg_comments > 0 else 0
        shares_percentage = (shares / avg_shares * 100) if avg_shares > 0 else 0
        revenue_percentage = (revenue / avg_revenue * 100) if avg_revenue > 0 else 0
        engagement_percentage = (engagement_rate / avg_engagement_rate * 100) if avg_engagement_rate > 0 else 0
        
        # 分析内容特征与性能的关系
        feature_impact = self._analyze_feature_impact(platform, content_features, performance_metrics)
        
        # 构建分析结果
        return {
            "metrics": {
                "views": views,
                "likes": likes,
                "comments": comments,
                "shares": shares,
                "revenue": revenue,
                "engagement_rate": round(engagement_rate * 100, 2)  # 转换为百分比
            },
            "comparison": {
                "avg_views": round(avg_views, 2),
                "avg_likes": round(avg_likes, 2),
                "avg_comments": round(avg_comments, 2),
                "avg_shares": round(avg_shares, 2),
                "avg_revenue": round(avg_revenue, 2),
                "avg_engagement_rate": round(avg_engagement_rate * 100, 2)  # 转换为百分比
            },
            "percentages": {
                "views": round(views_percentage, 2),
                "likes": round(likes_percentage, 2),
                "comments": round(comments_percentage, 2),
                "shares": round(shares_percentage, 2),
                "revenue": round(revenue_percentage, 2),
                "engagement": round(engagement_percentage, 2)
            },
            "feature_impact": feature_impact
        }
    
    def _basic_performance_analysis(self, platform: str, 
                                   content_features: Dict[str, Any], 
                                   performance_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        基本性能分析 (无历史数据时使用)
        
        Args:
            platform: 平台名称
            content_features: 内容特征
            performance_metrics: 性能指标
            
        Returns:
            基本性能分析结果
        """
        # 提取性能指标
        views = performance_metrics.get('views', 0)
        likes = performance_metrics.get('likes', 0)
        comments = performance_metrics.get('comments', 0)
        shares = performance_metrics.get('shares', 0)
        revenue = performance_metrics.get('revenue', 0)
        
        # 计算互动率
        engagement_rate = 0
        if views > 0:
            engagement_rate = (likes + comments + shares) / views
        
        # 构建分析结果
        return {
            "metrics": {
                "views": views,
                "likes": likes,
                "comments": comments,
                "shares": shares,
                "revenue": revenue,
                "engagement_rate": round(engagement_rate * 100, 2)  # 转换为百分比
            },
            "comparison": {
                "avg_views": "无历史数据",
                "avg_likes": "无历史数据",
                "avg_comments": "无历史数据",
                "avg_shares": "无历史数据",
                "avg_revenue": "无历史数据",
                "avg_engagement_rate": "无历史数据"
            },
            "percentages": {
                "views": "无历史数据",
                "likes": "无历史数据",
                "comments": "无历史数据",
                "shares": "无历史数据",
                "revenue": "无历史数据",
                "engagement": "无历史数据"
            },
            "feature_impact": {}
        }
    
    def _analyze_feature_impact(self, platform: str, 
                               content_features: Dict[str, Any], 
                               performance_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析内容特征对性能的影响
        
        Args:
            platform: 平台名称
            content_features: 内容特征
            performance_metrics: 性能指标
            
        Returns:
            特征影响分析结果
        """
        # 获取平台数据
        platform_data = self.performance_data.get(platform, pd.DataFrame())
        
        if platform_data.empty or len(platform_data) < 5:
            logger.warning(f"{platform} 平台的历史数据不足，无法进行特征影响分析")
            return {}
        
        # 分析结果
        feature_impact = {}
        
        # 分析每个特征的影响
        for feature, value in content_features.items():
            feature_col = f"feature_{feature}"
            
            if feature_col not in platform_data.columns:
                continue
            
            # 尝试将特征值转换为数值
            try:
                value = float(value)
            except (ValueError, TypeError):
                continue
            
            # 查找具有相似特征值的视频
            similar_videos = platform_data[
                (platform_data[feature_col] >= value * 0.9) & 
                (platform_data[feature_col] <= value * 1.1)
            ]
            
            if len(similar_videos) < 3:
                continue
            
            # 计算相似视频的平均性能
            similar_avg_views = similar_videos['metric_views'].mean() if 'metric_views' in similar_videos.columns else 0
            similar_avg_revenue = similar_videos['metric_revenue'].mean() if 'metric_revenue' in similar_videos.columns else 0
            
            # 计算所有视频的平均性能
            all_avg_views = platform_data['metric_views'].mean() if 'metric_views' in platform_data.columns else 0
            all_avg_revenue = platform_data['metric_revenue'].mean() if 'metric_revenue' in platform_data.columns else 0
            
            # 计算影响百分比
            views_impact = ((similar_avg_views / all_avg_views) - 1) * 100 if all_avg_views > 0 else 0
            revenue_impact = ((similar_avg_revenue / all_avg_revenue) - 1) * 100 if all_avg_revenue > 0 else 0
            
            # 确定影响方向
            views_direction = "positive" if views_impact > 5 else ("negative" if views_impact < -5 else "neutral")
            revenue_direction = "positive" if revenue_impact > 5 else ("negative" if revenue_impact < -5 else "neutral")
            
            feature_impact[feature] = {
                "value": value,
                "views_impact": round(views_impact, 2),
                "revenue_impact": round(revenue_impact, 2),
                "views_direction": views_direction,
                "revenue_direction": revenue_direction
            }
        
        return feature_impact
    
    def generate_optimization_suggestions(self, video_data: Dict[str, Any], 
                                         target_platforms: List[str] = None,
                                         optimization_level: str = "standard") -> Dict[str, Any]:
        """
        生成优化建议
        
        Args:
            video_data: 视频数据，包括内容特征和性能指标
            target_platforms: 目标平台列表，如果为None则使用视频数据中的平台
            optimization_level: 优化级别，可选值: 'basic', 'standard', 'advanced', 'professional'
            
        Returns:
            优化建议
        """
        logger.info(f"生成优化建议: {video_data.get('video_id', 'unknown')}")
        
        # 提取基本信息
        video_id = video_data.get('video_id')
        platform = video_data.get('platform')
        
        if not video_id:
            logger.error("缺少视频ID信息")
            return {
                "status": "error",
                "message": "缺少视频ID信息"
            }
        
        # 确定目标平台
        if not target_platforms:
            if platform:
                target_platforms = [platform]
            else:
                logger.error("未指定目标平台，且视频数据中没有平台信息")
                return {
                    "status": "error",
                    "message": "未指定目标平台，且视频数据中没有平台信息"
                }
        
        # 提取内容特征
        content_features = video_data.get('content_features', {})
        
        # 获取优化配置
        optimization_config = self.optimization_config
        
        # 获取优化级别配置
        level_config = optimization_config.get('optimization_levels', {}).get(optimization_level, {})
        
        if not level_config:
            logger.error(f"未找到优化级别 {optimization_level} 的配置")
            return {
                "status": "error",
                "message": f"未找到优化级别 {optimization_level} 的配置"
            }
        
        # 要优化的特征
        features_to_optimize = level_config.get('features', [])
        
        # 优化强度
        intensity = level_config.get('intensity', 0.5)
        
        # 为每个目标平台生成优化建议
        platform_suggestions = {}
        
        for target_platform in target_platforms:
            # 生成平台特定的优化建议
            platform_suggestion = self._generate_platform_suggestions(
                target_platform, content_features, features_to_optimize, intensity, optimization_config
            )
            
            platform_suggestions[target_platform] = platform_suggestion
        
        return {
            "status": "success",
            "video_id": video_id,
            "optimization_level": optimization_level,
            "target_platforms": target_platforms,
            "platform_suggestions": platform_suggestions
        }
    
    def _generate_platform_suggestions(self, platform: str, 
                                      content_features: Dict[str, Any], 
                                      features_to_optimize: List[str],
                                      intensity: float,
                                      optimization_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成平台特定的优化建议
        
        Args:
            platform: 平台名称
            content_features: 内容特征
            features_to_optimize: 要优化的特征列表
            intensity: 优化强度
            optimization_config: 优化配置
            
        Returns:
            平台特定的优化建议
        """
        # 获取平台特定配置
        platform_config = optimization_config.get('platform_specific', {}).get(platform, {})
        
        # 内容特征配置
        content_features_config = optimization_config.get('content_features', {})
        
        # 优化建议
        suggestions = {}
        
        # 处理每个要优化的特征
        for feature in features_to_optimize:
            # 获取特征配置
            feature_config = content_features_config.get(feature, {})
            
            if not feature_config:
                continue
            
            # 获取特征当前值
            current_value = content_features.get(feature)
            
            # 如果没有当前值，跳过
            if current_value is None:
                continue
            
            # 根据特征类型生成建议
            if feature == 'duration':
                suggestion = self._optimize_duration(platform, current_value, feature_config, intensity)
                if suggestion:
                    suggestions[feature] = suggestion
            
            elif feature == 'pace':
                suggestion = self._optimize_pace(platform, current_value, feature_config, intensity)
                if suggestion:
                    suggestions[feature] = suggestion
            
            elif feature == 'scene_duration':
                suggestion = self._optimize_scene_duration(platform, current_value, feature_config, intensity)
                if suggestion:
                    suggestions[feature] = suggestion
            
            elif feature == 'audio_quality':
                suggestion = self._optimize_audio_quality(platform, content_features, feature_config, intensity)
                if suggestion:
                    suggestions[feature] = suggestion
            
            elif feature == 'visual_quality':
                suggestion = self._optimize_visual_quality(platform, content_features, feature_config, intensity)
                if suggestion:
                    suggestions[feature] = suggestion
            
            elif feature == 'engagement_elements':
                suggestion = self._optimize_engagement_elements(platform, content_features, feature_config, intensity)
                if suggestion:
                    suggestions[feature] = suggestion
            
            elif feature == 'narrative_structure':
                suggestion = self._optimize_narrative_structure(platform, content_features, feature_config, intensity)
                if suggestion:
                    suggestions[feature] = suggestion
        
        # 添加平台特定建议
        platform_specific = {}
        
        if platform_config:
            platform_specific = {
                "aspect_ratio": platform_config.get('aspect_ratio'),
                "resolution": platform_config.get('resolution'),
                "preferred_transitions": platform_config.get('preferred_transitions'),
                "preferred_effects": platform_config.get('preferred_effects'),
                "music_importance": platform_config.get('music_importance')
            }
        
        # 构建最终建议
        return {
            "content_suggestions": suggestions,
            "platform_specific": platform_specific
        }
    
    def _optimize_duration(self, platform: str, current_value: Any, 
                          feature_config: Dict[str, Any], intensity: float) -> Dict[str, Any]:
        """
        优化视频时长
        
        Args:
            platform: 平台名称
            current_value: 当前值
            feature_config: 特征配置
            intensity: 优化强度
            
        Returns:
            优化建议
        """
        # 获取平台特定的最佳范围
        optimal_ranges = feature_config.get('optimal_ranges', {}).get(platform, {})
        
        if not optimal_ranges:
            return None
        
        # 提取最佳范围
        min_value = optimal_ranges.get('min')
        max_value = optimal_ranges.get('max')
        ideal_value = optimal_ranges.get('ideal')
        
        if min_value is None or max_value is None or ideal_value is None:
            return None
        
        # 尝试将当前值转换为数值
        try:
            current_value = float(current_value)
        except (ValueError, TypeError):
            return None
        
        # 判断当前值是否在最佳范围内
        if min_value <= current_value <= max_value:
            # 如果在范围内，但不是理想值，可以给出轻微调整建议
            if abs(current_value - ideal_value) > (max_value - min_value) * 0.1:
                direction = "增加" if current_value < ideal_value else "减少"
                adjustment = abs(ideal_value - current_value) * intensity
                
                return {
                    "current_value": current_value,
                    "optimal_range": {"min": min_value, "max": max_value, "ideal": ideal_value},
                    "suggestion": f"视频时长在可接受范围内，但可以{direction}约 {round(adjustment, 1)} 秒以达到最佳效果",
                    "adjustment": adjustment if direction == "增加" else -adjustment,
                    "priority": "low"
                }
            else:
                return {
                    "current_value": current_value,
                    "optimal_range": {"min": min_value, "max": max_value, "ideal": ideal_value},
                    "suggestion": "视频时长已接近最佳值，无需调整",
                    "adjustment": 0,
                    "priority": "none"
                }
        else:
            # 如果不在范围内，需要给出明确的调整建议
            if current_value < min_value:
                adjustment = (min_value - current_value) * intensity
                
                return {
                    "current_value": current_value,
                    "optimal_range": {"min": min_value, "max": max_value, "ideal": ideal_value},
                    "suggestion": f"视频时长过短，建议增加约 {round(adjustment, 1)} 秒",
                    "adjustment": adjustment,
                    "priority": "high"
                }
            else:  # current_value > max_value
                adjustment = (current_value - max_value) * intensity
                
                return {
                    "current_value": current_value,
                    "optimal_range": {"min": min_value, "max": max_value, "ideal": ideal_value},
                    "suggestion": f"视频时长过长，建议减少约 {round(adjustment, 1)} 秒",
                    "adjustment": -adjustment,
                    "priority": "high"
                }
    
    def _optimize_pace(self, platform: str, current_value: Any, 
                      feature_config: Dict[str, Any], intensity: float) -> Dict[str, Any]:
        """
        优化视频节奏
        
        Args:
            platform: 平台名称
            current_value: 当前值
            feature_config: 特征配置
            intensity: 优化强度
            
        Returns:
            优化建议
        """
        # 获取平台特定的最佳范围
        optimal_ranges = feature_config.get('optimal_ranges', {}).get(platform, {})
        
        if not optimal_ranges:
            return None
        
        # 提取最佳范围
        min_value = optimal_ranges.get('min')
        max_value = optimal_ranges.get('max')
        ideal_value = optimal_ranges.get('ideal')
        
        if min_value is None or max_value is None or ideal_value is None:
            return None
        
        # 尝试将当前值转换为数值
        try:
            current_value = float(current_value)
        except (ValueError, TypeError):
            return None
        
        # 判断当前值是否在最佳范围内
        if min_value <= current_value <= max_value:
            # 如果在范围内，但不是理想值，可以给出轻微调整建议
            if abs(current_value - ideal_value) > (max_value - min_value) * 0.1:
                direction = "增加" if current_value < ideal_value else "减少"
                adjustment = abs(ideal_value - current_value) * intensity
                
                suggestion_text = f"视频节奏在可接受范围内，但可以"
                if direction == "增加":
                    suggestion_text += "适当加快节奏"
                else:
                    suggestion_text += "适当放慢节奏"
                
                return {
                    "current_value": current_value,
                    "optimal_range": {"min": min_value, "max": max_value, "ideal": ideal_value},
                    "suggestion": suggestion_text,
                    "adjustment": adjustment if direction == "增加" else -adjustment,
                    "priority": "low"
                }
            else:
                return {
                    "current_value": current_value,
                    "optimal_range": {"min": min_value, "max": max_value, "ideal": ideal_value},
                    "suggestion": "视频节奏已接近最佳值，无需调整",
                    "adjustment": 0,
                    "priority": "none"
                }
        else:
            # 如果不在范围内，需要给出明确的调整建议
            if current_value < min_value:
                adjustment = (min_value - current_value) * intensity
                
                return {
                    "current_value": current_value,
                    "optimal_range": {"min": min_value, "max": max_value, "ideal": ideal_value},
                    "suggestion": "视频节奏过慢，建议加快剪辑节奏，减少静态画面时长",
                    "adjustment": adjustment,
                    "priority": "high"
                }
            else:  # current_value > max_value
                adjustment = (current_value - max_value) * intensity
                
                return {
                    "current_value": current_value,
                    "optimal_range": {"min": min_value, "max": max_value, "ideal": ideal_value},
                    "suggestion": "视频节奏过快，建议放慢剪辑节奏，增加关键内容的停留时间",
                    "adjustment": -adjustment,
                    "priority": "high"
                }
    
    def _optimize_scene_duration(self, platform: str, current_value: Any, 
                               feature_config: Dict[str, Any], intensity: float) -> Dict[str, Any]:
        """
        优化场景时长
        
        Args:
            platform: 平台名称
            current_value: 当前值
            feature_config: 特征配置
            intensity: 优化强度
            
        Returns:
            优化建议
        """
        # 获取平台特定的最佳范围
        optimal_ranges = feature_config.get('optimal_ranges', {}).get(platform, {})
        
        if not optimal_ranges:
            return None
        
        # 提取最佳范围
        min_value = optimal_ranges.get('min')
        max_value = optimal_ranges.get('max')
        ideal_value = optimal_ranges.get('ideal')
        
        if min_value is None or max_value is None or ideal_value is None:
            return None
        
        # 尝试将当前值转换为数值
        try:
            current_value = float(current_value)
        except (ValueError, TypeError):
            return None
        
        # 判断当前值是否在最佳范围内
        if min_value <= current_value <= max_value:
            # 如果在范围内，但不是理想值，可以给出轻微调整建议
            if abs(current_value - ideal_value) > (max_value - min_value) * 0.1:
                direction = "增加" if current_value < ideal_value else "减少"
                adjustment = abs(ideal_value - current_value) * intensity
                
                return {
                    "current_value": current_value,
                    "optimal_range": {"min": min_value, "max": max_value, "ideal": ideal_value},
                    "suggestion": f"平均场景时长在可接受范围内，但可以{direction}约 {round(adjustment, 1)} 秒以达到最佳效果",
                    "adjustment": adjustment if direction == "增加" else -adjustment,
                    "priority": "low"
                }
            else:
                return {
                    "current_value": current_value,
                    "optimal_range": {"min": min_value, "max": max_value, "ideal": ideal_value},
                    "suggestion": "平均场景时长已接近最佳值，无需调整",
                    "adjustment": 0,
                    "priority": "none"
                }
        else:
            # 如果不在范围内，需要给出明确的调整建议
            if current_value < min_value:
                adjustment = (min_value - current_value) * intensity
                
                return {
                    "current_value": current_value,
                    "optimal_range": {"min": min_value, "max": max_value, "ideal": ideal_value},
                    "suggestion": f"场景切换过于频繁，建议增加每个场景的平均时长约 {round(adjustment, 1)} 秒",
                    "adjustment": adjustment,
                    "priority": "high"
                }
            else:  # current_value > max_value
                adjustment = (current_value - max_value) * intensity
                
                return {
                    "current_value": current_value,
                    "optimal_range": {"min": min_value, "max": max_value, "ideal": ideal_value},
                    "suggestion": f"场景持续时间过长，建议增加场景切换，减少每个场景的平均时长约 {round(adjustment, 1)} 秒",
                    "adjustment": -adjustment,
                    "priority": "high"
                }
    
    def _optimize_audio_quality(self, platform: str, content_features: Dict[str, Any], 
                              feature_config: Dict[str, Any], intensity: float) -> Dict[str, Any]:
        """
        优化音频质量
        
        Args:
            platform: 平台名称
            content_features: 内容特征
            feature_config: 特征配置
            intensity: 优化强度
            
        Returns:
            优化建议
        """
        # 获取阈值配置
        thresholds = feature_config.get('thresholds', {})
        
        if not thresholds:
            return None
        
        # 提取音频相关特征
        noise_level = content_features.get('noise_level')
        speech_clarity = content_features.get('speech_clarity')
        music_quality = content_features.get('music_quality')
        
        # 如果没有任何音频特征，返回None
        if noise_level is None and speech_clarity is None and music_quality is None:
            return None
        
        # 优化建议
        suggestions = []
        
        # 检查噪音水平
        if noise_level is not None:
            try:
                noise_level = float(noise_level)
                max_noise = thresholds.get('noise_level', {}).get('max')
                
                if max_noise is not None and noise_level > max_noise:
                    suggestions.append(f"背景噪音水平过高 ({round(noise_level, 2)})，建议使用降噪处理")
            except (ValueError, TypeError):
                pass
        
        # 检查语音清晰度
        if speech_clarity is not None:
            try:
                speech_clarity = float(speech_clarity)
                min_clarity = thresholds.get('speech_clarity', {}).get('min')
                
                if min_clarity is not None and speech_clarity < min_clarity:
                    suggestions.append(f"语音清晰度不足 ({round(speech_clarity, 2)})，建议优化录音设备或环境")
            except (ValueError, TypeError):
                pass
        
        # 检查音乐质量
        if music_quality is not None:
            try:
                music_quality = float(music_quality)
                min_quality = thresholds.get('music_quality', {}).get('min')
                
                if min_quality is not None and music_quality < min_quality:
                    suggestions.append(f"背景音乐质量不佳 ({round(music_quality, 2)})，建议使用更高质量的音乐素材")
            except (ValueError, TypeError):
                pass
        
        # 如果没有任何建议，返回None
        if not suggestions:
            return None
        
        # 构建优化建议
        return {
            "suggestions": suggestions,
            "priority": "medium" if len(suggestions) > 1 else "low"
        }
    
    def _optimize_visual_quality(self, platform: str, content_features: Dict[str, Any], 
                               feature_config: Dict[str, Any], intensity: float) -> Dict[str, Any]:
        """
        优化视觉质量
        
        Args:
            platform: 平台名称
            content_features: 内容特征
            feature_config: 特征配置
            intensity: 优化强度
            
        Returns:
            优化建议
        """
        # 获取阈值配置
        thresholds = feature_config.get('thresholds', {})
        
        if not thresholds:
            return None
        
        # 提取视觉相关特征
        brightness = content_features.get('brightness')
        contrast = content_features.get('contrast')
        saturation = content_features.get('saturation')
        sharpness = content_features.get('sharpness')
        
        # 如果没有任何视觉特征，返回None
        if brightness is None and contrast is None and saturation is None and sharpness is None:
            return None
        
        # 优化建议
        suggestions = []
        
        # 检查亮度
        if brightness is not None:
            try:
                brightness = float(brightness)
                min_brightness = thresholds.get('brightness', {}).get('min')
                max_brightness = thresholds.get('brightness', {}).get('max')
                
                if min_brightness is not None and brightness < min_brightness:
                    suggestions.append(f"视频亮度过低 ({round(brightness, 2)})，建议增加亮度")
                elif max_brightness is not None and brightness > max_brightness:
                    suggestions.append(f"视频亮度过高 ({round(brightness, 2)})，建议降低亮度")
            except (ValueError, TypeError):
                pass
        
        # 检查对比度
        if contrast is not None:
            try:
                contrast = float(contrast)
                min_contrast = thresholds.get('contrast', {}).get('min')
                max_contrast = thresholds.get('contrast', {}).get('max')
                
                if min_contrast is not None and contrast < min_contrast:
                    suggestions.append(f"视频对比度过低 ({round(contrast, 2)})，建议增加对比度")
                elif max_contrast is not None and contrast > max_contrast:
                    suggestions.append(f"视频对比度过高 ({round(contrast, 2)})，建议降低对比度")
            except (ValueError, TypeError):
                pass
        
        # 检查饱和度
        if saturation is not None:
            try:
                saturation = float(saturation)
                min_saturation = thresholds.get('saturation', {}).get('min')
                max_saturation = thresholds.get('saturation', {}).get('max')
                
                if min_saturation is not None and saturation < min_saturation:
                    suggestions.append(f"视频饱和度过低 ({round(saturation, 2)})，建议增加饱和度")
                elif max_saturation is not None and saturation > max_saturation:
                    suggestions.append(f"视频饱和度过高 ({round(saturation, 2)})，建议降低饱和度")
            except (ValueError, TypeError):
                pass
        
        # 检查锐度
        if sharpness is not None:
            try:
                sharpness = float(sharpness)
                min_sharpness = thresholds.get('sharpness', {}).get('min')
                
                if min_sharpness is not None and sharpness < min_sharpness:
                    suggestions.append(f"视频锐度不足 ({round(sharpness, 2)})，建议增加锐度")
            except (ValueError, TypeError):
                pass
        
        # 如果没有任何建议，返回None
        if not suggestions:
            return None
        
        # 构建优化建议
        return {
            "suggestions": suggestions,
            "priority": "medium" if len(suggestions) > 1 else "low"
        }
    
    def _optimize_engagement_elements(self, platform: str, content_features: Dict[str, Any], 
                                    feature_config: Dict[str, Any], intensity: float) -> Dict[str, Any]:
        """
        优化互动元素
        
        Args:
            platform: 平台名称
            content_features: 内容特征
            feature_config: 特征配置
            intensity: 优化强度
            
        Returns:
            优化建议
        """
        # 获取元素配置
        elements = feature_config.get('elements', {})
        
        if not elements:
            return None
        
        # 优化建议
        suggestions = []
        
        # 检查每个互动元素
        for element_name, element_config in elements.items():
            # 获取元素频率
            element_frequency = content_features.get(f"{element_name}_frequency")
            
            if element_frequency is None:
                continue
            
            try:
                element_frequency = float(element_frequency)
                optimal_frequency = element_config.get('optimal_frequency')
                
                if optimal_frequency is None:
                    continue
                
                # 计算差异
                difference = abs(element_frequency - optimal_frequency)
                
                # 如果差异超过阈值，给出建议
                if difference > 0.1:
                    direction = "增加" if element_frequency < optimal_frequency else "减少"
                    
                    element_display_names = {
                        "face_close_up": "人脸特写",
                        "action_scenes": "动作场景",
                        "text_overlays": "文字叠加",
                        "emotional_moments": "情感瞬间"
                    }
                    
                    element_display = element_display_names.get(element_name, element_name)
                    
                    suggestions.append(f"建议{direction}{element_display}的使用频率")
            except (ValueError, TypeError):
                continue
        
        # 如果没有任何建议，返回None
        if not suggestions:
            return None
        
        # 构建优化建议
        return {
            "suggestions": suggestions,
            "priority": "medium"
        }
    
    def _optimize_narrative_structure(self, platform: str, content_features: Dict[str, Any], 
                                     feature_config: Dict[str, Any], intensity: float) -> Dict[str, Any]:
        """
        优化叙事结构
        
        Args:
            platform: 平台名称
            content_features: 内容特征
            feature_config: 特征配置
            intensity: 优化强度
            
        Returns:
            优化建议
        """
        # 获取元素配置
        elements = feature_config.get('elements', {})
        
        if not elements:
            return None
        
        # 优化建议
        suggestions = []
        
        # 检查每个叙事元素
        for element_name, element_config in elements.items():
            # 获取元素时长比例
            element_ratio = content_features.get(f"{element_name}_ratio")
            
            if element_ratio is None:
                continue
            
            try:
                element_ratio = float(element_ratio)
                optimal_ratio = element_config.get('duration_ratio')
                
                if optimal_ratio is None:
                    continue
                
                # 计算差异
                difference = abs(element_ratio - optimal_ratio)
                
                # 如果差异超过阈值，给出建议
                if difference > 0.05:
                    direction = "增加" if element_ratio < optimal_ratio else "减少"
                    
                    element_display_names = {
                        "hook": "开场钩子",
                        "buildup": "内容铺垫",
                        "climax": "高潮部分",
                        "conclusion": "结尾总结"
                    }
                    
                    element_display = element_display_names.get(element_name, element_name)
                    
                    suggestions.append(f"建议{direction}{element_display}的时长比例")
            except (ValueError, TypeError):
                continue
        
        # 如果没有任何建议，返回None
        if not suggestions:
            return None
        
        # 构建优化建议
        return {
            "suggestions": suggestions,
            "priority": "high"
        }
    
    def apply_optimization(self, video_data: Dict[str, Any], 
                          optimization_suggestions: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用优化建议
        
        Args:
            video_data: 视频数据
            optimization_suggestions: 优化建议
            
        Returns:
            优化后的视频数据
        """
        logger.info(f"应用优化建议: {video_data.get('video_id', 'unknown')}")
        
        # 提取基本信息
        video_id = video_data.get('video_id')
        
        if not video_id:
            logger.error("缺少视频ID信息")
            return {
                "status": "error",
                "message": "缺少视频ID信息"
            }
        
        # 提取平台建议
        platform_suggestions = optimization_suggestions.get('platform_suggestions', {})
        
        if not platform_suggestions:
            logger.error("没有平台优化建议")
            return {
                "status": "error",
                "message": "没有平台优化建议"
            }
        
        # 应用优化建议
        optimized_data = {}
        
        for platform, suggestions in platform_suggestions.items():
            # 提取内容建议
            content_suggestions = suggestions.get('content_suggestions', {})
            
            # 提取平台特定建议
            platform_specific = suggestions.get('platform_specific', {})
            
            # 应用内容建议
            optimized_content = self._apply_content_suggestions(video_data, content_suggestions)
            
            # 应用平台特定建议
            optimized_platform = self._apply_platform_specific(video_data, platform_specific)
            
            optimized_data[platform] = {
                "optimized_content": optimized_content,
                "platform_specific": optimized_platform
            }
        
        return {
            "status": "success",
            "video_id": video_id,
            "optimized_data": optimized_data
        }
    
    def _apply_content_suggestions(self, video_data: Dict[str, Any], 
                                  content_suggestions: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用内容优化建议
        
        Args:
            video_data: 视频数据
            content_suggestions: 内容优化建议
            
        Returns:
            优化后的内容数据
        """
        # 提取内容特征
        content_features = video_data.get('content_features', {}).copy()
        
        # 应用每个特征的优化建议
        optimized_features = content_features.copy()
        applied_suggestions = []
        
        for feature, suggestion in content_suggestions.items():
            if feature == 'duration':
                # 应用时长优化
                if 'adjustment' in suggestion:
                    current_value = content_features.get('duration', 0)
                    
                    try:
                        current_value = float(current_value)
                        adjustment = suggestion.get('adjustment', 0)
                        
                        optimized_features['duration'] = max(0, current_value + adjustment)
                        applied_suggestions.append(f"调整视频时长: {current_value} -> {optimized_features['duration']}")
                    except (ValueError, TypeError):
                        pass
            
            elif feature == 'pace':
                # 应用节奏优化
                if 'adjustment' in suggestion:
                    current_value = content_features.get('pace', 0)
                    
                    try:
                        current_value = float(current_value)
                        adjustment = suggestion.get('adjustment', 0)
                        
                        optimized_features['pace'] = max(0.1, current_value + adjustment)
                        applied_suggestions.append(f"调整视频节奏: {current_value} -> {optimized_features['pace']}")
                    except (ValueError, TypeError):
                        pass
            
            elif feature == 'scene_duration':
                # 应用场景时长优化
                if 'adjustment' in suggestion:
                    current_value = content_features.get('scene_duration', 0)
                    
                    try:
                        current_value = float(current_value)
                        adjustment = suggestion.get('adjustment', 0)
                        
                        optimized_features['scene_duration'] = max(0.5, current_value + adjustment)
                        applied_suggestions.append(f"调整场景时长: {current_value} -> {optimized_features['scene_duration']}")
                    except (ValueError, TypeError):
                        pass
            
            elif feature in ['audio_quality', 'visual_quality', 'engagement_elements', 'narrative_structure']:
                # 这些特征需要更复杂的处理，这里只记录建议
                if 'suggestions' in suggestion:
                    for sub_suggestion in suggestion['suggestions']:
                        applied_suggestions.append(sub_suggestion)
        
        return {
            "optimized_features": optimized_features,
            "applied_suggestions": applied_suggestions
        }
    
    def _apply_platform_specific(self, video_data: Dict[str, Any], 
                               platform_specific: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用平台特定优化建议
        
        Args:
            video_data: 视频数据
            platform_specific: 平台特定优化建议
            
        Returns:
            优化后的平台特定数据
        """
        # 提取视频元数据
        metadata = video_data.get('metadata', {}).copy()
        
        # 应用平台特定建议
        optimized_metadata = metadata.copy()
        applied_suggestions = []
        
        # 应用分辨率和宽高比
        aspect_ratio = platform_specific.get('aspect_ratio')
        resolution = platform_specific.get('resolution')
        
        if aspect_ratio:
            optimized_metadata['aspect_ratio'] = aspect_ratio
            applied_suggestions.append(f"设置宽高比: {aspect_ratio}")
        
        if resolution:
            optimized_metadata['resolution'] = resolution
            applied_suggestions.append(f"设置分辨率: {resolution}")
        
        # 应用转场效果
        preferred_transitions = platform_specific.get('preferred_transitions')
        
        if preferred_transitions:
            optimized_metadata['transitions'] = preferred_transitions
            applied_suggestions.append(f"使用推荐转场效果: {', '.join(preferred_transitions)}")
        
        # 应用视频效果
        preferred_effects = platform_specific.get('preferred_effects')
        
        if preferred_effects:
            optimized_metadata['effects'] = preferred_effects
            applied_suggestions.append(f"使用推荐视频效果: {', '.join(preferred_effects)}")
        
        # 应用音乐重要性
        music_importance = platform_specific.get('music_importance')
        
        if music_importance is not None:
            optimized_metadata['music_volume'] = music_importance
            applied_suggestions.append(f"设置音乐音量: {music_importance}")
        
        return {
            "optimized_metadata": optimized_metadata,
            "applied_suggestions": applied_suggestions
        }