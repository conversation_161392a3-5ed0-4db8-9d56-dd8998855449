import unittest

from platform_adapter import PlatformAdapter

class TestPlatformAdapter(unittest.TestCase):
    def setUp(self):
        self.adapter = PlatformAdapter()

    def test_adapt_for_platform(self):
        video_path = "original_video.mp4"

        # 测试TikTok平台适配
        adapted_tiktok_path = self.adapter.adapt_for_platform(video_path, "tiktok")
        self.assertIsNotNone(adapted_tiktok_path)
        self.assertTrue("tiktok_adapted" in adapted_tiktok_path)

        # 测试快手平台适配
        adapted_kuaishou_path = self.adapter.adapt_for_platform(video_path, "kuaishou")
        self.assertIsNotNone(adapted_kuaishou_path)
        self.assertTrue("kuaishou_adapted" in adapted_kuaishou_path)

        # 测试Bilibili平台适配
        adapted_bilibili_path = self.adapter.adapt_for_platform(video_path, "bilibili")
        self.assertIsNotNone(adapted_bilibili_path)
        self.assertTrue("bilibili_adapted" in adapted_bilibili_path)

        # 测试不支持的平台
        adapted_unsupported_path = self.adapter.adapt_for_platform(video_path, "youtube")
        self.assertIsNone(adapted_unsupported_path)

    def test_generate_metadata(self):
        video_info = {"title": "我的精彩视频", "description": "这是一个关于旅行的视频。", "tags": ["旅行", "风景"]}

        # 测试TikTok元数据生成
        tiktok_metadata = self.adapter.generate_metadata(video_info, "tiktok")
        self.assertIn("title", tiktok_metadata)
        self.assertTrue("#TikTok" in tiktok_metadata["title"])
        self.assertIn("tags", tiktok_metadata)
        self.assertTrue("tiktok_video" in tiktok_metadata["tags"])

        # 测试快手元数据生成
        kuaishou_metadata = self.adapter.generate_metadata(video_info, "kuaishou")
        self.assertIn("caption", kuaishou_metadata)
        self.assertTrue("#快手" in kuaishou_metadata["caption"])
        self.assertIn("topics", kuaishou_metadata)
        self.assertTrue("kuaishou_creation" in kuaishou_metadata["topics"])

        # 测试Bilibili元数据生成
        bilibili_metadata = self.adapter.generate_metadata(video_info, "bilibili")
        self.assertIn("title", bilibili_metadata)
        self.assertTrue("- B站" in bilibili_metadata["title"])
        self.assertIn("description", bilibili_metadata)
        self.assertTrue("更多精彩内容请关注！" in bilibili_metadata["description"])
        self.assertIn("tags", bilibili_metadata)
        self.assertTrue("bilibili_upload" in bilibili_metadata["tags"])

        # 测试不支持的平台元数据生成
        unsupported_metadata = self.adapter.generate_metadata(video_info, "youtube")
        self.assertEqual(unsupported_metadata, {})

if __name__ == "__main__":
    unittest.main()
