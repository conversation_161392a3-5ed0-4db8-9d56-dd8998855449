# backend.agent.market_analyzer.market_analyzer

import json
import logging
import os
import random
import time
from collections import Counter
from datetime import datetime, timedelta
from typing import Any, Dict, List

import pandas as pd

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


class MarketAnalyzer:
    """
    市场分析器：分析各平台热门内容、收益趋势，提供内容策略建议
    """

    def __init__(self, config_dir: str = None, data_dir: str = None):
        """
        初始化市场分析器

        Args:
            config_dir: 配置文件目录，默认为当前目录下的 'config'
            data_dir: 数据存储目录，默认为当前目录下的 'data/market'
        """
        self.config_dir = config_dir or os.path.join(os.getcwd(), "config")
        self.data_dir = data_dir or os.path.join(os.getcwd(), "data", "market")

        # 确保目录存在
        os.makedirs(self.config_dir, exist_ok=True)
        os.makedirs(self.data_dir, exist_ok=True)

        # 支持的平台
        self.supported_platforms = {
            "douyin": "抖音",
            "kuaishou": "快手",
            "bilibili": "B站",
            "youtube": "YouTube",
            "weibo": "微博",
            "xiaohongshu": "小红书",
            "wechat": "微信视频号",
        }

        # 平台API配置
        self.platform_apis = self._load_platform_apis()

        # 加载历史数据
        self.trend_data = self._load_trend_data()
        self.revenue_data = self._load_revenue_data()

        logger.info(f"MarketAnalyzer 初始化完成。支持的平台: {', '.join(self.supported_platforms.keys())}")

    def _load_platform_apis(self) -> Dict[str, Dict[str, Any]]:
        """
        加载平台API配置

        Returns:
            平台API配置字典
        """
        api_config_path = os.path.join(self.config_dir, "platform_apis.json")

        if os.path.exists(api_config_path):
            try:
                with open(api_config_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                logger.error("加载平台API配置失败: {e}")
                return self._get_default_platform_apis()
        else:
            logger.info("平台API配置文件不存在，使用默认配置")
            default_apis = self._get_default_platform_apis()

            # 保存默认配置
            try:
                with open(api_config_path, "w", encoding="utf-8") as f:
                    json.dump(default_apis, f, ensure_ascii=False, indent=2)
            except Exception as e:
                logger.error("保存默认平台API配置失败: {e}")

            return default_apis

    def _get_default_platform_apis(self) -> Dict[str, Dict[str, Any]]:
        """
        获取默认平台API配置

        Returns:
            默认平台API配置字典
        """
        return {
            "douyin": {
                "trending_api": "https://api.example.com/douyin/trending",
                "revenue_api": "https://api.example.com/douyin/revenue",
                "category_api": "https://api.example.com/douyin/categories",
                "auth_required": True,
                "rate_limit": 100,  # 每小时请求次数限制
                "data_fields": [
                    "title",
                    "author",
                    "play_count",
                    "like_count",
                    "comment_count",
                    "share_count",
                    "estimated_revenue",
                ],
            },
            "kuaishou": {
                "trending_api": "https://api.example.com/kuaishou/trending",
                "revenue_api": "https://api.example.com/kuaishou/revenue",
                "category_api": "https://api.example.com/kuaishou/categories",
                "auth_required": True,
                "rate_limit": 100,
                "data_fields": [
                    "title",
                    "author",
                    "play_count",
                    "like_count",
                    "comment_count",
                    "share_count",
                    "estimated_revenue",
                ],
            },
            "bilibili": {
                "trending_api": "https://api.example.com/bilibili/trending",
                "revenue_api": "https://api.example.com/bilibili/revenue",
                "category_api": "https://api.example.com/bilibili/categories",
                "auth_required": True,
                "rate_limit": 100,
                "data_fields": [
                    "title",
                    "author",
                    "play_count",
                    "like_count",
                    "coin_count",
                    "favorite_count",
                    "share_count",
                    "estimated_revenue",
                ],
            },
        }

    def _load_trend_data(self) -> Dict[str, pd.DataFrame]:
        """
        加载趋势数据

        Returns:
            平台趋势数据字典，键为平台名称，值为DataFrame
        """
        trend_data = {}

        for platform in self.supported_platforms:
            trend_file = os.path.join(self.data_dir, "{platform}_trends.csv")

            if os.path.exists(trend_file):
                try:
                    df = pd.read_csv(trend_file)
                    trend_data[platform] = df
                    logger.info("已加载 {platform} 平台趋势数据: {len(df)} 条记录")
                except Exception as e:
                    logger.error("加载 {platform} 平台趋势数据失败: {e}")
                    trend_data[platform] = pd.DataFrame()
            else:
                logger.info("{platform} 平台趋势数据文件不存在")
                trend_data[platform] = pd.DataFrame()

        return trend_data

    def _load_revenue_data(self) -> Dict[str, pd.DataFrame]:
        """
        加载收益数据

        Returns:
            平台收益数据字典，键为平台名称，值为DataFrame
        """
        revenue_data = {}

        for platform in self.supported_platforms:
            revenue_file = os.path.join(self.data_dir, "{platform}_revenue.csv")

            if os.path.exists(revenue_file):
                try:
                    df = pd.read_csv(revenue_file)
                    revenue_data[platform] = df
                    logger.info("已加载 {platform} 平台收益数据: {len(df)} 条记录")
                except Exception as e:
                    logger.error("加载 {platform} 平台收益数据失败: {e}")
                    revenue_data[platform] = pd.DataFrame()
            else:
                logger.info("{platform} 平台收益数据文件不存在")
                revenue_data[platform] = pd.DataFrame()

        return revenue_data

    def fetch_trending_content(
        self, platform: str, category: str = None, limit: int = 50, time_range: str = "day"
    ) -> Dict[str, Any]:
        """
        获取平台热门内容

        Args:
            platform: 平台名称
            category: 内容分类，如果为None则获取所有分类
            limit: 返回结果数量限制
            time_range: 时间范围，可选值: 'day', 'week', 'month'

        Returns:
            热门内容数据
        """
        logger.info("获取 {platform} 平台热门内容: category={category}, limit={limit}, time_range={time_range}")

        # 检查平台是否支持
        if platform not in self.supported_platforms:
            logger.error(f"不支持的平台: {platform}")
            return {"status": "error", "message": "不支持的平台: {platform}"}

        # 检查是否有API配置
        if platform not in self.platform_apis:
            logger.error(f"平台 {platform} 没有API配置")
            return {"status": "error", "message": f"平台 {platform} 没有API配置"}

        # 实际应用中，这里会调用平台API获取真实数据
        # 这里使用模拟数据进行演示
        trending_data = self._simulate_trending_data(platform, category, limit, time_range)

        # 保存数据
        self._save_trending_data(platform, trending_data)

        return {
            "status": "success",
            "platform": platform,
            "category": category,
            "time_range": time_range,
            "count": len(trending_data),
            "data": trending_data,
        }

    def _simulate_trending_data(
        self, platform: str, category: str = None, limit: int = 50, time_range: str = "day"
    ) -> List[Dict[str, Any]]:
        """
        模拟热门内容数据

        Args:
            platform: 平台名称
            category: 内容分类
            limit: 返回结果数量限制
            time_range: 时间范围

        Returns:
            模拟的热门内容数据列表
        """
        # 不同平台的内容类型
        content_types = {
            "douyin": ["舞蹈", "搞笑", "美食", "旅行", "游戏", "知识", "美妆", "宠物", "音乐", "运动"],
            "kuaishou": ["搞笑", "美食", "生活", "游戏", "音乐", "舞蹈", "宠物", "汽车", "时尚", "教育"],
            "bilibili": ["游戏", "动画", "音乐", "舞蹈", "知识", "美食", "生活", "时尚", "科技", "电影"],
        }

        # 使用平台特定的内容类型，如果没有则使用通用类型
        types = content_types.get(platform, ["娱乐", "知识", "生活", "游戏", "音乐"])

        # 如果指定了分类，则只使用该分类
        if category and category in types:
            types = [category]

        # 生成模拟数据
        trending_data = []

        for i in range(limit):
            content_type = random.choice(types)

            # 根据平台生成不同的数据结构
            if platform == "douyin":
                item = {
                    "id": "dy_{int(time.time())}_{random.randint(1000, 9999)}",
                    "title": "{content_type}视频 #{i+1}",
                    "author": "抖音用户_{random.randint(10000, 99999)}",
                    "type": content_type,
                    "duration": random.randint(15, 180),  # 15秒到3分钟
                    "play_count": random.randint(10000, 10000000),
                    "like_count": random.randint(1000, 1000000),
                    "comment_count": random.randint(100, 100000),
                    "share_count": random.randint(10, 10000),
                    "publish_time": (datetime.now() - timedelta(days=random.randint(1, 7))).strftime(
                        "%Y-%m-%d %H:%M:%S"
                    ),
                    "tags": ["#{content_type}", "#热门", "#抖音{content_type}"],
                    "estimated_revenue": round(random.uniform(10, 5000), 2),
                    "url": "https://douyin.com/video/{random.randint(10000000, 99999999)}",
                }
            elif platform == "kuaishou":
                item = {
                    "id": "ks_{int(time.time())}_{random.randint(1000, 9999)}",
                    "title": "{content_type}视频 #{i+1}",
                    "author": "快手用户_{random.randint(10000, 99999)}",
                    "type": content_type,
                    "duration": random.randint(15, 180),
                    "play_count": random.randint(10000, 8000000),
                    "like_count": random.randint(1000, 800000),
                    "comment_count": random.randint(100, 80000),
                    "share_count": random.randint(10, 8000),
                    "publish_time": (datetime.now() - timedelta(days=random.randint(1, 7))).strftime(
                        "%Y-%m-%d %H:%M:%S"
                    ),
                    "tags": ["#{content_type}", "#热门", "#快手{content_type}"],
                    "estimated_revenue": round(random.uniform(8, 4000), 2),
                    "url": "https://kuaishou.com/video/{random.randint(10000000, 99999999)}",
                }
            elif platform == "bilibili":
                item = {
                    "id": "bl_{int(time.time())}_{random.randint(1000, 9999)}",
                    "title": "{content_type}视频 #{i+1} - B站精选",
                    "author": "B站用户_{random.randint(10000, 99999)}",
                    "type": content_type,
                    "duration": random.randint(60, 600),  # 1分钟到10分钟
                    "play_count": random.randint(10000, 5000000),
                    "like_count": random.randint(1000, 500000),
                    "coin_count": random.randint(100, 50000),
                    "favorite_count": random.randint(100, 50000),
                    "share_count": random.randint(10, 5000),
                    "publish_time": (datetime.now() - timedelta(days=random.randint(1, 7))).strftime(
                        "%Y-%m-%d %H:%M:%S"
                    ),
                    "tags": ["{content_type}", "热门", "B站{content_type}"],
                    "estimated_revenue": round(random.uniform(5, 3000), 2),
                    "url": f"https://bilibili.com/video/BV{random.choice('123456789abcdefghijklmnopqrstuvwxyz')}{random.randint(100000, 999999)}",
                }
            else:
                # 通用结构
                item = {
                    "id": "{platform}_{int(time.time())}_{random.randint(1000, 9999)}",
                    "title": "{content_type}视频 #{i+1}",
                    "author": "{platform}用户_{random.randint(10000, 99999)}",
                    "type": content_type,
                    "duration": random.randint(30, 300),
                    "play_count": random.randint(1000, 1000000),
                    "like_count": random.randint(100, 100000),
                    "comment_count": random.randint(10, 10000),
                    "share_count": random.randint(1, 1000),
                    "publish_time": (datetime.now() - timedelta(days=random.randint(1, 7))).strftime(
                        "%Y-%m-%d %H:%M:%S"
                    ),
                    "tags": ["{content_type}", "热门"],
                    "estimated_revenue": round(random.uniform(1, 1000), 2),
                    "url": "https://{platform}.com/video/{random.randint(10000000, 99999999)}",
                }

            trending_data.append(item)

        # 按播放量排序
        trending_data.sort(key=lambda x: x["play_count"], reverse=True)

        return trending_data

    def _save_trending_data(self, platform: str, data: List[Dict[str, Any]]) -> None:
        """
        保存热门内容数据

        Args:
            platform: 平台名称
            data: 热门内容数据
        """
        if not data:
            return

        # 转换为DataFrame
        df = pd.DataFrame(data)

        # 添加获取时间
        df["fetch_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 保存到CSV文件
        trend_file = os.path.join(self.data_dir, "{platform}_trends.csv")

        # 如果文件已存在，则追加数据
        if os.path.exists(trend_file) and platform in self.trend_data and not self.trend_data[platform].empty:
            # 合并数据
            combined_df = pd.concat([self.trend_data[platform], df], ignore_index=True)
            # 去重
            combined_df.drop_duplicates(subset=["id"], keep="last", inplace=True)
            # 保存
            combined_df.to_csv(trend_file, index=False)
            # 更新内存中的数据
            self.trend_data[platform] = combined_df
            logger.info("已更新 {platform} 平台趋势数据: {len(combined_df)} 条记录")
        else:
            # 直接保存
            df.to_csv(trend_file, index=False)
            # 更新内存中的数据
            self.trend_data[platform] = df
            logger.info("已保存 {platform} 平台趋势数据: {len(df)} 条记录")

    def analyze_platform_revenue(self, platforms: List[str] = None, time_range: str = "month") -> Dict[str, Any]:
        """
        分析平台收益情况

        Args:
            platforms: 平台列表，如果为None则分析所有支持的平台
            time_range: 时间范围，可选值: 'day', 'week', 'month', 'year'

        Returns:
            平台收益分析结果
        """
        logger.info("分析平台收益情况: platforms={platforms}, time_range={time_range}")

        # 如果未指定平台，则使用所有支持的平台
        if not platforms:
            platforms = list(self.supported_platforms.keys())

        # 过滤不支持的平台
        platforms = [p for p in platforms if p in self.supported_platforms]

        if not platforms:
            logger.error("没有指定有效的平台")
            return {"status": "error", "message": "没有指定有效的平台"}

        # 分析结果
        analysis_results = {}

        for platform in platforms:
            # 获取平台收益数据
            # 实际应用中，这里会调用平台API获取真实数据
            # 这里使用模拟数据进行演示
            revenue_data = self._simulate_revenue_data(platform, time_range)

            # 分析收益数据
            platform_analysis = self._analyze_platform_revenue_data(platform, revenue_data)

            analysis_results[platform] = platform_analysis

        # 比较不同平台的收益情况
        platform_comparison = self._compare_platform_revenue(analysis_results)

        return {
            "status": "success",
            "time_range": time_range,
            "platforms": platforms,
            "platform_analysis": analysis_results,
            "platform_comparison": platform_comparison,
        }

    def _simulate_revenue_data(self, platform: str, time_range: str) -> List[Dict[str, Any]]:
        """
        模拟平台收益数据

        Args:
            platform: 平台名称
            time_range: 时间范围

        Returns:
            模拟的收益数据列表
        """
        # 根据时间范围确定数据点数量
        if time_range == "day":
            points = 24  # 每小时一个数据点
        elif time_range == "week":
            points = 7  # 每天一个数据点
        elif time_range == "month":
            points = 30  # 每天一个数据点
        elif time_range == "year":
            points = 12  # 每月一个数据点
        else:
            points = 30

        # 不同平台的基础收益和波动范围
        platform_revenue_config = {
            "douyin": {"base": 100, "variance": 50},
            "kuaishou": {"base": 80, "variance": 40},
            "bilibili": {"base": 60, "variance": 30},
            "youtube": {"base": 120, "variance": 60},
            "weibo": {"base": 40, "variance": 20},
            "xiaohongshu": {"base": 50, "variance": 25},
            "wechat": {"base": 70, "variance": 35},
        }

        # 使用平台特定的配置，如果没有则使用默认值
        config = platform_revenue_config.get(platform, {"base": 50, "variance": 25})

        # 生成模拟数据
        revenue_data = []

        for i in range(points):
            # 计算时间点
            if time_range == "day":
                time_point = (datetime.now() - timedelta(hours=i)).strftime("%Y-%m-%d %H:00:00")
                time_label = (datetime.now() - timedelta(hours=i)).strftime("%H:00")
            elif time_range == "year":
                time_point = (datetime.now() - timedelta(days=i * 30)).strftime("%Y-%m-01 00:00:00")
                time_label = (datetime.now() - timedelta(days=i * 30)).strftime("%Y-%m")
            else:
                time_point = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d 00:00:00")
                time_label = (datetime.now() - timedelta(days=i)).strftime("%m-%d")

            # 生成收益数据
            base_revenue = config["base"]
            variance = config["variance"]

            # 添加一些随机波动
            revenue = max(0, base_revenue + random.uniform(-variance, variance))

            # 添加一些趋势
            if time_range != "day":
                # 周末收益略高
                weekday = (datetime.now() - timedelta(days=i)).weekday()
                if weekday >= 5:  # 5和6是周六和周日
                    revenue *= 1.2

                # 月初和月末收益略低
                day_of_month = (datetime.now() - timedelta(days=i)).day
                if day_of_month <= 3 or day_of_month >= 28:
                    revenue *= 0.9

            # 不同平台的特殊调整
            if platform == "douyin":
                # 抖音在晚上收益更高
                if time_range == "day" and i >= 18 and i <= 23:
                    revenue *= 1.3
            elif platform == "bilibili":
                # B站在周末收益更高
                weekday = (datetime.now() - timedelta(days=i)).weekday()
                if weekday >= 5:
                    revenue *= 1.3

            item = {
                "time": time_point,
                "time_label": time_label,
                "revenue": round(revenue, 2),
                "views": int(revenue * random.uniform(80, 120)),
                "likes": int(revenue * random.uniform(8, 12)),
                "comments": int(revenue * random.uniform(0.8, 1.2)),
                "shares": int(revenue * random.uniform(0.4, 0.6)),
                "new_followers": int(revenue * random.uniform(0.1, 0.3)),
            }

            revenue_data.append(item)

        # 按时间排序
        revenue_data.sort(key=lambda x: x["time"])

        return revenue_data

    def _analyze_platform_revenue_data(self, platform: str, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        分析平台收益数据

        Args:
            platform: 平台名称
            data: 收益数据

        Returns:
            收益分析结果
        """
        if not data:
            return {"platform": platform, "status": "error", "message": "没有收益数据"}

        # 提取收益数据
        revenues = [item["revenue"] for item in data]
        views = [item["views"] for item in data]
        likes = [item["likes"] for item in data]
        comments = [item["comments"] for item in data]
        shares = [item["shares"] for item in data]
        new_followers = [item["new_followers"] for item in data]
        time_labels = [item["time_label"] for item in data]

        # 计算统计数据
        total_revenue = sum(revenues)
        avg_revenue = total_revenue / len(revenues) if revenues else 0
        max_revenue = max(revenues) if revenues else 0
        min_revenue = min(revenues) if revenues else 0

        total_views = sum(views)
        avg_views = total_views / len(views) if views else 0

        # 计算每千次播放收益 (RPM)
        rpm = (total_revenue / total_views * 1000) if total_views else 0

        # 计算趋势
        if len(revenues) > 1:
            revenue_trend = (revenues[-1] - revenues[0]) / revenues[0] if revenues[0] else 0
        else:
            revenue_trend = 0

        # 计算互动率
        engagement_rate = (sum(likes) + sum(comments) + sum(shares)) / sum(views) if sum(views) else 0

        return {
            "platform": platform,
            "platform_name": self.supported_platforms.get(platform, platform),
            "total_revenue": round(total_revenue, 2),
            "avg_revenue": round(avg_revenue, 2),
            "max_revenue": round(max_revenue, 2),
            "min_revenue": round(min_revenue, 2),
            "total_views": total_views,
            "avg_views": round(avg_views, 2),
            "rpm": round(rpm, 2),
            "revenue_trend": round(revenue_trend * 100, 2),  # 转换为百分比
            "engagement_rate": round(engagement_rate * 100, 2),  # 转换为百分比
            "time_labels": time_labels,
            "revenue_data": revenues,
            "views_data": views,
            "likes_data": likes,
            "comments_data": comments,
            "shares_data": shares,
            "new_followers_data": new_followers,
        }

    def _compare_platform_revenue(self, platform_analysis: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        比较不同平台的收益情况

        Args:
            platform_analysis: 平台收益分析结果

        Returns:
            平台比较结果
        """
        if not platform_analysis:
            return {}

        # 提取各平台的关键指标
        platforms = []
        total_revenues = []
        avg_revenues = []
        rpms = []
        engagement_rates = []
        revenue_trends = []

        for platform, analysis in platform_analysis.items():
            platforms.append(platform)
            total_revenues.append(analysis.get("total_revenue", 0))
            avg_revenues.append(analysis.get("avg_revenue", 0))
            rpms.append(analysis.get("rpm", 0))
            engagement_rates.append(analysis.get("engagement_rate", 0))
            revenue_trends.append(analysis.get("revenue_trend", 0))

        # 计算排名
        total_revenue_rank = [sorted(total_revenues, reverse=True).index(x) + 1 for x in total_revenues]
        avg_revenue_rank = [sorted(avg_revenues, reverse=True).index(x) + 1 for x in avg_revenues]
        rpm_rank = [sorted(rpms, reverse=True).index(x) + 1 for x in rpms]
        engagement_rank = [sorted(engagement_rates, reverse=True).index(x) + 1 for x in engagement_rates]
        trend_rank = [sorted(revenue_trends, reverse=True).index(x) + 1 for x in revenue_trends]

        # 计算综合排名 (加权平均)
        weights = {"total_revenue": 0.3, "rpm": 0.3, "engagement": 0.2, "trend": 0.2}

        composite_scores = []

        for i in range(len(platforms)):
            score = (
                total_revenue_rank[i] * weights["total_revenue"]
                + rpm_rank[i] * weights["rpm"]
                + engagement_rank[i] * weights["engagement"]
                + trend_rank[i] * weights["trend"]
            )
            composite_scores.append(score)

        # 综合排名 (分数越低越好)
        composite_rank = [sorted(composite_scores).index(x) + 1 for x in composite_scores]

        # 构建比较结果
        comparison_result = {
            "best_overall_platform": platforms[composite_rank.index(1)],
            "best_revenue_platform": platforms[total_revenue_rank.index(1)],
            "best_rpm_platform": platforms[rpm_rank.index(1)],
            "best_engagement_platform": platforms[engagement_rank.index(1)],
            "best_growth_platform": platforms[trend_rank.index(1)],
            "platform_ranking": [],
        }

        for i in range(len(platforms)):
            platform_data = {
                "platform": platforms[i],
                "platform_name": self.supported_platforms.get(platforms[i], platforms[i]),
                "total_revenue": total_revenues[i],
                "total_revenue_rank": total_revenue_rank[i],
                "rpm": rpms[i],
                "rpm_rank": rpm_rank[i],
                "engagement_rate": engagement_rates[i],
                "engagement_rank": engagement_rank[i],
                "revenue_trend": revenue_trends[i],
                "trend_rank": trend_rank[i],
                "composite_score": composite_scores[i],
                "composite_rank": composite_rank[i],
            }
            comparison_result["platform_ranking"].append(platform_data)

        # 按综合排名排序
        comparison_result["platform_ranking"].sort(key=lambda x: x["composite_rank"])

        return comparison_result

    def analyze_content_trends(
        self, platforms: List[str] = None, categories: List[str] = None, time_range: str = "week"
    ) -> Dict[str, Any]:
        """
        分析内容趋势

        Args:
            platforms: 平台列表，如果为None则分析所有支持的平台
            categories: 内容分类列表，如果为None则分析所有分类
            time_range: 时间范围，可选值: 'day', 'week', 'month'

        Returns:
            内容趋势分析结果
        """
        logger.info("分析内容趋势: platforms={platforms}, categories={categories}, time_range={time_range}")

        # 如果未指定平台，则使用所有支持的平台
        if not platforms:
            platforms = list(self.supported_platforms.keys())

        # 过滤不支持的平台
        platforms = [p for p in platforms if p in self.supported_platforms]

        if not platforms:
            logger.error("没有指定有效的平台")
            return {"status": "error", "message": "没有指定有效的平台"}

        # 获取各平台的热门内容
        platform_trending = {}

        for platform in platforms:
            # 获取平台热门内容
            trending_result = self.fetch_trending_content(platform, None, 100, time_range)

            if trending_result.get("status") == "success":
                platform_trending[platform] = trending_result.get("data", [])

        # 分析内容趋势
        trend_analysis = self._analyze_content_trends(platform_trending, categories)

        return {
            "status": "success",
            "time_range": time_range,
            "platforms": platforms,
            "categories": categories,
            "trend_analysis": trend_analysis,
        }

    def _analyze_content_trends(
        self, platform_trending: Dict[str, List[Dict[str, Any]]], categories: List[str] = None
    ) -> Dict[str, Any]:
        """
        分析内容趋势

        Args:
            platform_trending: 各平台热门内容
            categories: 内容分类列表

        Returns:
            内容趋势分析结果
        """
        if not platform_trending:
            return {}

        # 所有平台的热门内容类型统计
        all_types = []

        for platform, trending_data in platform_trending.items():
            for item in trending_data:
                content_type = item.get("type")
                if content_type:
                    all_types.append(content_type)

        # 统计内容类型频率
        type_counter = Counter(all_types)

        # 如果指定了分类，则只保留这些分类
        if categories:
            type_counter = {k: v for k, v in type_counter.items() if k in categories}

        # 按频率排序
        sorted_types = sorted(type_counter.items(), key=lambda x: x[1], reverse=True)

        # 提取热门标签
        all_tags = []

        for platform, trending_data in platform_trending.items():
            for item in trending_data:
                tags = item.get("tags", [])
                all_tags.extend(tags)

        # 统计标签频率
        tag_counter = Counter(all_tags)

        # 按频率排序
        sorted_tags = sorted(tag_counter.items(), key=lambda x: x[1], reverse=True)[:20]  # 只取前20个

        # 分析各平台的内容类型分布
        platform_type_distribution = {}

        for platform, trending_data in platform_trending.items():
            platform_types = [item.get("type") for item in trending_data if item.get("type")]
            platform_type_counter = Counter(platform_types)

            # 如果指定了分类，则只保留这些分类
            if categories:
                platform_type_counter = {k: v for k, v in platform_type_counter.items() if k in categories}

            # 按频率排序
            platform_sorted_types = sorted(platform_type_counter.items(), key=lambda x: x[1], reverse=True)

            platform_type_distribution[platform] = {
                "type_counts": dict(platform_type_counter),
                "sorted_types": platform_sorted_types,
            }

        # 分析热门内容的特征
        avg_duration = {}
        avg_play_count = {}
        avg_like_count = {}
        avg_comment_count = {}
        avg_share_count = {}
        avg_revenue = {}

        for platform, trending_data in platform_trending.items():
            # 按内容类型分组计算平均值
            type_durations = {}
            type_play_counts = {}
            type_like_counts = {}
            type_comment_counts = {}
            type_share_counts = {}
            type_revenues = {}

            for item in trending_data:
                content_type = item.get("type")

                if not content_type or (categories and content_type not in categories):
                    continue

                if content_type not in type_durations:
                    type_durations[content_type] = []
                    type_play_counts[content_type] = []
                    type_like_counts[content_type] = []
                    type_comment_counts[content_type] = []
                    type_share_counts[content_type] = []
                    type_revenues[content_type] = []

                if "duration" in item:
                    type_durations[content_type].append(item["duration"])
                if "play_count" in item:
                    type_play_counts[content_type].append(item["play_count"])
                if "like_count" in item:
                    type_like_counts[content_type].append(item["like_count"])
                if "comment_count" in item:
                    type_comment_counts[content_type].append(item["comment_count"])
                if "share_count" in item:
                    type_share_counts[content_type].append(item["share_count"])
                if "estimated_revenue" in item:
                    type_revenues[content_type].append(item["estimated_revenue"])

            # 计算平均值
            avg_duration[platform] = {
                content_type: sum(durations) / len(durations) if durations else 0
                for content_type, durations in type_durations.items()
            }

            avg_play_count[platform] = {
                content_type: sum(counts) / len(counts) if counts else 0
                for content_type, counts in type_play_counts.items()
            }

            avg_like_count[platform] = {
                content_type: sum(counts) / len(counts) if counts else 0
                for content_type, counts in type_like_counts.items()
            }

            avg_comment_count[platform] = {
                content_type: sum(counts) / len(counts) if counts else 0
                for content_type, counts in type_comment_counts.items()
            }

            avg_share_count[platform] = {
                content_type: sum(counts) / len(counts) if counts else 0
                for content_type, counts in type_share_counts.items()
            }

            avg_revenue[platform] = {
                content_type: sum(revenues) / len(revenues) if revenues else 0
                for content_type, revenues in type_revenues.items()
            }

        # 构建分析结果
        return {
            "popular_types": sorted_types,
            "popular_tags": sorted_tags,
            "platform_type_distribution": platform_type_distribution,
            "avg_duration": avg_duration,
            "avg_play_count": avg_play_count,
            "avg_like_count": avg_like_count,
            "avg_comment_count": avg_comment_count,
            "avg_share_count": avg_share_count,
            "avg_revenue": avg_revenue,
        }

    def generate_content_strategy(
        self,
        platforms: List[str] = None,
        target_audience: Dict[str, Any] = None,
        content_preferences: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """
        生成内容策略

        Args:
            platforms: 目标平台列表，如果为None则考虑所有支持的平台
            target_audience: 目标受众信息
            content_preferences: 内容偏好设置

        Returns:
            内容策略建议
        """
        logger.info("生成内容策略: platforms={platforms}")

        # 如果未指定平台，则使用所有支持的平台
        if not platforms:
            platforms = list(self.supported_platforms.keys())

        # 过滤不支持的平台
        platforms = [p for p in platforms if p in self.supported_platforms]

        if not platforms:
            logger.error("没有指定有效的平台")
            return {"status": "error", "message": "没有指定有效的平台"}

        # 分析平台收益情况
        revenue_analysis = self.analyze_platform_revenue(platforms, "month")

        # 分析内容趋势
        trend_analysis = self.analyze_content_trends(platforms, None, "week")

        # 生成内容策略
        strategy = self._generate_strategy(
            platforms, revenue_analysis, trend_analysis, target_audience, content_preferences
        )

        return {"status": "success", "platforms": platforms, "strategy": strategy}

    def _generate_strategy(
        self,
        platforms: List[str],
        revenue_analysis: Dict[str, Any],
        trend_analysis: Dict[str, Any],
        target_audience: Dict[str, Any] = None,
        content_preferences: Dict[str, Any] = None,
    ) -> Dict[str, Any]:
        """
        生成内容策略

        Args:
            platforms: 目标平台列表
            revenue_analysis: 平台收益分析结果
            trend_analysis: 内容趋势分析结果
            target_audience: 目标受众信息
            content_preferences: 内容偏好设置

        Returns:
            内容策略建议
        """
        # 平台优先级
        platform_priority = []

        if "platform_comparison" in revenue_analysis:
            platform_ranking = revenue_analysis["platform_comparison"].get("platform_ranking", [])
            platform_priority = [item["platform"] for item in platform_ranking if item["platform"] in platforms]

        # 如果没有平台优先级，则使用指定的平台列表
        if not platform_priority:
            platform_priority = platforms

        # 内容类型建议
        content_type_recommendations = {}

        if "trend_analysis" in trend_analysis:
            trend_data = trend_analysis["trend_analysis"]

            # 热门内容类型
            popular_types = trend_data.get("popular_types", [])

            # 各平台的内容类型分布
            platform_type_distribution = trend_data.get("platform_type_distribution", {})

            # 各平台各类型的平均收益
            avg_revenue = trend_data.get("avg_revenue", {})

            for platform in platform_priority:
                # 该平台的热门内容类型
                platform_types = []

                if platform in platform_type_distribution:
                    platform_types = [t[0] for t in platform_type_distribution[platform].get("sorted_types", [])]

                # 该平台各类型的平均收益
                platform_revenue = {}

                if platform in avg_revenue:
                    platform_revenue = avg_revenue[platform]

                # 按收益排序的内容类型
                revenue_sorted_types = sorted(platform_revenue.items(), key=lambda x: x[1], reverse=True)

                # 推荐的内容类型
                recommended_types = []

                # 首先考虑高收益的内容类型
                for content_type, revenue in revenue_sorted_types[:3]:
                    if content_type not in recommended_types:
                        recommended_types.append(content_type)

                # 然后考虑热门的内容类型
                for content_type in platform_types[:3]:
                    if content_type not in recommended_types:
                        recommended_types.append(content_type)

                # 最后考虑全平台热门的内容类型
                for content_type, count in popular_types[:3]:
                    if content_type not in recommended_types:
                        recommended_types.append(content_type)

                # 限制推荐类型数量
                recommended_types = recommended_types[:5]

                # 为每种类型添加理由
                type_reasons = {}

                for content_type in recommended_types:
                    reasons = []

                    # 检查是否是高收益类型
                    for t, r in revenue_sorted_types:
                        if t == content_type:
                            reasons.append("在{platform}平台上，{content_type}类型的内容平均收益为{r:.2f}元")
                            break

                    # 检查是否是平台热门类型
                    for i, t in enumerate(platform_types):
                        if t == content_type:
                            reasons.append("在{platform}平台上，{content_type}类型的内容排名第{i+1}位")
                            break

                    # 检查是否是全平台热门类型
                    for t, c in popular_types:
                        if t == content_type:
                            reasons.append(f"在所有平台上，{content_type}类型的内容出现{c}次")
                            break

                    type_reasons[content_type] = reasons

                content_type_recommendations[platform] = {
                    "recommended_types": recommended_types,
                    "type_reasons": type_reasons,
                }

        # 内容特征建议
        content_feature_recommendations = {}

        if "trend_analysis" in trend_analysis:
            trend_data = trend_analysis["trend_analysis"]

            # 各平台各类型的平均时长
            avg_duration = trend_data.get("avg_duration", {})

            # 各平台各类型的平均互动数据
            avg_like_count = trend_data.get("avg_like_count", {})
            avg_comment_count = trend_data.get("avg_comment_count", {})
            avg_share_count = trend_data.get("avg_share_count", {})

            for platform in platform_priority:
                # 该平台推荐的内容类型
                recommended_types = content_type_recommendations.get(platform, {}).get("recommended_types", [])

                # 该平台各类型的平均时长
                platform_duration = {}

                if platform in avg_duration:
                    platform_duration = avg_duration[platform]

                # 该平台各类型的平均互动数据
                platform_likes = {}
                platform_comments = {}
                platform_shares = {}

                if platform in avg_like_count:
                    platform_likes = avg_like_count[platform]

                if platform in avg_comment_count:
                    platform_comments = avg_comment_count[platform]

                if platform in avg_share_count:
                    platform_shares = avg_share_count[platform]

                # 为每种推荐类型生成特征建议
                type_features = {}

                for content_type in recommended_types:
                    features = {}

                    # 推荐时长
                    if content_type in platform_duration:
                        duration = platform_duration[content_type]
                        features["recommended_duration"] = round(duration, 1)

                    # 互动特征
                    engagement_features = {}

                    if content_type in platform_likes:
                        engagement_features["likes"] = platform_likes[content_type]

                    if content_type in platform_comments:
                        engagement_features["comments"] = platform_comments[content_type]

                    if content_type in platform_shares:
                        engagement_features["shares"] = platform_shares[content_type]

                    if engagement_features:
                        # 计算互动比例
                        total = sum(engagement_features.values())

                        if total > 0:
                            engagement_ratio = {k: round(v / total * 100, 1) for k, v in engagement_features.items()}
                            features["engagement_ratio"] = engagement_ratio

                    type_features[content_type] = features

                content_feature_recommendations[platform] = type_features

        # 发布策略建议
        publishing_recommendations = {}

        for platform in platform_priority:
            # 根据平台特性生成发布策略
            if platform == "douyin":
                publishing_recommendations[platform] = {
                    "optimal_posting_times": ["12:00", "18:00", "21:00"],
                    "posting_frequency": "每天1-2次",
                    "title_strategy": "简短吸引人，使用热门话题标签",
                    "description_strategy": "简洁明了，包含2-3个热门标签",
                    "hashtag_strategy": "使用3-5个相关热门标签",
                }
            elif platform == "kuaishou":
                publishing_recommendations[platform] = {
                    "optimal_posting_times": ["12:00", "19:00", "21:30"],
                    "posting_frequency": "每天1-2次",
                    "title_strategy": "直接明了，突出内容亮点",
                    "description_strategy": "详细描述内容，使用方言或地域特色",
                    "hashtag_strategy": "使用2-4个相关标签，包括地域标签",
                }
            elif platform == "bilibili":
                publishing_recommendations[platform] = {
                    "optimal_posting_times": ["12:00", "17:00", "22:00"],
                    "posting_frequency": "每周2-3次",
                    "title_strategy": "详细且有吸引力，20-30字为佳",
                    "description_strategy": "详细介绍内容，添加时间轴",
                    "hashtag_strategy": "使用5-10个相关标签，包括分区标签",
                }
            else:
                publishing_recommendations[platform] = {
                    "optimal_posting_times": ["12:00", "18:00", "21:00"],
                    "posting_frequency": "每天1次",
                    "title_strategy": "简明扼要，突出内容亮点",
                    "description_strategy": "详细描述内容，添加相关链接",
                    "hashtag_strategy": "使用3-5个相关标签",
                }

        # 构建策略结果
        strategy = {
            "platform_priority": platform_priority,
            "content_type_recommendations": content_type_recommendations,
            "content_feature_recommendations": content_feature_recommendations,
            "publishing_recommendations": publishing_recommendations,
            "general_recommendations": [
                "关注热门话题和趋势，及时调整内容策略",
                "保持内容质量和一致性，建立个人品牌",
                "与观众互动，回复评论，增加粉丝黏性",
                "分析数据，了解哪些内容表现最好，持续优化",
                "跨平台发布，但根据各平台特性调整内容形式",
            ],
        }

        return strategy
