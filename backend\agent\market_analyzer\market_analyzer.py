#!/usr/bin/env python3
"""
market_analyzer module
"""

import json
import logging
import os
import random
from collections import Counter
from datetime import datetime, timedelta
from typing import Any, Dict, List

# import pandas as pd  # 暂时注释掉，避免DEFAULTPARSER错误

# 临时替代pandas的简单类
class SimpleDataFrame:
    def __init__(self, data=None):
        self.data = data or []

    def empty(self):
        return len(self.data) == 0

    def __len__(self):
        return len(self.data)

    def to_csv(self, path, index=False):
        # 简单的CSV写入
        pass

    @classmethod
    def read_csv(cls, path):
        # 简单的CSV读取
        return cls([])

# 临时替代pandas
class pd:
    DataFrame = SimpleDataFrame

    @staticmethod
    def read_csv(path):
        return SimpleDataFrame([])

    @staticmethod
    def concat(dfs, ignore_index=True):
        return SimpleDataFrame([])

"""
市场分析器：分析各平台热门内容、收益趋势，提供内容策略建议
"""
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


class MarketAnalyzer:
    """
    市场分析器：分析各平台热门内容、收益趋势，提供内容策略建议
    """

    def __init__(self, config_dir: str = None, data_dir: str = None):
        """
        初始化市场分析器
        Args:
            config_dir: 配置文件目录，默认为当前目录下的 'config'
            data_dir: 数据存储目录，默认为当前目录下的 'data/market'
        """
        self.config_dir = config_dir or os.path.join(os.getcwd(), "config")
        self.data_dir = data_dir or os.path.join(os.getcwd(), "data", "market")
        os.makedirs(self.config_dir, exist_ok=True)
        os.makedirs(self.data_dir, exist_ok=True)
        self.supported_platforms = {
            "douyin": "抖音",
            "kuaishou": "快手",
            "bilibili": "B站",
            "youtube": "YouTube",
            "weibo": "微博",
            "xiaohongshu": "小红书",
            "wechat": "微信视频号",
        }
        self.platform_apis = self._load_platform_apis()
        self.trend_data = self._load_trend_data()
        self.revenue_data = self._load_revenue_data()
        logger.info(f"MarketAnalyzer 初始化完成。支持的平台: {', '.join(self.supported_platforms.keys())}")
def _load_platform_apis(self) -> Dict[str, Dict[str, Any]]:
    api_config_path = os.path.join(self.config_dir, "platform_apis.json")
    if os.path.exists(api_config_path):
        try:
            with open(api_config_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except Exception:
            logger.error("操作失败")
            return self._get_default_platform_apis()
    else:
        logger.info("平台API配置文件不存在，使用默认配置")
        default_apis = self._get_default_platform_apis()
        try:
            with open(api_config_path, "w", encoding="utf-8") as f:
                json.dump(default_apis, f, ensure_ascii=False, indent=2)
        except Exception:
            logger.error("操作失败")
        return default_apis
def _get_default_platform_apis(self) -> Dict[str, Dict[str, Any]]:
    return {
        "douyin": {
            "trending_api": "https://api.example.com/douyin/trending",
            "revenue_api": "https://api.example.com/douyin/revenue",
            "category_api": "https://api.example.com/douyin/categories",
            "auth_required": True,
            "rate_limit": 100,  # 每小时请求次数限制
            "data_fields": [
                "title",
                "author",
                "play_count",
                "like_count",
                "comment_count",
                "share_count",
                "estimated_revenue",
            ],
        },
        "kuaishou": {
            "trending_api": "https://api.example.com/kuaishou/trending",
            "revenue_api": "https://api.example.com/kuaishou/revenue",
            "category_api": "https://api.example.com/kuaishou/categories",
            "auth_required": True,
            "rate_limit": 100,
            "data_fields": [
                "title",
                "author",
                "play_count",
                "like_count",
                "comment_count",
                "share_count",
                "estimated_revenue",
            ],
        },
        "bilibili": {
            "trending_api": "https://api.example.com/bilibili/trending",
            "revenue_api": "https://api.example.com/bilibili/revenue",
            "category_api": "https://api.example.com/bilibili/categories",
            "auth_required": True,
            "rate_limit": 100,
            "data_fields": [
                "title",
                "author",
                "play_count",
                "like_count",
                "coin_count",
                "favorite_count",
                "share_count",
                "estimated_revenue",
            ],
        },
    }
def _load_trend_data(self) -> Dict[str, pd.DataFrame]:
    trend_data = {}
    for platform in self.supported_platforms:
        trend_file = os.path.join(self.data_dir, "{platform}_trends.csv")
        if os.path.exists(trend_file):
            try:
                df = pd.read_csv(trend_file)
                trend_data[platform] = df
                logger.info("已加载 {platform} 平台趋势数据: {len(df)} 条记录")
            except Exception:
                logger.error("操作失败")
                trend_data[platform] = pd.DataFrame()
        else:
            logger.info("{platform} 平台趋势数据文件不存在")
            trend_data[platform] = pd.DataFrame()
    return trend_data
def _load_revenue_data(self) -> Dict[str, pd.DataFrame]:
    revenue_data = {}
    for platform in self.supported_platforms:
        revenue_file = os.path.join(self.data_dir, "{platform}_revenue.csv")
        if os.path.exists(revenue_file):
            try:
                df = pd.read_csv(revenue_file)
                revenue_data[platform] = df
                logger.info("已加载 {platform} 平台收益数据: {len(df)} 条记录")
            except Exception:
                logger.error("操作失败")
                revenue_data[platform] = pd.DataFrame()
        else:
            logger.info("{platform} 平台收益数据文件不存在")
            revenue_data[platform] = pd.DataFrame()
    return revenue_data
def fetch_trending_content(
    self, platform: str, category: str = None, limit: int = 50, time_range: str = "day"
) -> Dict[str, Any]:
    logger.info("获取 {platform} 平台热门内容: category={category}, limit={limit}, time_range={time_range}")
    if platform not in self.supported_platforms:
        logger.error(f"不支持的平台: {platform}")
        return {"status": "error", "message": "不支持的平台: {platform}"}
    if platform not in self.platform_apis:
        logger.error(f"平台 {platform} 没有API配置")
        return {"status": "error", "message": f"平台 {platform} 没有API配置"}
    trending_data = self._simulate_trending_data(platform, category, limit, time_range)
    self._save_trending_data(platform, trending_data)
    return {
        "status": "success",
        "platform": platform,
        "category": category,
        "time_range": time_range,
        "count": len(trending_data),
        "data": trending_data,
    }
def _simulate_trending_data(
    self, platform: str, category: str = None, limit: int = 50, time_range: str = "day"
) -> List[Dict[str, Any]]:
    content_types = {
        "douyin": ["舞蹈", "搞笑", "美食", "旅行", "游戏", "知识", "美妆", "宠物", "音乐", "运动"],
        "kuaishou": ["搞笑", "美食", "生活", "游戏", "音乐", "舞蹈", "宠物", "汽车", "时尚", "教育"],
        "bilibili": ["游戏", "动画", "音乐", "舞蹈", "知识", "美食", "生活", "时尚", "科技", "电影"],
    }
    types = content_types.get(platform, ["娱乐", "知识", "生活", "游戏", "音乐"])
    if category and category in types:
        types = [category]
    trending_data = []
    for i in range(limit):
        content_type = random.choice(types)
        if platform == "douyin":
            item = {
                "id": "dy_{int(time.time())}_{random.randint(1000, 9999)}",
                "title": "{content_type}视频 #{i+1}",
                "author": "抖音用户_{random.randint(10000, 99999)}",
                "type": content_type,
                "duration": random.randint(15, 180),  # 15秒到3分钟
                "play_count": random.randint(10000, 10000000),
                "like_count": random.randint(1000, 1000000),
                "comment_count": random.randint(100, 100000),
                "share_count": random.randint(10, 10000),
                "publish_time": (datetime.now() - timedelta(days=random.randint(1, 7))).strftime(
                    "%Y-%m-%d %H:%M:%S"
                ),
                "tags": ["#{content_type}", "#热门", "#抖音{content_type}"],
                "estimated_revenue": round(random.uniform(10, 5000), 2),
                "url": "https://douyin.com/video/{random.randint(10000000, 99999999)}",
            }
        elif platform == "kuaishou":
            item = {
                "id": "ks_{int(time.time())}_{random.randint(1000, 9999)}",
                "title": "{content_type}视频 #{i+1}",
                "author": "快手用户_{random.randint(10000, 99999)}",
                "type": content_type,
                "duration": random.randint(15, 180),
                "play_count": random.randint(10000, 8000000),
                "like_count": random.randint(1000, 800000),
                "comment_count": random.randint(100, 80000),
                "share_count": random.randint(10, 8000),
                "publish_time": (datetime.now() - timedelta(days=random.randint(1, 7))).strftime(
                    "%Y-%m-%d %H:%M:%S"
                ),
                "tags": ["#{content_type}", "#热门", "#快手{content_type}"],
                "estimated_revenue": round(random.uniform(8, 4000), 2),
                "url": "https://kuaishou.com/video/{random.randint(10000000, 99999999)}",
            }
        elif platform == "bilibili":
            item = {
                "id": "bl_{int(time.time())}_{random.randint(1000, 9999)}",
                "title": "{content_type}视频 #{i+1} - B站精选",
                "author": "B站用户_{random.randint(10000, 99999)}",
                "type": content_type,
                "duration": random.randint(60, 600),  # 1分钟到10分钟
                "play_count": random.randint(10000, 5000000),
                "like_count": random.randint(1000, 500000),
                "coin_count": random.randint(100, 50000),
                "favorite_count": random.randint(100, 50000),
                "share_count": random.randint(10, 5000),
                "publish_time": (datetime.now() - timedelta(days=random.randint(1, 7))).strftime(
                    "%Y-%m-%d %H:%M:%S"
                ),
                "tags": ["{content_type}", "热门", "B站{content_type}"],
                "estimated_revenue": round(random.uniform(5, 3000), 2),
                "url": f"https://bilibili.com/video/BV{random.choice(
                    '123456789abcdefghijklmnopqrstuvwxyz')}{random.randint(100000,
                    999999)}"
                ,
            }
        else:
            item = {
                "id": "{platform}_{int(time.time())}_{random.randint(1000, 9999)}",
                "title": "{content_type}视频 #{i+1}",
                "author": "{platform}用户_{random.randint(10000, 99999)}",
                "type": content_type,
                "duration": random.randint(30, 300),
                "play_count": random.randint(1000, 1000000),
                "like_count": random.randint(100, 100000),
                "comment_count": random.randint(10, 10000),
                "share_count": random.randint(1, 1000),
                "publish_time": (datetime.now() - timedelta(days=random.randint(1, 7))).strftime(
                    "%Y-%m-%d %H:%M:%S"
                ),
                "tags": ["{content_type}", "热门"],
                "estimated_revenue": round(random.uniform(1, 1000), 2),
                "url": "https://{platform}.com/video/{random.randint(10000000, 99999999)}",
            }
        trending_data.append(item)
    trending_data.sort(key=lambda x: x["play_count"], reverse=True)
    return trending_data
def _save_trending_data(self, platform: str, data: List[Dict[str, Any]]) -> None:
    if not data:
        return
    df = pd.DataFrame(data)
    df["fetch_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    trend_file = os.path.join(self.data_dir, "{platform}_trends.csv")
    if os.path.exists(trend_file) and platform in self.trend_data and not self.trend_data[platform].empty:
        combined_df = pd.concat([self.trend_data[platform], df], ignore_index=True)
        combined_df.drop_duplicates(subset=["id"], keep="last", inplace=True)
        combined_df.to_csv(trend_file, index=False)
        self.trend_data[platform] = combined_df
        logger.info("已更新 {platform} 平台趋势数据: {len(combined_df)} 条记录")
    else:
        df.to_csv(trend_file, index=False)
        self.trend_data[platform] = df
        logger.info("已保存 {platform} 平台趋势数据: {len(df)} 条记录")
def analyze_platform_revenue(self, platforms: List[str] = None, time_range: str = "month") -> Dict[str, Any]:
    logger.info("分析平台收益情况: platforms={platforms}, time_range={time_range}")
    if not platforms:
        platforms = list(self.supported_platforms.keys())
    platforms = [p for p in platforms if p in self.supported_platforms]
    if not platforms:
        logger.error("没有指定有效的平台")
        return {"status": "error", "message": "没有指定有效的平台"}
    analysis_results = {}
    for platform in platforms:
        revenue_data = self._simulate_revenue_data(platform, time_range)
        platform_analysis = self._analyze_platform_revenue_data(platform, revenue_data)
        analysis_results[platform] = platform_analysis
    platform_comparison = self._compare_platform_revenue(analysis_results)
    return {
        "status": "success",
        "time_range": time_range,
        "platforms": platforms,
        "platform_analysis": analysis_results,
        "platform_comparison": platform_comparison,
    }
def _simulate_revenue_data(self, platform: str, time_range: str) -> List[Dict[str, Any]]:
    if time_range == "day":
        points = 24  # 每小时一个数据点
    elif time_range == "week":
        points = 7  # 每天一个数据点
    elif time_range == "month":
        points = 30  # 每天一个数据点
    elif time_range == "year":
        points = 12  # 每月一个数据点
    else:
        points = 30
    platform_revenue_config = {
        "douyin": {"base": 100, "variance": 50},
        "kuaishou": {"base": 80, "variance": 40},
        "bilibili": {"base": 60, "variance": 30},
        "youtube": {"base": 120, "variance": 60},
        "weibo": {"base": 40, "variance": 20},
        "xiaohongshu": {"base": 50, "variance": 25},
        "wechat": {"base": 70, "variance": 35},
    }
    config = platform_revenue_config.get(platform, {"base": 50, "variance": 25})
    revenue_data = []
    for i in range(points):
        if time_range == "day":
            time_point = (datetime.now() - timedelta(hours=i)).strftime("%Y-%m-%d %H:00:00")
            time_label = (datetime.now() - timedelta(hours=i)).strftime("%H:00")
        elif time_range == "year":
            time_point = (datetime.now() - timedelta(days=i * 30)).strftime("%Y-%m-01 00:00:00")
            time_label = (datetime.now() - timedelta(days=i * 30)).strftime("%Y-%m")
        else:
            time_point = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d 00:00:00")
            time_label = (datetime.now() - timedelta(days=i)).strftime("%m-%d")
        base_revenue = config["base"]
        variance = config["variance"]
        revenue = max(0, base_revenue + random.uniform(-variance, variance))
        if time_range != "day":
            weekday = (datetime.now() - timedelta(days=i)).weekday()
            if weekday >= 5:  # 5和6是周六和周日
                revenue *= 1.2
            day_of_month = (datetime.now() - timedelta(days=i)).day
            if day_of_month <= 3 or day_of_month >= 28:
                revenue *= 0.9
        if platform == "douyin":
            if time_range == "day" and i >= 18 and i <= 23:
                revenue *= 1.3
        elif platform == "bilibili":
            weekday = (datetime.now() - timedelta(days=i)).weekday()
            if weekday >= 5:
                revenue *= 1.3
        item = {
            "time": time_point,
            "time_label": time_label,
            "revenue": round(revenue, 2),
            "views": int(revenue * random.uniform(80, 120)),
            "likes": int(revenue * random.uniform(8, 12)),
            "comments": int(revenue * random.uniform(0.8, 1.2)),
            "shares": int(revenue * random.uniform(0.4, 0.6)),
            "new_followers": int(revenue * random.uniform(0.1, 0.3)),
        }
        revenue_data.append(item)
    revenue_data.sort(key=lambda x: x["time"])
    return revenue_data
def _analyze_platform_revenue_data(self, platform: str, data: List[Dict[str, Any]]) -> Dict[str, Any]:
    if not data:
        return {"platform": platform, "status": "error", "message": "没有收益数据"}
    revenues = [item["revenue"] for item in data]
    views = [item["views"] for item in data]
    likes = [item["likes"] for item in data]
    comments = [item["comments"] for item in data]
    shares = [item["shares"] for item in data]
    new_followers = [item["new_followers"] for item in data]
    time_labels = [item["time_label"] for item in data]
    total_revenue = sum(revenues)
    avg_revenue = total_revenue / len(revenues) if revenues else 0
    max_revenue = max(revenues) if revenues else 0
    min_revenue = min(revenues) if revenues else 0
    total_views = sum(views)
    avg_views = total_views / len(views) if views else 0
    rpm = (total_revenue / total_views * 1000) if total_views else 0
    if len(revenues) > 1:
        revenue_trend = (revenues[-1] - revenues[0]) / revenues[0] if revenues[0] else 0
    else:
        revenue_trend = 0
    engagement_rate = (sum(likes) + sum(comments) + sum(shares)) / sum(views) if sum(views) else 0
    return {
        "platform": platform,
        "platform_name": self.supported_platforms.get(platform, platform),
        "total_revenue": round(total_revenue, 2),
        "avg_revenue": round(avg_revenue, 2),
        "max_revenue": round(max_revenue, 2),
        "min_revenue": round(min_revenue, 2),
        "total_views": total_views,
        "avg_views": round(avg_views, 2),
        "rpm": round(rpm, 2),
        "revenue_trend": round(revenue_trend * 100, 2),  # 转换为百分比
        "engagement_rate": round(engagement_rate * 100, 2),  # 转换为百分比
        "time_labels": time_labels,
        "revenue_data": revenues,
        "views_data": views,
        "likes_data": likes,
        "comments_data": comments,
        "shares_data": shares,
        "new_followers_data": new_followers,
    }
def _compare_platform_revenue(self, platform_analysis: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
    if not platform_analysis:
        return {}
    platforms = []
    total_revenues = []
    avg_revenues = []
    rpms = []
    engagement_rates = []
    revenue_trends = []
    for platform, analysis in platform_analysis.items():
        platforms.append(platform)
        total_revenues.append(analysis.get("total_revenue", 0))
        avg_revenues.append(analysis.get("avg_revenue", 0))
        rpms.append(analysis.get("rpm", 0))
        engagement_rates.append(analysis.get("engagement_rate", 0))
        revenue_trends.append(analysis.get("revenue_trend", 0))
    total_revenue_rank = [sorted(total_revenues, reverse=True).index(x) + 1 for x in total_revenues]
    avg_revenue_rank = [sorted(avg_revenues, reverse=True).index(x) + 1 for x in avg_revenues]
    rpm_rank = [sorted(rpms, reverse=True).index(x) + 1 for x in rpms]
    engagement_rank = [sorted(engagement_rates, reverse=True).index(x) + 1 for x in engagement_rates]
    trend_rank = [sorted(revenue_trends, reverse=True).index(x) + 1 for x in revenue_trends]
    weights = {"total_revenue": 0.3, "rpm": 0.3, "engagement": 0.2, "trend": 0.2}
    composite_scores = []
    for i in range(len(platforms)):
        score = (
            total_revenue_rank[i] * weights["total_revenue"]
            + rpm_rank[i] * weights["rpm"]
            + engagement_rank[i] * weights["engagement"]
            + trend_rank[i] * weights["trend"]
        )
        composite_scores.append(score)
    composite_rank = [sorted(composite_scores).index(x) + 1 for x in composite_scores]
    comparison_result = {
        "best_overall_platform": platforms[composite_rank.index(1)],
        "best_revenue_platform": platforms[total_revenue_rank.index(1)],
        "best_rpm_platform": platforms[rpm_rank.index(1)],
        "best_engagement_platform": platforms[engagement_rank.index(1)],
        "best_growth_platform": platforms[trend_rank.index(1)],
        "platform_ranking": [],
    }
    for i in range(len(platforms)):
        platform_data = {
            "platform": platforms[i],
            "platform_name": self.supported_platforms.get(platforms[i], platforms[i]),
            "total_revenue": total_revenues[i],
            "total_revenue_rank": total_revenue_rank[i],
            "rpm": rpms[i],
            "rpm_rank": rpm_rank[i],
            "engagement_rate": engagement_rates[i],
            "engagement_rank": engagement_rank[i],
            "revenue_trend": revenue_trends[i],
            "trend_rank": trend_rank[i],
            "composite_score": composite_scores[i],
            "composite_rank": composite_rank[i],
        }
        comparison_result["platform_ranking"].append(platform_data)
    comparison_result["platform_ranking"].sort(key=lambda x: x["composite_rank"])
    return comparison_result
def analyze_content_trends(
    self, platforms: List[str] = None, categories: List[str] = None, time_range: str = "week"
) -> Dict[str, Any]:
    logger.info("分析内容趋势: platforms={platforms}, categories={categories}, time_range={time_range}")
    if not platforms:
        platforms = list(self.supported_platforms.keys())
    platforms = [p for p in platforms if p in self.supported_platforms]
    if not platforms:
        logger.error("没有指定有效的平台")
        return {"status": "error", "message": "没有指定有效的平台"}
    platform_trending = {}
    for platform in platforms:
        trending_result = self.fetch_trending_content(platform, None, 100, time_range)
        if trending_result.get("status") == "success":
            platform_trending[platform] = trending_result.get("data", [])
    trend_analysis = self._analyze_content_trends(platform_trending, categories)
    return {
        "status": "success",
        "time_range": time_range,
        "platforms": platforms,
        "categories": categories,
        "trend_analysis": trend_analysis,
    }
def _analyze_content_trends(
    self, platform_trending: Dict[str, List[Dict[str, Any]]], categories: List[str] = None
) -> Dict[str, Any]:
    if not platform_trending:
        return {}
    all_types = []
    for platform, trending_data in platform_trending.items():
        for item in trending_data:
            content_type = item.get("type")
            if content_type:
                all_types.append(content_type)
    type_counter = Counter(all_types)
    if categories:
        type_counter = {k: v for k, v in type_counter.items() if k in categories}
    sorted_types = sorted(type_counter.items(), key=lambda x: x[1], reverse=True)
    all_tags = []
    for platform, trending_data in platform_trending.items():
        for item in trending_data:
            tags = item.get("tags", [])
            all_tags.extend(tags)
    tag_counter = Counter(all_tags)
    sorted_tags = sorted(tag_counter.items(), key=lambda x: x[1], reverse=True)[:20]  # 只取前20个
    platform_type_distribution = {}
    for platform, trending_data in platform_trending.items():
        platform_types = [item.get("type") for item in trending_data if item.get("type")]
        platform_type_counter = Counter(platform_types)
        if categories:
            platform_type_counter = {k: v for k, v in platform_type_counter.items() if k in categories}
        platform_sorted_types = sorted(platform_type_counter.items(), key=lambda x: x[1], reverse=True)
        platform_type_distribution[platform] = {
            "type_counts": dict(platform_type_counter),
            "sorted_types": platform_sorted_types,
        }
    avg_duration = {}
    avg_play_count = {}
    avg_like_count = {}
    avg_comment_count = {}
    avg_share_count = {}
    avg_revenue = {}
    for platform, trending_data in platform_trending.items():
        type_durations = {}
        type_play_counts = {}
        type_like_counts = {}
        type_comment_counts = {}
        type_share_counts = {}
        type_revenues = {}
        for item in trending_data:
            content_type = item.get("type")
            if not content_type or (categories and content_type not in categories):
                continue
            if content_type not in type_durations:
                type_durations[content_type] = []
                type_play_counts[content_type] = []
                type_like_counts[content_type] = []
                type_comment_counts[content_type] = []
                type_share_counts[content_type] = []
                type_revenues[content_type] = []
            if "duration" in item:
                type_durations[content_type].append(item["duration"])
            if "play_count" in item:
                type_play_counts[content_type].append(item["play_count"])
            if "like_count" in item:
                type_like_counts[content_type].append(item["like_count"])
            if "comment_count" in item:
                type_comment_counts[content_type].append(item["comment_count"])
            if "share_count" in item:
                type_share_counts[content_type].append(item["share_count"])
            if "estimated_revenue" in item:
                type_revenues[content_type].append(item["estimated_revenue"])
        avg_duration[platform] = {
            content_type: sum(durations) / len(durations) if durations else 0
            for content_type, durations in type_durations.items()
        }
        avg_play_count[platform] = {
            content_type: sum(counts) / len(counts) if counts else 0
            for content_type, counts in type_play_counts.items()
        }
        avg_like_count[platform] = {
            content_type: sum(counts) / len(counts) if counts else 0
            for content_type, counts in type_like_counts.items()
        }
        avg_comment_count[platform] = {
            content_type: sum(counts) / len(counts) if counts else 0
            for content_type, counts in type_comment_counts.items()
        }
        avg_share_count[platform] = {
            content_type: sum(counts) / len(counts) if counts else 0
            for content_type, counts in type_share_counts.items()
        }
        avg_revenue[platform] = {
            content_type: sum(revenues) / len(revenues) if revenues else 0
            for content_type, revenues in type_revenues.items()
        }
    return {
        "popular_types": sorted_types,
        "popular_tags": sorted_tags,
        "platform_type_distribution": platform_type_distribution,
        "avg_duration": avg_duration,
        "avg_play_count": avg_play_count,
        "avg_like_count": avg_like_count,
        "avg_comment_count": avg_comment_count,
        "avg_share_count": avg_share_count,
        "avg_revenue": avg_revenue,
    }
def generate_content_strategy(
    self,
    platforms: List[str] = None,
    target_audience: Dict[str, Any] = None,
    content_preferences: Dict[str, Any] = None,
) -> Dict[str, Any]:
    logger.info("生成内容策略: platforms={platforms}")
    if not platforms:
        platforms = list(self.supported_platforms.keys())
    platforms = [p for p in platforms if p in self.supported_platforms]
    if not platforms:
        logger.error("没有指定有效的平台")
        return {"status": "error", "message": "没有指定有效的平台"}
    revenue_analysis = self.analyze_platform_revenue(platforms, "month")
    trend_analysis = self.analyze_content_trends(platforms, None, "week")
    strategy = self._generate_strategy(
        platforms, revenue_analysis, trend_analysis, target_audience, content_preferences
    )
    return {"status": "success", "platforms": platforms, "strategy": strategy}
def _generate_strategy(
    self,
    platforms: List[str],
    revenue_analysis: Dict[str, Any],
    trend_analysis: Dict[str, Any],
    target_audience: Dict[str, Any] = None,
    content_preferences: Dict[str, Any] = None,
) -> Dict[str, Any]:
    platform_priority = []
    if "platform_comparison" in revenue_analysis:
        platform_ranking = revenue_analysis["platform_comparison"].get("platform_ranking", [])
        platform_priority = [item["platform"] for item in platform_ranking if item["platform"] in platforms]
    if not platform_priority:
        platform_priority = platforms
    content_type_recommendations = {}
    if "trend_analysis" in trend_analysis:
        trend_data = trend_analysis["trend_analysis"]
        popular_types = trend_data.get("popular_types", [])
        platform_type_distribution = trend_data.get("platform_type_distribution", {})
        avg_revenue = trend_data.get("avg_revenue", {})
        for platform in platform_priority:
            platform_types = []
            if platform in platform_type_distribution:
                platform_types = [t[0] for t in platform_type_distribution[platform].get("sorted_types", [])]
            platform_revenue = {}
            if platform in avg_revenue:
                platform_revenue = avg_revenue[platform]
            revenue_sorted_types = sorted(platform_revenue.items(), key=lambda x: x[1], reverse=True)
            recommended_types = []
            for content_type, revenue in revenue_sorted_types[:3]:
                if content_type not in recommended_types:
                    recommended_types.append(content_type)
            for content_type in platform_types[:3]:
                if content_type not in recommended_types:
                    recommended_types.append(content_type)
            for content_type, count in popular_types[:3]:
                if content_type not in recommended_types:
                    recommended_types.append(content_type)
            recommended_types = recommended_types[:5]
            type_reasons = {}
            for content_type in recommended_types:
                reasons = []
                for t, r in revenue_sorted_types:
                    if t == content_type:
                        reasons.append("在{platform}平台上，{content_type}类型的内容平均收益为{r:.2f}元")
                        break
                for i, t in enumerate(platform_types):
                    if t == content_type:
                        reasons.append("在{platform}平台上，{content_type}类型的内容排名第{i+1}位")
                        break
                for t, c in popular_types:
                    if t == content_type:
                        reasons.append(f"在所有平台上，{content_type}类型的内容出现{c}次")
                        break
                type_reasons[content_type] = reasons
            content_type_recommendations[platform] = {
                "recommended_types": recommended_types,
                "type_reasons": type_reasons,
            }
    content_feature_recommendations = {}
    if "trend_analysis" in trend_analysis:
        trend_data = trend_analysis["trend_analysis"]
        avg_duration = trend_data.get("avg_duration", {})
        avg_like_count = trend_data.get("avg_like_count", {})
        avg_comment_count = trend_data.get("avg_comment_count", {})
        avg_share_count = trend_data.get("avg_share_count", {})
        for platform in platform_priority:
            recommended_types = content_type_recommendations.get(platform, {}).get("recommended_types", [])
            platform_duration = {}
            if platform in avg_duration:
                platform_duration = avg_duration[platform]
            platform_likes = {}
            platform_comments = {}
            platform_shares = {}
            if platform in avg_like_count:
                platform_likes = avg_like_count[platform]
            if platform in avg_comment_count:
                platform_comments = avg_comment_count[platform]
            if platform in avg_share_count:
                platform_shares = avg_share_count[platform]
            type_features = {}
            for content_type in recommended_types:
                features = {}
                if content_type in platform_duration:
                    duration = platform_duration[content_type]
                    features["recommended_duration"] = round(duration, 1)
                engagement_features = {}
                if content_type in platform_likes:
                    engagement_features["likes"] = platform_likes[content_type]
                if content_type in platform_comments:
                    engagement_features["comments"] = platform_comments[content_type]
                if content_type in platform_shares:
                    engagement_features["shares"] = platform_shares[content_type]
                if engagement_features:
                    total = sum(engagement_features.values())
                    if total > 0:
                        engagement_ratio = {k: round(v / total * 100, 1) for k, v in engagement_features.items()}
                        features["engagement_ratio"] = engagement_ratio
                type_features[content_type] = features
            content_feature_recommendations[platform] = type_features
    publishing_recommendations = {}
    for platform in platform_priority:
        if platform == "douyin":
            publishing_recommendations[platform] = {
                "optimal_posting_times": ["12:00", "18:00", "21:00"],
                "posting_frequency": "每天1-2次",
                "title_strategy": "简短吸引人，使用热门话题标签",
                "description_strategy": "简洁明了，包含2-3个热门标签",
                "hashtag_strategy": "使用3-5个相关热门标签",
            }
        elif platform == "kuaishou":
            publishing_recommendations[platform] = {
                "optimal_posting_times": ["12:00", "19:00", "21:30"],
                "posting_frequency": "每天1-2次",
                "title_strategy": "直接明了，突出内容亮点",
                "description_strategy": "详细描述内容，使用方言或地域特色",
                "hashtag_strategy": "使用2-4个相关标签，包括地域标签",
            }
        elif platform == "bilibili":
            publishing_recommendations[platform] = {
                "optimal_posting_times": ["12:00", "17:00", "22:00"],
                "posting_frequency": "每周2-3次",
                "title_strategy": "详细且有吸引力，20-30字为佳",
                "description_strategy": "详细介绍内容，添加时间轴",
                "hashtag_strategy": "使用5-10个相关标签，包括分区标签",
            }
        else:
            publishing_recommendations[platform] = {
                "optimal_posting_times": ["12:00", "18:00", "21:00"],
                "posting_frequency": "每天1次",
                "title_strategy": "简明扼要，突出内容亮点",
                "description_strategy": "详细描述内容，添加相关链接",
                "hashtag_strategy": "使用3-5个相关标签",
            }
    strategy = {
        "platform_priority": platform_priority,
        "content_type_recommendations": content_type_recommendations,
        "content_feature_recommendations": content_feature_recommendations,
        "publishing_recommendations": publishing_recommendations,
        "general_recommendations": [
            "关注热门话题和趋势，及时调整内容策略",
            "保持内容质量和一致性，建立个人品牌",
            "与观众互动，回复评论，增加粉丝黏性",
            "分析数据，了解哪些内容表现最好，持续优化",
            "跨平台发布，但根据各平台特性调整内容形式",
        ],
    }
    return strategy