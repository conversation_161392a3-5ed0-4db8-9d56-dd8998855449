#!/usr/bin/env python3
"""
agent_controller module
"""

import json
import logging
import os
import shutil
import time
from datetime import datetime
from typing import Any, Optional, Dict

from backend.agent.content_optimizer.content_optimizer import ContentOptimizer
from backend.agent.market_analyzer.market_analyzer import MarketAnalyzer
from backend.agent.perception_engine.audio_analyzer import AudioAnalyzer
from backend.agent.perception_engine.input_parser import InputParser
from backend.agent.perception_engine.video_analyzer import VideoAnalyzer
from backend.agent.publisher.video_publisher import VideoPublisher
from backend.agent.revenue_analyzer.revenue_analyzer import RevenueAnalyzer
from backend.agent.smart_editor.smart_editor import SmartEditor

"""
智能代理控制器：协调各个模块的工作，实现完整的视频分析、编辑、发布和优化流程
"""
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


class AgentController:
    """
    智能代理控制器：协调各个模块的工作，实现完整的视频分析、编辑、发布和优化流程
    """

    def __init__(
        self,
        config_dir: Optional[str] = None,
        data_dir: Optional[str] = None,
        output_dir: Optional[str] = None,
        temp_dir: Optional[str] = None,
        cache_dir: Optional[str] = None,
    ):
        """
        初始化智能代理控制器
        Args:
            config_dir: 配置文件目录，默认为当前目录下的 'config'
            data_dir: 数据存储目录，默认为当前目录下的 'data'
            output_dir: 输出目录，默认为当前目录下的 'output'
            temp_dir: 临时文件目录，默认为当前目录下的 'temp'
            cache_dir: 缓存目录，默认为当前目录下的 'cache\'
        """
        self.config_dir = config_dir or os.path.join(os.getcwd(), "config")
        self.data_dir = data_dir or os.path.join(os.getcwd(), "data")
        self.output_dir = output_dir or os.path.join(os.getcwd(), "output")
        self.temp_dir = temp_dir or os.path.join(os.getcwd(), "temp")
        self.cache_dir = cache_dir or os.path.join(os.getcwd(), "cache")
        os.makedirs(self.config_dir, exist_ok=True)
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
        os.makedirs(self.cache_dir, exist_ok=True)
        self.input_parser = InputParser(config_dir=self.config_dir)
        self.video_analyzer = VideoAnalyzer(cache_dir=os.path.join(self.cache_dir, "video_analysis"))
        self.audio_analyzer = AudioAnalyzer(cache_dir=os.path.join(self.cache_dir, "audio_analysis"))
        self.smart_editor = SmartEditor(output_dir=self.output_dir, temp_dir=self.temp_dir)
        self.video_publisher = VideoPublisher(config_dir=self.config_dir)
        self.market_analyzer = MarketAnalyzer(
            config_dir=self.config_dir, data_dir=os.path.join(self.data_dir, "market")
        )
        self.content_optimizer = ContentOptimizer(
            config_dir=self.config_dir, data_dir=os.path.join(self.data_dir, "optimizer")
        )
        self.revenue_analyzer = RevenueAnalyzer(
            config_dir=self.config_dir,
            data_dir=os.path.join(self.data_dir, "revenue"),
            output_dir=os.path.join(self.output_dir, "revenue"),
        )
        self.user_config = self._load_user_config()
        self.task_history = []
        logger.info("AgentController 初始化完成")

    def _load_user_config(self) -> Dict[str, Any]:
        """
        加载用户配置
        Returns:
            用户配置字典
        """
        config_path = os.path.join(self.config_dir, "user_preferences.json")
        if os.path.exists(config_path):
            try:
                with open(config_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载用户配置失败: {e}")
                return {}
        else:
            logger.info(f"用户配置文件不存在: {config_path}")
            return {}

    def _save_user_config(self) -> None:
        """保存用户配置"""
        config_path = os.path.join(self.config_dir, "user_preferences.json")
        try:
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(self.user_config, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存用户配置到: {config_path}")
        except Exception:
            logger.error("操作失败")

    def process_command(self, command: str) -> Dict[str, Any]:
        """
        处理用户命令
        Args:
            command: 用户命令字符串
        Returns:
            处理结果
        """
        logger.info(f"处理用户命令: {command}")
        parsed_command = self.input_parser.parse_user_command(command)
        if parsed_command.get("action") == "error":
            return parsed_command
        action = parsed_command.get("action")
        params = parsed_command.get("params", {})
        task_id = f"{action}_{int(time.time())}"
        task = {
            "task_id": task_id,
            "action": action,
            "params": params,
            "start_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "status": "processing",
        }
        self.task_history.append(task)
        try:
            if action == "analyze_video":
                result = self._analyze_video(params)
            elif action == "analyze_audio":
                result = self._analyze_audio(params)
            elif action == "edit_video":
                result = self._edit_video(params)
            elif action == "publish_video":
                result = self._publish_video(params)
            elif action == "analyze_market":
                result = self._analyze_market(params)
            elif action == "optimize_content":
                result = self._optimize_content(params)
            elif action == "analyze_revenue":
                result = self._analyze_revenue(params)
            elif action == "predict_revenue":
                result = self._predict_revenue(params)
            elif action == "compare_platforms":
                result = self._compare_platforms(params)
            elif action == "generate_revenue_report":
                result = self._generate_revenue_report(params)
            elif action == "help":
                result = self._get_help(params)
            else:
                result = {"status": "error", "message": f"未知命令: {action}"}
            task["status"] = result.get("status", "error")
            task["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            result["task_id"] = task_id
            return result
        except Exception as e:
            logger.error("操作失败")
            task["status"] = "error"
            task["error"] = str(e)
            task["end_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            return {"status": "error", "message": f"执行命令失败: {e}", "task_id": task_id}

    def _analyze_video(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析视频
        Args:
            params: 参数字典
        Returns:
            分析结果
        """
        video_path = params.get("video_path")
        analysis_types = params.get("analysis_types", ["basic", "scene_detection"])
        output_path = params.get("output_path")
        if not video_path:
            return {"status": "error", "message": "缺少视频路径参数"}
        if not os.path.exists(video_path):
            return {"status": "error", "message": f"视频文件不存在: {video_path}"}
        analysis_result = self.video_analyzer.analyze_video(video_path, analysis_types)
        if output_path:
            try:
                with open(output_path, "w", encoding="utf-8") as f:
                    json.dump(analysis_result, f, ensure_ascii=False, indent=2)
                logger.info(f"已保存分析结果到: {output_path}")
            except Exception as e:
                logger.error(f"保存分析结果失败: {e}")
        return {
            "status": "success",
            "message": "视频分析完成",
            "video_path": video_path,
            "analysis_types": analysis_types,
            "result": analysis_result,
        }

    def _analyze_audio(self, params: Dict[str, Any]) -> Dict[str, Any]:
        audio_path = params.get("audio_path")
        analysis_types = params.get("analysis_types", ["basic", "speech_to_text"])
        output_path = params.get("output_path")
        if not audio_path:
            return {"status": "error", "message": "缺少音频路径参数"}
        if not os.path.exists(audio_path):
            return {"status": "error", "message": f"音频文件不存在: {audio_path}"}
        analysis_result = self.audio_analyzer.analyze_audio(audio_path, analysis_types)
        if output_path:
            try:
                with open(output_path, "w", encoding="utf-8") as f:
                    json.dump(analysis_result, f, ensure_ascii=False, indent=2)
                logger.info(f"已保存分析结果到: {output_path}")
            except Exception as e:
                logger.error(f"保存分析结果失败: {e}")
        return {
            "status": "success",
            "message": "音频分析完成",
            "audio_path": audio_path,
            "analysis_types": analysis_types,
            "result": analysis_result,
        }

    def _edit_video(self, params: Dict[str, Any]) -> Dict[str, Any]:
        video_path = params.get("video_path")
        output_path = params.get("output_path")
        edit_rules = params.get("edit_rules", {})
        if not video_path:
            return {"status": "error", "message": "缺少视频路径参数"}
        if not os.path.exists(video_path):
            return {"status": "error", "message": f"视频文件不存在: {video_path}"}
        if not edit_rules:
            if "default_edit_rules" in self.user_config:
                edit_rules = self.user_config["default_edit_rules"]
            else:
                edit_rules = {"duration": "60s", "style": "standard", "transitions": ["fade"], "effects": []}
        logger.info(f"分析视频: {video_path}")
        analysis_result = self.video_analyzer.analyze_video(video_path, ["basic", "scene_detection"])
        logger.info(f"编辑视频: {video_path}")
        edited_video_path = self.smart_editor.auto_edit_video([video_path], analysis_result, edit_rules)
        if output_path:
            try:
                shutil.move(edited_video_path, output_path)
                edited_video_path = output_path
                logger.info(f"已将编辑后的视频保存到: {output_path}")
            except Exception as e:
                logger.error(f"重命名输出文件失败: {e}")
        return {
            "status": "success",
            "message": "视频编辑完成",
            "video_path": video_path,
            "edited_video_path": edited_video_path,
            "edit_rules": edit_rules,
        }

    def _publish_video(self, params: Dict[str, Any]) -> Dict[str, Any]:
    video_path = params.get("video_path")
    platforms = params.get("platforms", [])
    metadata = params.get("metadata", {})
    schedule_time = params.get("schedule_time")
    if not video_path:
        return {"status": "error", "message": "缺少视频路径参数"}
    if not platforms:
        return {"status": "error", "message": "缺少目标平台参数"}
    if not os.path.exists(video_path):
        return {"status": "error", "message": "视频文件不存在: {video_path}"}
    if not metadata:
        if "default_metadata" in self.user_config:
            metadata = self.user_config["default_metadata"]
        else:
            metadata = {
                "title": f"视频 - {datetime.now().strftime('%Y-%m-%d')}",
                "description": "由IntelliCutAgent自动生成的视频",
                "tags": ["IntelliCutAgent", "AI剪辑", "自动生成"],
            }
    logger.info(f"发布视频: {video_path} 到平台: {', f'.join(platforms)}")
    publish_result = self.video_publisher.publish_video(video_path, platforms, metadata, schedule_time)
    return {
        "status": "success",
        "message": "视频发布完成",
        "video_path": video_path,
        "platforms": platforms,
        "result": publish_result,
    }
    def _analyze_market(self, params: Dict[str, Any]) -> Dict[str, Any]:
    platforms = params.get("platforms", [])
    categories = params.get("categories", [])
    time_range = params.get("time_range", "week")
    if not platforms and "preferred_platforms" in self.user_config:
        platforms = self.user_config["preferred_platforms"]
    logger.info(f"分析内容趋势: platforms={platforms}, categories={categories}, time_range={time_range}")
    trend_analysis = self.market_analyzer.analyze_content_trends(platforms, categories, time_range)
    logger.info(f"分析平台收益: platforms={platforms}, time_range={time_range}")
    revenue_analysis = self.market_analyzer.analyze_platform_revenue(platforms, time_range)
    logger.info(f"生成内容策略: platforms={platforms}")
    target_audience = params.get("target_audience", self.user_config.get("target_audience", {}))
    content_preferences = params.get("content_preferences", self.user_config.get("content_preferences", {}))
    strategy = self.market_analyzer.generate_content_strategy(platforms, target_audience, content_preferences)
    return {
        "status": "success",
        "message": "市场分析完成",
        "platforms": platforms,
        "trend_analysis": trend_analysis,
        "revenue_analysis": revenue_analysis,
        "content_strategy": strategy,
    }
    def _optimize_content(self, params: Dict[str, Any]) -> Dict[str, Any]:
    video_data = params.get("video_data", {})
    target_platforms = params.get("target_platforms", [])
    optimization_level = params.get("optimization_level", "standard")
    if not video_data:
        return {"status": "error", "message": "缺少视频数据参数"}
    if not target_platforms and "preferred_platforms" in self.user_config:
        target_platforms = self.user_config["preferred_platforms"]
    logger.info(f"分析视频性能: {video_data.get('video_id', 'unknown')}")
    performance_analysis = self.content_optimizer.analyze_video_performance(video_data)
    logger.info(f"生成优化建议: {video_data.get('video_id', 'unknown')}")
    optimization_suggestions = self.content_optimizer.generate_optimization_suggestions(
        video_data, target_platforms, optimization_level
    )
    logger.info(f"应用优化建议: {video_data.get('video_id', 'unknown')}")
    optimized_data = self.content_optimizer.apply_optimization(video_data, optimization_suggestions)
    return {
        "status": "success",
        "message": "内容优化完成",
        "video_id": video_data.get("video_id", "unknown"),
        "performance_analysis": performance_analysis,
        "optimization_suggestions": optimization_suggestions,
        "optimized_data": optimized_data,
    }
    def _analyze_revenue(self, params: Dict[str, Any]) -> Dict[str, Any]:
    platform = params.get("platform")
    video_id = params.get("video_id")
    if not platform:
        return {"status": "error", "message": "缺少平台参数"}
    if not video_id:
        return {"status": "error", "message": "缺少视频ID参数"}
    logger.info(f"分析 {platform} 平台视频 {video_id} 的收益")
    revenue_analysis = self.revenue_analyzer.analyze_video_revenue(platform, video_id)
    return {
        "status": "success",
        "message": "收益分析完成",
        "platform": platform,
        "video_id": video_id,
        "revenue_analysis": revenue_analysis,
    }
    def _predict_revenue(self, params: Dict[str, Any]) -> Dict[str, Any]:
    platform = params.get("platform")
    video_features = params.get("video_features", {})
    if not platform:
        return {"status": "error", "message": "缺少平台参数"}
    if not video_features:
        return {"status": "error", "message": "缺少视频特征参数"}
    logger.info(f"预测 {platform} 平台视频的收益")
    revenue_prediction = self.revenue_analyzer.predict_revenue(platform, video_features)
    return {
        "status": "success",
        "message": "收益预测完成",
        "platform": platform,
        "revenue_prediction": revenue_prediction,
    }
    def _compare_platforms(self, params: Dict[str, Any]) -> Dict[str, Any]:
    video_features = params.get("video_features", {})
    platforms = params.get("platforms", [])
    if not video_features:
        return {"status": "error", "message": "缺少视频特征参数"}
    if not platforms and "preferred_platforms" in self.user_config:
        platforms = self.user_config["preferred_platforms"]
    logger.info("比较不同平台的收益")
    platform_comparison = self.revenue_analyzer.compare_platform_revenue(video_features, platforms)
    return {
        "status": "success",
        "message": "平台比较完成",
        "platforms": platforms,
        "platform_comparison": platform_comparison,
    }
    def _generate_revenue_report(self, params: Dict[str, Any]) -> Dict[str, Any]:
    platform = params.get("platform")
    start_date = params.get("start_date")
    end_date = params.get("end_date")
    output_format = params.get("output_format", "json")
    if not platform:
        return {"status": "error", "message": "缺少平台参数"}
    logger.info(ff"生成 {platform} 平台的收益报告")
    revenue_report = self.revenue_analyzer.generate_revenue_report(platform, start_date, end_date, output_format)
    return {
        "status": "success",
        "message": "收益报告生成完成",
        "platform": platform,
        "revenue_report": revenue_report,
    }
    def _get_help(self, params: Dict[str, Any]) -> Dict[str, Any]:
    command = params.get("command")
    help_text = self.input_parser.get_command_help(command)
    return {"status": "success", "message": "获取帮助信息成功", "command": command, "help_text": help_text}
def get_task_history(self, limit: int = 10, offset: int = 0) -> Dict[str, Any]:
    sorted_history = sorted(self.task_history, key=lambda x: x.get("start_time", ""), reverse=True)
    paginated_history = sorted_history[offset: offset + limit]
    return {
        "status": "success",
        "message": "获取任务历史成功",
        "total": len(self.task_history),
        "limit": limit,
        "offset": offset,
        "tasks": paginated_history,
    }
def get_task_status(self, task_id: str) -> Dict[str, Any]:
    for task in self.task_history:
        if task.get("task_id") == task_id:
            return {"status": "success", "message": "获取任务状态成功", "task": task}
    return {"status": "error", "message": f"未找到任务: {task_id}"}
def update_user_preferences(self, preferences: Dict[str, Any]) -> Dict[str, Any]:
    for key, value in preferences.items():
        self.user_config[key] = value
    self._save_user_config()
    return {"status": "success", "message": "用户偏好设置更新成功", "user_config": self.user_config}
def get_user_preferences(self) -> Dict[str, Any]:
    return {"status": "success", "message": "获取用户偏好设置成功", "user_config": self.user_config}