#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理控制器
负责协调各个组件，处理用户请求，管理任务执行
"""

import json
import logging
import os
import shutil
from datetime import datetime
from typing import Any, Dict, List, Optional

from .content_analyzer.content_analyzer import ContentAnalyzer
from .content_optimizer.content_optimizer import ContentOptimizer
from .perception_engine.audio_analyzer import AudioAnalyzer
from .perception_engine.input_parser import InputParser
from .perception_engine.video_analyzer import VideoAnalyzer
from .smart_editor.smart_editor import SmartEditor

logger = logging.getLogger(__name__)


class AgentController:
    """
    代理控制器类
    
    功能：
    1. 协调各个组件
    2. 处理用户请求
    3. 管理任务执行
    4. 维护用户配置
    """

    def __init__(self, config_dir: Optional[str] = None, data_dir: Optional[str] = None):
        """
        初始化代理控制器
        
        Args:
            config_dir: 配置目录路径
            data_dir: 数据目录路径
        """
        self.config_dir = config_dir or os.path.join(os.getcwd(), "config")
        self.data_dir = data_dir or os.path.join(os.getcwd(), "data")
        self.cache_dir = os.path.join(self.data_dir, "cache")
        self.temp_dir = os.path.join(os.getcwd(), "temp")
        self.output_dir = os.path.join(os.getcwd(), "output")
        
        # 确保目录存在
        for directory in [self.config_dir, self.data_dir, self.cache_dir, self.temp_dir, self.output_dir]:
            os.makedirs(directory, exist_ok=True)
        
        # 初始化组件
        self.input_parser = InputParser(config_dir=self.config_dir)
        self.video_analyzer = VideoAnalyzer(cache_dir=os.path.join(self.cache_dir, "video_analysis"))
        self.audio_analyzer = AudioAnalyzer(cache_dir=os.path.join(self.cache_dir, "audio_analysis"))
        self.smart_editor = SmartEditor(output_dir=self.output_dir, temp_dir=self.temp_dir)
        self.content_optimizer = ContentOptimizer(config_dir=self.config_dir, data_dir=os.path.join(self.data_dir, "content_optimizer"))
        
        # 任务历史
        self.task_history = []
        
        # 用户配置
        self.user_config = self._load_user_config()
        
        logger.info("AgentController 初始化完成")

    def _load_user_config(self) -> Dict[str, Any]:
        """加载用户配置"""
        config_path = os.path.join(self.config_dir, "user_config.json")
        try:
            if os.path.exists(config_path):
                with open(config_path, "r", encoding="utf-8") as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"加载用户配置失败: {e}")
        
        # 返回默认配置
        return {
            "preferred_platforms": ["douyin", "bilibili"],
            "default_edit_rules": {
                "duration": "30s",
                "style": "standard",
                "transitions": ["fade"],
                "effects": []
            },
            "default_metadata": {
                "title": "AI生成视频",
                "description": "由IntelliCutAgent自动生成",
                "tags": ["AI", "自动生成"]
            }
        }

    def _save_user_config(self):
        """保存用户配置"""
        config_path = os.path.join(self.config_dir, "user_config.json")
        try:
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(self.user_config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存用户配置失败: {e}")

    def process_request(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理用户请求
        
        Args:
            request_data: 请求数据
            
        Returns:
            处理结果
        """
        try:
            # 解析请求
            parsed_command = self.input_parser.parse_command(request_data)
            action = parsed_command.get("action")
            params = parsed_command.get("params", {})
            
            # 创建任务记录
            task_id = f"task_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            task = {
                "task_id": task_id,
                "action": action,
                "params": params,
                "start_time": datetime.now().isoformat(),
                "status": "running"
            }
            self.task_history.append(task)
            
            logger.info(f"处理请求: action={action}, task_id={task_id}")
            
            # 执行对应的操作
            if action == "analyze_video":
                result = self._analyze_video(params)
            elif action == "analyze_audio":
                result = self._analyze_audio(params)
            elif action == "edit_video":
                result = self._edit_video(params)
            elif action == "publish_video":
                result = self._publish_video(params)
            elif action == "help":
                result = self._get_help(params)
            else:
                result = {"status": "error", "message": f"未知命令: {action}"}
            
            # 更新任务状态
            task["status"] = result.get("status", "error")
            task["end_time"] = datetime.now().isoformat()
            task["result"] = result
            
            return result
            
        except Exception as e:
            logger.error(f"处理请求失败: {e}")
            return {"status": "error", "message": f"处理请求失败: {e}"}

    def _analyze_video(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """分析视频"""
        video_path = params.get("video_path")
        analysis_types = params.get("analysis_types", ["basic"])
        output_path = params.get("output_path")
        
        if not video_path:
            return {"status": "error", "message": "缺少视频路径参数"}
        if not os.path.exists(video_path):
            return {"status": "error", "message": f"视频文件不存在: {video_path}"}
        
        logger.info(f"分析视频: {video_path}")
        analysis_result = self.video_analyzer.analyze_video(video_path, analysis_types)
        
        if output_path:
            try:
                with open(output_path, "w", encoding="utf-8") as f:
                    json.dump(analysis_result, f, ensure_ascii=False, indent=2)
                logger.info(f"分析结果已保存到: {output_path}")
            except Exception as e:
                logger.error(f"保存分析结果失败: {e}")
        
        return {
            "status": "success",
            "message": "视频分析完成",
            "video_path": video_path,
            "analysis_types": analysis_types,
            "result": analysis_result,
        }

    def _analyze_audio(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """分析音频"""
        audio_path = params.get("audio_path")
        analysis_types = params.get("analysis_types", ["basic"])
        output_path = params.get("output_path")
        
        if not audio_path:
            return {"status": "error", "message": "缺少音频路径参数"}
        if not os.path.exists(audio_path):
            return {"status": "error", "message": f"音频文件不存在: {audio_path}"}
        
        logger.info(f"分析音频: {audio_path}")
        analysis_result = self.audio_analyzer.analyze_audio(audio_path, analysis_types)
        
        if output_path:
            try:
                with open(output_path, "w", encoding="utf-8") as f:
                    json.dump(analysis_result, f, ensure_ascii=False, indent=2)
                logger.info(f"分析结果已保存到: {output_path}")
            except Exception as e:
                logger.error(f"保存分析结果失败: {e}")
        
        return {
            "status": "success",
            "message": "音频分析完成",
            "audio_path": audio_path,
            "analysis_types": analysis_types,
            "result": analysis_result,
        }

    def _edit_video(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """编辑视频"""
        video_path = params.get("video_path")
        output_path = params.get("output_path")
        edit_rules = params.get("edit_rules", {})
        
        if not video_path:
            return {"status": "error", "message": "缺少视频路径参数"}
        if not os.path.exists(video_path):
            return {"status": "error", "message": f"视频文件不存在: {video_path}"}
        
        if not edit_rules:
            if "default_edit_rules" in self.user_config:
                edit_rules = self.user_config["default_edit_rules"]
            else:
                edit_rules = {"duration": "60s", "style": "standard", "transitions": ["fade"], "effects": []}
        
        logger.info(f"分析视频: {video_path}")
        analysis_result = self.video_analyzer.analyze_video(video_path, ["basic", "scene_detection"])
        
        logger.info(f"编辑视频: {video_path}")
        edited_video_path = self.smart_editor.edit_video([video_path], analysis_result, edit_rules)
        
        if output_path:
            try:
                shutil.move(edited_video_path, output_path)
                edited_video_path = output_path
                logger.info(f"已将编辑后的视频保存到: {output_path}")
            except Exception as e:
                logger.error(f"重命名输出文件失败: {e}")
        
        return {
            "status": "success",
            "message": "视频编辑完成",
            "video_path": video_path,
            "edited_video_path": edited_video_path,
            "edit_rules": edit_rules,
        }

    def _publish_video(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """发布视频"""
        video_path = params.get("video_path")
        platforms = params.get("platforms", [])
        metadata = params.get("metadata", {})
        
        if not video_path:
            return {"status": "error", "message": "缺少视频路径参数"}
        if not os.path.exists(video_path):
            return {"status": "error", "message": f"视频文件不存在: {video_path}"}
        if not platforms:
            return {"status": "error", "message": "缺少目标平台参数"}
        
        if not metadata:
            if "default_metadata" in self.user_config:
                metadata = self.user_config["default_metadata"]
            else:
                metadata = {
                    "title": f"视频 - {datetime.now().strftime('%Y-%m-%d')}",
                    "description": "由IntelliCutAgent自动生成",
                    "tags": ["IntelliCutAgent", "AI剪辑", "自动生成"],
                }
        
        logger.info(f"发布视频: {video_path} 到平台: {', '.join(platforms)}")
        
        # 模拟发布过程
        publish_result = {}
        for platform in platforms:
            publish_result[platform] = {
                "status": "success",
                "url": f"https://{platform}.com/video/mock_video_id",
                "message": f"成功发布到{platform}"
            }
        
        return {
            "status": "success",
            "message": "视频发布完成",
            "video_path": video_path,
            "platforms": platforms,
            "result": publish_result,
        }

    def _get_help(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """获取帮助信息"""
        command = params.get("command")
        help_text = self.input_parser.get_command_help(command)
        return {"status": "success", "message": "获取帮助信息成功", "command": command, "help_text": help_text}

    def get_task_history(self, limit: int = 10, offset: int = 0) -> Dict[str, Any]:
        """获取任务历史"""
        sorted_history = sorted(self.task_history, key=lambda x: x.get("start_time", ""), reverse=True)
        paginated_history = sorted_history[offset: offset + limit]
        return {
            "status": "success",
            "message": "获取任务历史成功",
            "total": len(self.task_history),
            "limit": limit,
            "offset": offset,
            "tasks": paginated_history,
        }

    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        for task in self.task_history:
            if task.get("task_id") == task_id:
                return {"status": "success", "message": "获取任务状态成功", "task": task}
        return {"status": "error", "message": f"未找到任务: {task_id}"}

    def update_user_preferences(self, preferences: Dict[str, Any]) -> Dict[str, Any]:
        """更新用户偏好设置"""
        for key, value in preferences.items():
            self.user_config[key] = value
        self._save_user_config()
        return {"status": "success", "message": "用户偏好设置更新成功", "user_config": self.user_config}

    def get_user_preferences(self) -> Dict[str, Any]:
        """获取用户偏好设置"""
        return {"status": "success", "message": "获取用户偏好设置成功", "user_config": self.user_config}


# 演示函数
def main():
    """演示代理控制器功能"""
    controller = AgentController()
    
    # 示例请求
    request_data = {
        "action": "analyze_video",
        "params": {
            "video_path": "demo_video.mp4",
            "analysis_types": ["basic", "scene_detection"]
        }
    }
    
    result = controller.process_request(request_data)
    print("处理结果:", json.dumps(result, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    main()
