# backend.agent.tools.api_factory

import logging
from typing import Any
from typing import Dict

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# 导入API客户端
from backend.agent.tools.api_clients import ToutiaoAPI
from backend.agent.tools.api_clients import XiaohongshuAPI
from backend.agent.tools.api_clients import XiguaAPI
from backend.agent.tools.api_clients import YouTubeAPI

# 导入配置加载器
from backend.utils.config_loader import ConfigLoader

class APIFactory:
    """
    API工厂类，用于创建和管理API客户端实例。
    """

    def __init__(self, config_dir: str = None):
        """
        初始化API工厂。

        Args:
            config_dir: 配置目录路径，如果为 None 则使用默认路径
        """
        # 初始化配置加载器
        self.config_loader = ConfigLoader(config_dir)

        # API客户端实例缓存
        self._api_instances = {}

        logger.info("API工厂初始化完成")

    def get_api(self, platform: str) -> Any:
        """
        获取指定平台的API客户端实例。

        Args:
            platform: 平台名称 (youtube, xigua, toutiao, xiaohongshu)

        Returns:
            API客户端实例
        """
        # 检查缓存中是否已有实例
        if platform in self._api_instances:
            return self._api_instances[platform]

        # 加载凭证
        credentials = self.config_loader.load_credentials(platform)

        # 获取凭证路径
        credentials_path = self.config_loader.get_credentials_path(platform)

        # 根据平台创建对应的API客户端实例
        api_instance = None

        if platform == "youtube":
            api_key = credentials.get("api_key")
            api_instance = YouTubeAPI(credentials_path=credentials_path, api_key=api_key)

        elif platform == "xigua":
            cookie = credentials.get("cookie")
            api_instance = XiguaAPI(credentials_path=credentials_path, cookie=cookie)

        elif platform == "toutiao":
            cookie = credentials.get("cookie")
            api_instance = ToutiaoAPI(credentials_path=credentials_path, cookie=cookie)

        elif platform == "xiaohongshu":
            cookie = credentials.get("cookie")
            api_instance = XiaohongshuAPI(credentials_path=credentials_path, cookie=cookie)

        else:
            logger.error("不支持的平台: {platform}")
            return None

        # 缓存实例
        if api_instance:
            self._api_instances[platform] = api_instance
            logger.info("已创建 {platform} API客户端实例")

        return api_instance

    def authenticate_api(self, platform: str) -> bool:
        """
        认证指定平台的API客户端。

        Args:
            platform: 平台名称 (youtube, xigua, toutiao, xiaohongshu)

        Returns:
            是否成功认证
        """
        api_instance = self.get_api(platform)

        if api_instance:
            success = api_instance.authenticate()
            if success:
                logger.info("{platform} API客户端认证成功")
            else:
                logger.error("{platform} API客户端认证失败")
            return success
        else:
            logger.error("获取 {platform} API客户端实例失败")
            return False

    def authenticate_all(self) -> Dict[str, bool]:
        """
        认证所有平台的API客户端。

        Returns:
            各平台认证结果
        """
        platforms = ["youtube", "xigua", "toutiao", "xiaohongshu"]
        results = {}

        for platform in platforms:
            results[platform] = self.authenticate_api(platform)

        return results
