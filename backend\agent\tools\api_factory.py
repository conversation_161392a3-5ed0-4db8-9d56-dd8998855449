#!/usr/bin/env python3
"""
api_factory module
"""

import logging
from typing import Any, Optional, Dict

from backend.agent.tools.api_clients import ToutiaoAPI, XiaohongshuAPI, XiguaAPI, YouTubeAPI
from backend.utils.config_loader import ConfigLoader

"""
API工厂类，用于创建和管理API客户端实例。
"""
    """
    初始化API工厂。
    Args:
        config_dir: 配置目录路径，如果为 None 则使用默认路径
    """
    """
    获取指定平台的API客户端实例。
    Args:
        platform: 平台名称 (youtube, xigua, toutiao, xiaohongshu)
    Returns:
        API客户端实例
    """
    """
    认证指定平台的API客户端。
    Args:
        platform: 平台名称 (youtube, xigua, toutiao, xiaohongshu)
    Returns:
        是否成功认证
    """
    """
    认证所有平台的API客户端。
    Returns:
        各平台认证结果
    """
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)
class APIFactory:
    def __init__(self, config_dir: Optional[str] = None):
        self.config_loader = ConfigLoader(config_dir)
        self._api_instances = {}
        logger.info("API工厂初始化完成")
    def get_api(self, platform: str) -> Any:
        if platform in self._api_instances:
        return self._api_instances[platform]
        credentials = self.config_loader.load_credentials(platform)
        credentials_path = self.config_loader.get_credentials_path(platform)
        api_instance = None
        if platform == "youtube":
        api_key = credentials.get("api_key")
        api_instance = YouTubeAPI(credentials_path=credentials_path, api_key=api_key)
        elif platform == "xigua":
        cookie = credentials.get("cookie")
        api_instance = XiguaAPI(credentials_path=credentials_path, cookie=cookie)
        elif platform == "toutiao":
        cookie = credentials.get("cookie")
        api_instance = ToutiaoAPI(credentials_path=credentials_path, cookie=cookie)
        elif platform == "xiaohongshu":
        cookie = credentials.get("cookie")
        api_instance = XiaohongshuAPI(credentials_path=credentials_path, cookie=cookie)
        else:
        logger.error(f"不支持的平台: {platform}")
        return None
        if api_instance:
        self._api_instances[platform] = api_instance
        logger.info(f"已创建 {platform} API客户端实例")
        return api_instance
    def authenticate_api(self, platform: str) -> bool:
        api_instance = self.get_api(platform)
        if api_instance:
        success = api_instance.authenticate()
        if success:
            logger.info(f"{platform} API客户端认证成功")
        else:
            logger.error(f"{platform} API客户端认证失败")
        return success
        else:
        logger.error(f"获取 {platform} API客户端实例失败")
        return False
    def authenticate_all(self) -> Dict[str, bool]:
        platforms = ["youtube", "xigua", "toutiao", "xiaohongshu"]
        results = {}
        for platform in platforms:
        results[platform] = self.authenticate_api(platform)
        return results
