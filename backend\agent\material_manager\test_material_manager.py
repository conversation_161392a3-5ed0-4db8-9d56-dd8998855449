import unittest
from material_manager import MaterialManager

class TestMaterialManager(unittest.TestCase):
    def setUp(self):
        self.manager = MaterialManager()

    def test_upload_material(self):
        # 测试上传视频素材
        self.assertTrue(self.manager.upload_material("video1.mp4", "video", ["tag1", "tag2"]))
        self.assertEqual(len(self.manager.materials), 1)
        self.assertEqual(self.manager.materials["1"]["file_path"], "video1.mp4")

        # 测试上传图片素材
        self.assertTrue(self.manager.upload_material("image1.jpg", "image", ["tag3"]))
        self.assertEqual(len(self.manager.materials), 2)
        self.assertEqual(self.manager.materials["2"]["file_path"], "image1.jpg")

    def test_get_material(self):
        self.manager.upload_material("video1.mp4", "video", ["tag1"])
        material = self.manager.get_material("1")
        self.assertIsNotNone(material)
        self.assertEqual(material["file_path"], "video1.mp4")

        # 测试获取不存在的素材
        self.assertIsNone(self.manager.get_material("999"))

    def test_search_materials(self):
        self.manager.upload_material("video1.mp4", "video", ["tag1", "action"])
        self.manager.upload_material("video2.mov", "video", ["tag2", "comedy"])
        self.manager.upload_material("image1.png", "image", ["tag1", "nature"])

        # 按关键词搜索
        results = self.manager.search_materials("video1")
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["file_path"], "video1.mp4")

        # 按类型搜索
        results = self.manager.search_materials("", material_type="image")
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["file_path"], "image1.png")

        # 按标签搜索
        results = self.manager.search_materials("", tags=["tag1"])
        self.assertEqual(len(results), 2)

        # 组合搜索
        results = self.manager.search_materials("video", material_type="video", tags=["action"])
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]["file_path"], "video1.mp4")

    def test_delete_material(self):
        self.manager.upload_material("video1.mp4", "video", ["tag1"])
        self.assertEqual(len(self.manager.materials), 1)

        # 删除存在的素材
        self.assertTrue(self.manager.delete_material("1"))
        self.assertEqual(len(self.manager.materials), 0)

        # 删除不存在的素材
        self.assertFalse(self.manager.delete_material("999"))

if __name__ == '__main__':
    unittest.main()