"""
并行视频处理示例
"""
import os
import sys
import time
import argparse
import logging

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from backend.agent.smart_editor import VideoEditor

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def process_video_sequential(input_path, output_path, effect, **params):
    """顺序处理视频"""
    editor = VideoEditor()
    
    # 加载视频
    start_time = time.time()
    video = editor.load_video(input_path)
    load_time = time.time() - start_time
    
    if video is None:
        logger.error(f"无法加载视频: {input_path}")
        return False
    
    # 应用特效
    start_time = time.time()
    processed = editor.apply_effect(video, effect, **params)
    effect_time = time.time() - start_time
    
    if processed is None:
        logger.error(f"特效应用失败: {effect}")
        video.close()
        return False
    
    # 保存视频
    start_time = time.time()
    result = editor.save_video(processed, output_path)
    save_time = time.time() - start_time
    
    # 关闭视频
    video.close()
    processed.close()
    
    # 打印时间统计
    logger.info(f"顺序处理时间统计:")
    logger.info(f"  加载时间: {load_time:.2f}秒")
    logger.info(f"  特效应用时间: {effect_time:.2f}秒")
    logger.info(f"  保存时间: {save_time:.2f}秒")
    logger.info(f"  总时间: {load_time + effect_time + save_time:.2f}秒")
    
    return result

def process_video_parallel(input_path, output_path, effect, max_workers=None, **params):
    """并行处理视频"""
    editor = VideoEditor()
    
    # 应用特效
    start_time = time.time()
    result = editor.apply_effect_parallel(
        input_path, output_path, effect, 
        max_workers=max_workers, **params
    )
    total_time = time.time() - start_time
    
    # 打印时间统计
    logger.info(f"并行处理时间统计:")
    logger.info(f"  总时间: {total_time:.2f}秒")
    
    return result

def process_large_video(input_path, output_path, max_workers=None):
    """处理大型视频示例"""
    editor = VideoEditor()
    
    # 定义操作列表
    operations = [
        {'type': 'effect', 'name': 'brightness', 'factor': 1.2},
        {'type': 'effect', 'name': 'contrast', 'factor': 1.1},
        {'type': 'effect', 'name': 'saturation', 'factor': 1.2},
        {'type': 'speed', 'factor': 1.5}
    ]
    
    # 处理大型视频
    start_time = time.time()
    result = editor.process_large_video(
        input_path, output_path, operations, 
        max_workers=max_workers
    )
    total_time = time.time() - start_time
    
    # 打印时间统计
    logger.info(f"大型视频处理时间统计:")
    logger.info(f"  总时间: {total_time:.2f}秒")
    
    return result

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='并行视频处理示例')
    parser.add_argument('input', help='输入视频路径')
    parser.add_argument('output', help='输出视频路径')
    parser.add_argument('--mode', choices=['sequential', 'parallel', 'large'], 
                        default='parallel', help='处理模式')
    parser.add_argument('--effect', default='brightness', help='特效名称')
    parser.add_argument('--factor', type=float, default=1.5, help='特效参数')
    parser.add_argument('--workers', type=int, default=None, help='工作进程数')
    
    args = parser.parse_args()
    
    # 确保输出目录存在
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 根据模式处理视频
    if args.mode == 'sequential':
        logger.info(f"使用顺序处理模式处理视频: {args.input}")
        result = process_video_sequential(
            args.input, args.output, args.effect, 
            factor=args.factor
        )
    elif args.mode == 'parallel':
        logger.info(f"使用并行处理模式处理视频: {args.input}")
        result = process_video_parallel(
            args.input, args.output, args.effect, 
            max_workers=args.workers, factor=args.factor
        )
    else:  # large
        logger.info(f"使用大型视频处理模式处理视频: {args.input}")
        result = process_large_video(
            args.input, args.output, 
            max_workers=args.workers
        )
    
    if result:
        logger.info(f"视频处理成功: {args.output}")
    else:
        logger.error(f"视频处理失败")

if __name__ == '__main__':
    main()