# backend.agent.tools.api_clients

"""
API 客户端模块，提供与各种外部 API 的交互接口。

这个模块包含了与各种视频平台、社交媒体、内容分发网络等外部服务交互的客户端实现。
每个客户端类封装了特定 API 的认证、请求和响应处理逻辑，提供了简单易用的接口。

已实现的客户端包括：
- YouTubeAPI: 用于与 YouTube 平台交互，包括视频上传、分析等功能
- XiguaAPI: 用于与西瓜视频平台交互，包括视频上传、分析和收益数据获取等功能
- ToutiaoAPI: 用于与今日头条平台交互，包括文章和视频发布、分析和收益数据获取等功能
- XiaohongshuAPI: 用于与小红书平台交互，包括笔记发布、分析和收益数据获取等功能
"""

from .youtube_api import YouTubeAPI
from .xigua_api import XiguaAPI
from .toutiao_api import ToutiaoAPI
from .xiaohongshu_api import XiaohongshuAPI

__all__ = [
    "YouTubeAPI",
    "XiguaAPI",
    "ToutiaoAPI",
    "XiaohongshuAPI"
]

print("Backend Agent: API Clients module initialized.")