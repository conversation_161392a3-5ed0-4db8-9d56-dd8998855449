import logging
from typing import List
from typing import Tuple

import cv2
import moviepy.editor as mp
import numpy as np

logger = logging.getLogger(__name__)

class VideoSummarizer:
    """视频摘要生成器，用于自动生成视频摘要"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def summarize_by_motion(
        self, video_path: str, summary_duration: float = 30.0, threshold: float = 20.0
    ) -> List[Tuple[float, float]]:
        """
        基于运动强度生成视频摘要

        Args:
            video_path: 视频文件路径
            summary_duration: 摘要时长（秒）
            threshold: 运动检测阈值

        Returns:
            摘要片段列表，每个片段为 (开始时间, 结束时间) 元组
        """
        try:
            # 打开视频
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                self.logger.error("无法打开视频: {video_path}")
                return []

            # 获取视频信息
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            frame_count / fps

            self.logger.info("视频信息: FPS={fps}, 总帧数={frame_count}, 时长={duration}秒")

            # 计算每帧的运动强度
            motion_scores = []
            prev_frame = None

            while True:
                ret, frame = cap.read()
                if not ret:
                    break

                # 转换为灰度图
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

                if prev_frame is not None:
                    # 计算帧差异
                    diff = cv2.absdiff(gray, prev_frame)
                    # 计算运动强度
                    motion_score = float(cv2.mean(diff)[0])
                    motion_scores.append(motion_score)

                prev_frame = gray

            # 释放资源
            cap.release()

            # 如果没有足够的帧
            if len(motion_scores) < 2:
                self.logger.error("视频帧数不足")
                return []

            # 归一化运动分数
            motion_scores = np.array(motion_scores)
            motion_scores = (motion_scores - motion_scores.min()) / (motion_scores.max() - motion_scores.min() + 1e-6)

            # 找出运动强度高的片段
            segments = []
            in_segment = False
            segment_start = 0

            for i, score in enumerate(motion_scores):
                if score > threshold / 100.0 and not in_segment:
                    # 开始新片段
                    segment_start = i
                    in_segment = True
                elif score <= threshold / 100.0 and in_segment:
                    # 结束当前片段
                    segment_end = i
                    segments.append((segment_start / fps, segment_end / fps))
                    in_segment = False

            # 处理最后一个片段
            if in_segment:
                segment_end = len(motion_scores)
                segments.append((segment_start / fps, segment_end / fps))

            # 按运动强度排序片段
            segment_scores = []
            for start, end in segments:
                start_idx = int(start * fps)
                end_idx = min(int(end * fps), len(motion_scores))
                if start_idx < end_idx:
                    avg_score = np.mean(motion_scores[start_idx:end_idx])
                    segment_scores.append((start, end, avg_score))

            # 按分数降序排序
            segment_scores.sort(key=lambda x: x[2], reverse=True)

            # 选择最高分的片段，直到达到摘要时长
            selected_segments = []
            current_duration = 0.0

            for start, end, score in segment_scores:
                segment_duration = end - start
                if current_duration + segment_duration <= summary_duration:
                    selected_segments.append((start, end))
                    current_duration += segment_duration
                    self.logger.debug("选择片段: {start:.2f}s - {end:.2f}s, 分数: {score:.4f}")
                else:
                    # 如果剩余时间不足，截取部分片段
                    remaining_duration = summary_duration - current_duration
                    if remaining_duration > 0:
                        selected_segments.append((start, start + remaining_duration))
                        self.logger.debug(
                            "选择部分片段: {start:.2f}s - {start + remaining_duration:.2f}s, 分数: {score:.4f}"
                        )
                    break

            # 按时间顺序排序
            selected_segments.sort(key=lambda x: x[0])

            self.logger.info("摘要生成完成，共选择 {len(selected_segments)} 个片段，总时长: {current_duration:.2f}秒")
            return selected_segments

        except Exception as e:
            self.logger.error("摘要生成失败: {e}")
            import traceback

            self.logger.error(traceback.format_exc())
            return []

    def create_summary_video(self, video_path: str, output_path: str, segments: List[Tuple[float, float]]) -> bool:
        """
        根据片段列表创建摘要视频

        Args:
            video_path: 原始视频路径
            output_path: 输出视频路径
            segments: 片段列表，每个片段为 (开始时间, 结束时间) 元组

        Returns:
            是否成功创建摘要视频
        """
        try:
            # 加载视频
            video = mp.VideoFileClip(video_path)

            # 提取片段
            clips = []
            for start, end in segments:
                clip = video.subclip(start, end)
                clips.append(clip)

            # 如果没有片段
            if not clips:
                self.logger.error("没有可用片段")
                return False

            # 合并片段
            final_clip = mp.concatenate_videoclips(clips)

            # 添加淡入淡出效果
            final_clip = final_clip.fadein(0.5).fadeout(0.5)

            # 保存视频
            final_clip.write_videofile(
                output_path, codec="libx264", audio_codec="aac", temp_audiofile="temp-audio.m4a", remove_temp=True
            )

            # 释放资源
            video.close()
            final_clip.close()

            self.logger.info("摘要视频已保存: {output_path}")
            return True

        except Exception as e:
            self.logger.error("摘要视频创建失败: {e}")
            import traceback

            self.logger.error(traceback.format_exc())
            return False
