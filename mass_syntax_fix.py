#!/usr/bin/env python3
"""
批量语法修复脚本 - 修复所有被破坏的文件
"""

import subprocess
from pathlib import Path


def fix_file_structure(file_path: Path):
    """修复单个文件的结构"""
    try:
        content = file_path.read_text(encoding='utf-8')
        lines = content.split('\n')
        
        # 如果文件开头就是缩进的内容，说明结构被破坏了
        if lines and lines[0].startswith('    '):
            print(f"修复文件结构: {file_path}")
            
            # 重新构建文件结构
            new_lines = []
            imports = []
            other_code = []
            
            # 分离导入和其他代码
            for line in lines:
                stripped = line.strip()
                
                # 跳过空行和注释
                if not stripped or stripped.startswith('#'):
                    continue
                
                # 导入语句
                if stripped.startswith(('import ', 'from ')):
                    imports.append(stripped)
                # 其他代码
                elif stripped:
                    # 去掉开头的缩进
                    if line.startswith('    '):
                        other_code.append(line[4:])
                    else:
                        other_code.append(line)
            
            # 重新组织文件
            if file_path.suffix == '.py':
                # 添加shebang和编码
                new_lines.append('#!/usr/bin/env python3')
                new_lines.append('"""')
                new_lines.append(f'{file_path.stem} module')
                new_lines.append('"""')
                new_lines.append('')
                
                # 添加导入
                if imports:
                    new_lines.extend(sorted(set(imports)))
                    new_lines.append('')
                
                # 添加其他代码
                new_lines.extend(other_code)
            
            # 写回文件
            new_content = '\n'.join(new_lines)
            file_path.write_text(new_content, encoding='utf-8')
            return True
            
    except Exception as e:
        print(f"修复 {file_path} 失败: {e}")
        return False
    
    return False

def fix_all_syntax_errors():
    """修复所有语法错误"""
    print("🔧 批量修复语法错误...")
    
    # 获取所有Python文件
    python_files = list(Path(".").rglob("*.py"))
    fixed_count = 0
    
    for file_path in python_files:
        if "venv" in str(file_path) or "__pycache__" in str(file_path):
            continue
        
        if fix_file_structure(file_path):
            fixed_count += 1
    
    print(f"✅ 修复了 {fixed_count} 个文件的结构")
    
    # 运行基础的语法修复
    try:
        # 使用autopep8修复基础语法问题
        subprocess.run([
            "python", "-m", "autopep8",
            "--in-place",
            "--recursive", 
            "--max-line-length=120",
            "--aggressive",
            "."
        ], check=False, capture_output=True)
        print("✅ autopep8修复完成")
    except:
        print("⚠️ autopep8修复失败")

def run_simple_fixes():
    """运行简单的修复"""
    print("🧹 运行简单修复...")
    
    # 只运行最基础的工具
    tools = [
        (["autoflake", "--remove-all-unused-imports", "--in-place", "--recursive", "."], "autoflake"),
        (["isort", "--profile", "black", "--line-length", "120", "."], "isort"),
    ]
    
    for command, name in tools:
        try:
            result = subprocess.run(command, check=False, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {name} 完成")
            else:
                print(f"⚠️ {name} 有警告")
        except Exception as e:
            print(f"❌ {name} 失败: {e}")

def get_error_count():
    """获取错误数量"""
    try:
        result = subprocess.run([
            "python", "-m", "flake8",
            "--count",
            "--max-line-length=120",
            "--exclude=venv,__pycache__,.git",
            "."
        ], capture_output=True, text=True)
        
        output = result.stdout.strip()
        if output:
            lines = output.split('\n')
            for line in reversed(lines):
                if line.strip().isdigit():
                    return int(line.strip())
        return 0
    except:
        return -1

def main():
    """主函数"""
    print("🚨 批量语法修复脚本")
    print("=" * 50)
    
    # 获取初始错误数
    initial_errors = get_error_count()
    print(f"📊 初始错误数: {initial_errors}")
    
    # 执行修复
    print("\n🔧 开始批量修复...")
    
    # 1. 修复文件结构
    fix_all_syntax_errors()
    
    # 2. 运行简单修复
    run_simple_fixes()
    
    # 最终检查
    print("\n📊 修复结果:")
    final_errors = get_error_count()
    
    if final_errors >= 0:
        print(f"修复前: {initial_errors} 个错误")
        print(f"修复后: {final_errors} 个错误")
        
        if initial_errors > 0:
            reduction = initial_errors - final_errors
            reduction_percent = (reduction / initial_errors * 100)
            print(f"修复了: {reduction} 个错误 ({reduction_percent:.1f}%)")
        
        if final_errors == 0:
            print("🎉 完美! 所有错误已解决!")
        elif final_errors < 10:
            print("🎊 优秀! 接近完美!")
        elif final_errors < 50:
            print("👍 良好! 大部分错误已解决!")
        else:
            print("⚠️ 仍需继续修复")
            
        # 计算总体修复率
        total_reduction = 635 - final_errors
        total_percent = (total_reduction / 635 * 100)
        print(f"📈 总体修复率: {total_percent:.1f}% (635 → {final_errors})")
    else:
        print("❌ 无法获取准确统计")

if __name__ == "__main__":
    main()
