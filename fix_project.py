#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IntelliCutAgent 项目修复脚本
批量修复常见的类型注解、导入和语法问题
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Optional, <PERSON><PERSON>


def fix_type_annotations(file_path: str) -> int:
    """修复类型注解问题"""
    fixes_count = 0
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复常见的类型注解问题
        patterns = [
            # str = None -> Optional[str] = None
            (r'(\w+): str = None', r'\1: Optional[str] = None'),
            (r'(\w+): int = None', r'\1: Optional[int] = None'),
            (r'(\w+): float = None', r'\1: Optional[float] = None'),
            (r'(\w+): bool = None', r'\1: Optional[bool] = None'),
            (r'(\w+): Dict\[str, Any\] = None', r'\1: Optional[Dict[str, Any]] = None'),
            (r'(\w+): List\[str\] = None', r'\1: Optional[List[str]] = None'),
            (r'(\w+): List\[Any\] = None', r'\1: Optional[List[Any]] = None'),
        ]
        
        for pattern, replacement in patterns:
            new_content = re.sub(pattern, replacement, content)
            if new_content != content:
                fixes_count += len(re.findall(pattern, content))
                content = new_content
        
        # 确保导入了Optional
        if 'Optional[' in content and 'from typing import' in content:
            # 检查是否已经导入了Optional
            if 'Optional' not in content.split('from typing import')[1].split('\n')[0]:
                # 添加Optional到导入
                content = re.sub(
                    r'from typing import ([^, Optional,\n]+)',
                    r'from typing import \1, Optional, Optional',
                    content
                )
                fixes_count += 1
        
        # 只有在内容发生变化时才写入文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ 修复 {file_path}: {fixes_count} 个类型注解问题")
        
        return fixes_count
        
    except Exception as e:
        print(f"✗ 修复 {file_path} 失败: {e}")
        return 0


def fix_import_issues(file_path: str) -> int:
    """修复导入问题"""
    fixes_count = 0
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复常见的导入问题
        patterns = [
            # 添加缺失的导入
            (r'from typing import ([^, Optional,\n]*?)(\n)f', 
             lambda m: f"from typing import {m.group(1)}, Optional, Optional{m.group(2)}" 
             if 'Optional' not in m.group(1) and 'Optional[' in content else m.group(0)),
        ]
        
        for pattern, replacement in patterns:
            if callable(replacement):
                new_content = re.sub(pattern, replacement, content)
            else:
                new_content = re.sub(pattern, replacement, content)
            
            if new_content != content:
                fixes_count += 1
                content = new_content
        
        # 只有在内容发生变化时才写入文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ 修复 {file_path}: {fixes_count} 个导入问题")
        
        return fixes_count
        
    except Exception as e:
        print(f"✗ 修复 {file_path} 失败: {e}")
        return 0


def fix_string_formatting(file_path: str) -> int:
    """修复字符串格式化问题"""
    fixes_count = 0
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复f-string格式化问题
        patterns = [
            # f"text {variable}" -> f"text {variable}"
            (r'"([^f"]*\{[^}]+\}[^"]*)"', r'f"\1"'),
            (r"'([^f']*\{[^}]+\}[^']*)'", r"f'\1'"),
        ]
        
        for pattern, replacement in patterns:
            # 只替换不是已经是f-string的字符串
            matches = re.finditer(pattern, content)
            for match in reversed(list(matches)):
                start, end = match.span()
                # 检查前面是否已经有f
                if start > 0 and content[start-1].lower() == 'f':
                    continue
                # 执行替换
                content = content[:start] + 'f' + content[start:]
                fixes_count += 1
        
        # 只有在内容发生变化时才写入文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ 修复 {file_path}: {fixes_count} 个字符串格式化问题")
        
        return fixes_count
        
    except Exception as e:
        print(f"✗ 修复 {file_path} 失败: {e}")
        return 0


def get_python_files(directory: str) -> List[str]:
    """获取目录下所有Python文件"""
    python_files = []
    
    for root, dirs, files in os.walk(directory):
        # 跳过虚拟环境和缓存目录
        dirs[:] = [d for d in dirs if d not in ['venv', '__pycache__', '.git', 'node_modules']]
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    return python_files


def main():
    """主函数"""
    print("🔧 IntelliCutAgent 项目修复脚本")
    print("=" * 50)
    
    # 获取项目根目录
    project_root = os.path.dirname(os.path.abspath(__file__))
    
    # 获取所有Python文件
    python_files = get_python_files(project_root)
    
    print(f"📁 找到 {len(python_files)} 个Python文件")
    
    total_fixes = 0
    
    # 修复每个文件
    for file_path in python_files:
        relative_path = os.path.relpath(file_path, project_root)
        
        # 跳过一些特殊文件
        if any(skip in relative_path for skip in ['test_', '__pycache__', 'venv']):
            continue
        
        print(f"\n🔍 检查文件: {relative_path}")
        
        # 修复类型注解
        fixes = fix_type_annotations(file_path)
        total_fixes += fixes
        
        # 修复导入问题
        fixes = fix_import_issues(file_path)
        total_fixes += fixes
        
        # 修复字符串格式化
        fixes = fix_string_formatting(file_path)
        total_fixes += fixes
    
    print("\n" + "=" * 50)
    print(f"🎉 修复完成！总共修复了 {total_fixes} 个问题")
    
    # 提供下一步建议
    print("\n📋 建议的下一步操作:")
    print("1. 运行 'python main.py' 测试核心功能")
    print("2. 检查IDE中剩余的错误")
    print("3. 运行单元测试（如果有的话）")


if __name__ == "__main__":
    main()
