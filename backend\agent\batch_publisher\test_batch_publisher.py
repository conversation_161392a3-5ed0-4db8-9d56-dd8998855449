#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BatchPublisher 测试文件
测试批量发布功能
"""

import os
import sys
import unittest

# 添加项目根目录到路径
current_dir = os.path.dirname(__file__)
backend_dir = os.path.join(current_dir, "..", "..")
sys.path.insert(0, os.path.abspath(backend_dir))

from batch_publisher import BatchPublisher


class TestBatchPublisher(unittest.TestCase):
    """测试BatchPublisher类的功能"""

    def setUp(self):
        """在每个测试方法执行前设置"""
        self.publisher = BatchPublisher()
        self.test_video_path = "/path/to/test_final_video.mp4"
        self.test_metadata = {
            "title": "Test Video",
            "description": "A test video for publishing.",
            "tags": ["test", "video"],
        }

    def test_publish_to_platforms(self):
        """测试批量发布到平台功能"""
        platforms = ["douyin", "kuaishou", "bilibili", "youtube"]
        results = self.publisher.publish_to_platforms(self.test_video_path, platforms, self.test_metadata)
        
        # 验证抖音发布结果
        self.assertIn("douyin", results)
        self.assertEqual(results["douyin"]["status"], "success")
        self.assertIn("url", results["douyin"])
        
        # 验证快手发布结果
        self.assertIn("kuaishou", results)
        self.assertEqual(results["kuaishou"]["status"], "success")
        self.assertIn("url", results["kuaishou"])
        
        # 验证B站发布结果
        self.assertIn("bilibili", results)
        self.assertEqual(results["bilibili"]["status"], "success")
        self.assertIn("url", results["bilibili"])
        
        # 验证YouTube发布结果（模拟失败）
        self.assertIn("youtube", results)
        self.assertEqual(results["youtube"]["status"], "failed")
        self.assertIn("error_message", results["youtube"])
        
        print(f"\n测试批量发布到平台: {results}")

    def test_check_publish_status(self):
        """测试检查发布状态功能"""
        # 模拟抖音发布ID
        mock_publish_id_douyin = f"mock_video_id_douyin_{hash(self.test_video_path)}"
        status_douyin = self.publisher.check_publish_status(mock_publish_id_douyin)
        self.assertEqual(status_douyin["status"], "completed")
        self.assertIn("douyin", status_douyin["platforms"])
        
        # 模拟快手发布ID
        mock_publish_id_kuaishou = f"mock_video_id_kuaishou_{hash(self.test_video_path)}"
        status_kuaishou = self.publisher.check_publish_status(mock_publish_id_kuaishou)
        self.assertEqual(status_kuaishou["status"], "processing")
        self.assertIn("kuaishou", status_kuaishou["platforms"])
        
        # 测试不存在的发布ID
        non_existent_id = "non_existent_publish_id"
        status_non_existent = self.publisher.check_publish_status(non_existent_id)
        self.assertEqual(status_non_existent["status"], "failed")
        
        print(f"\n测试检查发布状态 (抖音): {status_douyin}")
        print(f"测试检查发布状态 (快手): {status_kuaishou}")
        print(f"测试检查发布状态 (不存在ID): {status_non_existent}")

    def test_get_supported_platforms(self):
        """测试获取支持的平台列表"""
        platforms = self.publisher.get_supported_platforms()
        self.assertIsInstance(platforms, list)
        self.assertIn("douyin", platforms)
        self.assertIn("kuaishou", platforms)
        self.assertIn("bilibili", platforms)
        print(f"\n支持的平台: {platforms}")

    def test_validate_metadata(self):
        """测试元数据验证"""
        # 测试有效元数据
        valid_metadata = {
            "title": "Valid Title",
            "description": "Valid description",
            "tags": ["tag1", "tag2"]
        }
        is_valid = self.publisher._validate_metadata(valid_metadata)
        self.assertTrue(is_valid)
        
        # 测试无效元数据（缺少标题）
        invalid_metadata = {
            "description": "Valid description",
            "tags": ["tag1", "tag2"]
        }
        is_valid = self.publisher._validate_metadata(invalid_metadata)
        self.assertFalse(is_valid)
        
        print(f"\n元数据验证测试完成")

    def test_platform_specific_publishing(self):
        """测试特定平台发布"""
        # 测试单个平台发布
        result = self.publisher.publish_to_single_platform(
            "douyin", 
            self.test_video_path, 
            self.test_metadata
        )
        self.assertEqual(result["status"], "success")
        self.assertIn("video_id", result)
        self.assertIn("url", result)
        
        print(f"\n单平台发布测试: {result}")


if __name__ == "__main__":
    unittest.main()
