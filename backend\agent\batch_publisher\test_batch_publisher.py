#!/usr/bin/env python3
"""
test_batch_publisher module
"""

import os
import sys
import unittest

from agent.batch_publisher.batch_publisher import BatchPublisher

"""
测试BatchPublisher类的功能。
"""
    """
    在每个测试方法执行前设置。
    """
    """
    测试批量发布到平台功能。
    """
    """
    测试检查发布状态功能。
    """
current_dir = os.path.dirname(__file__)
backend_dir = os.path.join(current_dir, "..", "..")
sys.path.insert(0, os.path.abspath(backend_dir))
class TestBatchPublisher(unittest.TestCase):
    def setUp(self):
    self.publisher = BatchPublisher()
    self.test_video_path = "/path/to/test_final_video.mp4"
    self.test_metadata = {
        "title": "Test Video",
        "description": "A test video for publishing.",
        "tags": ["test", "video"],
    }
def test_publish_to_platforms(self):
    platforms = ["tiktok", "kuaishou", "bilibili", "youtube"]
    results = self.publisher.publish_to_platforms(self.test_video_path, platforms, self.test_metadata)
    self.assertIn("tiktok", results)
    self.assertEqual(results["tiktok"]["status"], "success")
    self.assertIn("url", results["tiktok"])
    self.assertIn("kuaishou", results)
    self.assertEqual(results["kuaishou"]["status"], "success")
    self.assertIn("url", results["kuaishou"])
    self.assertIn("bilibili", results)
    self.assertEqual(results["bilibili"]["status"], "success")
    self.assertIn("url", results["bilibili"])
    self.assertIn("youtube", results)
    self.assertEqual(results["youtube"]["status"], "failed")
    self.assertIn("error_message", results["youtube"])
    print("\n测试批量发布到平台: {results}")
def test_check_publish_status(self):
    mock_publish_id_tiktok = "mock_video_id_tiktok_{hash(self.test_video_path)}"
    status_tiktok = self.publisher.check_publish_status(mock_publish_id_tiktok)
    self.assertEqual(status_tiktok["status"], "completed")
    self.assertIn("tiktok", status_tiktok["platforms"])
    mock_publish_id_kuaishou = "mock_video_id_kuaishou_{hash(self.test_video_path)}"
    status_kuaishou = self.publisher.check_publish_status(mock_publish_id_kuaishou)
    self.assertEqual(status_kuaishou["status"], "processing")
    self.assertIn("kuaishou", status_kuaishou["platforms"])
    non_existent_id = "non_existent_publish_id"
    status_non_existent = self.publisher.check_publish_status(non_existent_id)
    self.assertEqual(status_non_existent["status"], "failed")
    print("\n测试检查发布状态 (TikTok): {status_tiktok}")
    print("测试检查发布状态 (Kuaishou): {status_kuaishou}")
    print("测试检查发布状态 (不存在ID): {status_non_existent}")
if __name__ == "__main__":
unittest.main()