"""
并行视频处理模块 - 用于优化大型视频文件的处理性能
"""

import logging
import multiprocessing
import os
import tempfile
from typing import Callable, List, Optional, Tuple

import moviepy.editor as mp
import numpy as np

logger = logging.getLogger(__name__)


class VideoChunk:
    """视频分块类，表示视频的一个时间段"""

    def __init__(self, start_time: float, end_time: float, index: int):
        self.start_time = start_time
        self.end_time = end_time
        self.index = index
        self.duration = end_time - start_time
        self.input_path = None
        self.output_path = None

    def __repr__(self):
        return "VideoChunk(index={self.index}, start={self.start_time:.2f}, end={self.end_time:.2f})"


class ParallelVideoProcessor:
    """并行视频处理器，用于优化大型视频文件的处理"""

    def __init__(self, temp_dir: Optional[str] = None, max_workers: Optional[int] = None):
        """
        初始化并行视频处理器

        Args:
            temp_dir: 临时文件目录，如果为None则使用系统临时目录
            max_workers: 最大工作进程数，如果为None则使用CPU核心数
        """
        self.temp_dir = temp_dir or tempfile.gettempdir()
        self.max_workers = max_workers or multiprocessing.cpu_count()
        self.temp_files = []

        # 确保临时目录存在
        os.makedirs(self.temp_dir, exist_ok=True)

        logger.info("初始化并行视频处理器: 最大工作进程数={self.max_workers}, 临时目录={self.temp_dir}")

    def _create_temp_file(self, prefix: str, suffix: str) -> str:
        """创建临时文件并跟踪"""
        fd, path = tempfile.mkstemp(prefix=prefix, suffix=suffix, dir=self.temp_dir)
        os.close(fd)
        self.temp_files.append(path)
        return path

    def cleanup(self):
        """清理临时文件"""
        for file_path in self.temp_files:
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    logger.debug("已删除临时文件: {file_path}")
                except Exception as e:
                    logger.warning("删除临时文件失败: {file_path}, 错误: {e}")

        self.temp_files = []

    def split_video(
        self, video_path: str, chunk_count: Optional[int] = None, chunk_duration: Optional[float] = None
    ) -> List[VideoChunk]:
        """
        将视频分割为多个时间块

        Args:
            video_path: 视频文件路径
            chunk_count: 分块数量，如果为None则根据chunk_duration计算
            chunk_duration: 每个分块的时长（秒），如果为None则根据chunk_count计算

        Returns:
            视频分块列表
        """
        try:
            # 加载视频获取时长
            video = mp.VideoFileClip(video_path)
            total_duration = video.duration
            video.close()

            # 确定分块策略
            if chunk_count is None and chunk_duration is None:
                # 默认分块数为CPU核心数的2倍
                chunk_count = self.max_workers * 2

            if chunk_count is not None:
                # 根据分块数计算每个分块的时长
                chunk_duration = total_duration / chunk_count
            else:
                # 根据分块时长计算分块数
                chunk_count = int(np.ceil(total_duration / chunk_duration))

            # 创建分块
            chunks = []
            for i in range(chunk_count):
                start_time = i * chunk_duration
                end_time = min((i + 1) * chunk_duration, total_duration)

                # 如果是最后一个分块且时长太短，则合并到前一个分块
                if i == chunk_count - 1 and end_time - start_time < chunk_duration * 0.5 and i > 0:
                    chunks[-1].end_time = end_time
                    chunks[-1].duration = chunks[-1].end_time - chunks[-1].start_time
                else:
                    chunk = VideoChunk(start_time, end_time, i)
                    chunks.append(chunk)

            logger.info("视频分块完成: 总时长={total_duration:.2f}秒, 分块数={len(chunks)}")
            return chunks

        except Exception as e:
            logger.error("视频分块失败: {e}")
            import traceback

            logger.error(traceback.format_exc())
            return []

    def extract_video_chunks(self, video_path: str, chunks: List[VideoChunk]) -> List[VideoChunk]:
        """
        提取视频分块到临时文件

        Args:
            video_path: 视频文件路径
            chunks: 视频分块列表

        Returns:
            更新后的视频分块列表，包含临时文件路径
        """
        try:
            # 加载视频
            video = mp.VideoFileClip(video_path)

            # 提取每个分块
            for chunk in chunks:
                # 创建临时文件
                chunk.input_path = self._create_temp_file(prefix="chunk_{chunk.index:03d}_", suffix=".mp4")

                # 提取分块
                subclip = video.subclip(chunk.start_time, chunk.end_time)
                subclip.write_videofile(
                    chunk.input_path,
                    codec="libx264",
                    audio_codec="aac",
                    temp_audiofile=self._create_temp_file(prefix="temp_audio_", suffix=".m4a"),
                    remove_temp=True,
                    logger=None,  # 禁用进度条
                )
                subclip.close()

            # 关闭视频
            video.close()

            logger.info("视频分块提取完成: 共提取 {len(chunks)} 个分块")
            return chunks

        except Exception as e:
            logger.error("视频分块提取失败: {e}")
            import traceback

            logger.error(traceback.format_exc())
            return chunks

    def _process_chunk(self, chunk: VideoChunk, process_func: Callable, **kwargs) -> Tuple[int, str]:
        """
        处理单个视频分块（在工作进程中调用）

        Args:
            chunk: 视频分块
            process_func: 处理函数，接受输入路径、输出路径和其他参数
            **kwargs: 传递给处理函数的其他参数

        Returns:
            (分块索引, 输出文件路径)
        """
        try:
            # 创建输出文件
            chunk.output_path = self._create_temp_file(prefix="processed_{chunk.index:03d}_", suffix=".mp4")

            # 调用处理函数
            success = process_func(chunk.input_path, chunk.output_path, **kwargs)

            if not success:
                logger.warning("分块处理失败: {chunk}")
                return chunk.index, None

            return chunk.index, chunk.output_path

        except Exception as e:
            logger.error("分块处理异常: {chunk}, 错误: {e}")
            import traceback

            logger.error(traceback.format_exc())
            return chunk.index, None

    def process_video_parallel(
        self, video_path: str, output_path: str, process_func: Callable, chunk_count: Optional[int] = None, **kwargs
    ) -> bool:
        """
        并行处理视频

        Args:
            video_path: 输入视频路径
            output_path: 输出视频路径
            process_func: 处理函数，接受输入路径、输出路径和其他参数
            chunk_count: 分块数量，如果为None则自动计算
            **kwargs: 传递给处理函数的其他参数

        Returns:
            处理是否成功
        """
        try:
            # 分割视频
            chunks = self.split_video(video_path, chunk_count=chunk_count)
            if not chunks:
                logger.error("视频分块失败，无法继续处理")
                return False

            # 提取分块
            chunks = self.extract_video_chunks(video_path, chunks)

            # 并行处理分块
            with multiprocessing.Pool(processes=self.max_workers) as pool:
                # 准备任务参数
                tasks = [(chunk, process_func, kwargs) for chunk in chunks]

                # 执行并行处理
                results = []
                for chunk, func, params in tasks:
                    result = pool.apply_async(self._process_chunk, args=(chunk, func), kwds=params)
                    results.append(result)

                # 收集结果
                processed_chunks = {}
                for result in results:
                    chunk_index, output_path = result.get()
                    processed_chunks[chunk_index] = output_path

            # 检查是否所有分块都处理成功
            if None in processed_chunks.values():
                logger.error("部分分块处理失败，无法合并视频")
                return False

            # 按顺序合并分块
            temp_file_list = self._create_temp_file(prefix="file_list_", suffix=".txt")
            with open(temp_file_list, "w") as f:
                for i in range(len(chunks)):
                    if i in processed_chunks and processed_chunks[i]:
                        f.write(f"file '{processed_chunks[i]}'\n")

            # 使用FFmpeg合并视频
            import subprocess

            cmd = ["ffmpeg", "-y", "-", "concat", "-safe", "0", "-i", temp_file_list, "-c", "copy", output_path]
            subprocess.run(cmd, check=True)

            logger.info("视频处理完成: {output_path}")
            return True

        except Exception as e:
            logger.error("并行视频处理失败: {e}")
            import traceback

            logger.error(traceback.format_exc())
            return False
        finally:
            # 清理临时文件
            self.cleanup()

    def apply_effect_to_chunks(self, input_path: str, output_path: str, effect_name: str, **effect_params) -> bool:
        """
        对视频分块应用特效的包装函数

        Args:
            input_path: 输入视频路径
            output_path: 输出视频路径
            effect_name: 特效名称
            **effect_params: 特效参数

        Returns:
            处理是否成功
        """
        try:
            # 导入VideoEditor
            from backend.agent.smart_editor.video_editor import VideoEditor

            # 创建VideoEditor实例
            editor = VideoEditor(temp_dir=self.temp_dir)

            # 加载视频
            video = editor.load_video(input_path)
            if video is None:
                logger.error("无法加载视频: {input_path}")
                return False

            # 应用特效
            processed = editor.apply_effect(video, effect_name, **effect_params)
            if processed is None:
                logger.error("特效应用失败: {effect_name}")
                return False

            # 保存视频
            result = editor.save_video(processed, output_path)

            # 关闭视频
            video.close()
            processed.close()

            return result

        except Exception as e:
            logger.error("特效应用失败: {e}")
            import traceback

            logger.error(traceback.format_exc())
            return False
