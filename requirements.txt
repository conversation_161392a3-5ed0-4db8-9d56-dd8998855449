# IntelliCutAgent - Python Dependencies

# 核心依赖
numpy>=1.20.0
scipy>=1.6.0
pyyaml>=6.0
tqdm>=4.62.0
requests>=2.25.0
python-dotenv>=0.19.0

# 视频处理
moviepy>=1.0.3
ffmpeg-python>=0.2.0
opencv-python>=4.5.0
Pillow>=8.0.0

# 音频处理
librosa>=0.8.0
pydub>=0.25.0
soundfile>=0.10.0
mutagen>=1.45.0

# 自然语言处理
nltk>=3.6.0
spacy>=3.0.0
transformers>=4.5.0

# 机器学习
scikit-learn>=1.0.0
tensorflow>=2.5.0  # 或者使用 torch>=1.9.0

# 数据处理
pandas>=1.3.0
matplotlib>=3.4.0

# API服务 (可选)
fastapi>=0.68.0
uvicorn>=0.15.0
pydantic>=1.8.0

# API客户端
google-api-python-client>=2.23.0
google-auth>=2.3.0
google-auth-oauthlib>=0.4.6
google-auth-httplib2>=0.1.0
requests-toolbelt>=0.9.1

# 其他工具
colorlog>=6.6.0
psutil>=5.8.0
humanize>=3.11.0

# 注意: 
# 1. 某些库 (如 ffmpeg) 可能还需要系统级别的安装。
# 2. 根据实际部署环境，可能需要调整版本号。
# 3. 对于GPU加速，可能需要安装特定版本的CUDA和cuDNN。


# 新增依赖
seaborn>=0.13.0
