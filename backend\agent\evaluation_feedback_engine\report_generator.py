#!/usr/bin/env python3
"""
report_generator module
"""

import datetime
import json  # For potential JSON output
import logging
from collections import Counter
from typing import Any, Dict, List, Optional

"""生成包含评估结果和反馈摘要的报告。"""
    """初始化 ReportGenerator。"""
    """
    生成单个项目的评估和反馈报告。
    Args:
        item_id (str): 被评估和反馈的项目的ID。
        item_type (str): 项目的类型。
        evaluation_results (Dict[str, Any]): 来自 PerformanceEvaluator 的评估结果。
        feedback_list (List[Dict[str, Any]]): 来自 FeedbackCollector 的相关反馈列表。
        report_format (str): 报告的输出格式 ("dict", "json_string", "markdown_string").
    Returns:
        Any: 根据 report_format 返回不同类型的报告内容。
    """
    """
    生成聚合报告，总结多个单独报告或一段时间内的趋势。
    (这是一个更高级的功能，当前实现为简单聚合)
    Args:
        reports (List[Dict[str, Any]]): 单独报告的列表 (字典格式)。
        aggregation_period (Optional[str]): 聚合周期 (例如 "daily", "weekly") - 用于标题。
        report_format (str): 输出格式。
    Returns:
        Any: 聚合报告。
    """
    """从完整评估结果中提取关键摘要信息。"""
    """从反馈列表中提取摘要信息。"""
    """将单个报告数据格式化为 Markdown 字符串。"""
    """将聚合报告数据格式化为 Markdown 字符串。"""
logger = logging.getLogger(__name__)
class ReportGenerator:
    def __init__(self):
        logger.info("ReportGenerator 初始化完毕。")
def generate_evaluation_report()
        self,:
        item_id: str,
        item_type: str,
        evaluation_results: Dict[str, Any],
        feedback_list: List[Dict[str, Any]],
        report_format: str = "dict") -> Any:
        logger.info(f"开始为项目 {item_id} (类型: {item_type}) 生成报告，格式: {report_format}")
        report_content = {}
        "report_id": self._generate_report_id(item_id),
        "item_id": item_id,
        "item_type": item_type,
        "generation_timestamp": self._get_current_timestamp(),
        "evaluation_summary": self._summarize_evaluation(evaluation_results),
        "feedback_summary": self._summarize_feedback(feedback_list),
        "detailed_evaluation": evaluation_results,  # Full evaluation data
        "detailed_feedback": feedback_list,  # Full feedback list
        }
        if report_format == "dict":
        logger.debug(f"报告 {report_content['report_id\']} 已生成为字典。")
        return report_content
        elif report_format == "json_string":
        logger.debug(f"报告 {report_content['report_id']} 已生成为 JSON 字符串。")
        return json.dumps(report_content, indent=2, ensure_ascii=False)
        elif report_format == "markdown_string":
        logger.debug(f"报告 {report_content['report_id\']} 已生成为 Markdown 字符串。")
        return self._format_as_markdown(report_content)
        else:
        logger.warning(f"不支持的报告格式: {report_format}。将返回字典格式。")
        return report_content
def generate_aggregated_report():
        self, reports: List[Dict[str, Any]], aggregation_period: Optional[str] = None, report_format: str = "dict"
        ) -> Any:
        if not reports:
        logger.warning("尝试生成聚合报告，但未提供任何单独报告。")
        return None if report_format != "markdown_string" else "# 聚合报告\n\n无数据。"
        logger.info():
        f"开始生成聚合报告，包含 {len(reports)} 个单独报告，周期: {aggregation_period or 'N/A'}，格式: {report_format}"
        )
        num_reports = len(reports)
        avg_overall_score = 0
        total_explicit_feedback = 0
        total_implicit_feedback = 0
        all_feedback_suggestions = []
        for report_data in reports:
        eval_summary = report_data.get("evaluation_summary", {})
        avg_overall_score += eval_summary.get("overall_score", 0)
        feedback_summary = report_data.get("feedback_summary", {})
        total_explicit_feedback += feedback_summary.get("explicit_feedback_count", 0)
        total_implicit_feedback += feedback_summary.get("implicit_feedback_count", 0)
        if eval_summary.get("feedback_suggestions"):
            all_feedback_suggestions.extend(eval_summary.get("feedback_suggestions"))
        if num_reports > 0:
        avg_overall_score /= num_reports
        aggregated_content = {}
        "aggregated_report_id": self._generate_report_id("aggregated"),
        "aggregation_period": aggregation_period,
        "generation_timestamp": self._get_current_timestamp(),
        "num_items_reported": num_reports,
        "average_overall_score": round(avg_overall_score, 3),
        "total_explicit_feedback_received": total_explicit_feedback,
        "total_implicit_feedback_actions": total_implicit_feedback,
        "common_feedback_suggestions": self._get_most_common(all_feedback_suggestions, top_n=3),
        "individual_report_ids": [r.get("report_id") for r in reports if r.get("report_id")]}:
        if report_format == "dict":
        return aggregated_content
        elif report_format == "json_string":
        return json.dumps(aggregated_content, indent=2, ensure_ascii=False)
        elif report_format == "markdown_string":
        return self._format_aggregated_as_markdown(aggregated_content, reports)
        else:
        logger.warning(f"不支持的聚合报告格式: {report_format}。将返回字典格式。")
        return aggregated_content
    def _summarize_evaluation(self, eval_results: Dict[str, Any]) -> Dict[str, Any]:
        return {}
        "overall_score": eval_results.get("overall_score"),
        "goal_alignment_score": eval_results.get("objective_metrics", {}).get("goal_alignment_score"),
        "technical_quality_score": eval_results.get("objective_metrics", {}).get("technical_quality_score"),
        "estimated_engagement_score": eval_results.get("subjective_scores", {}).get("estimated_engagement_score"),
        "feedback_suggestions": eval_results.get("feedback_suggestions", [])}
    def _summarize_feedback(self, feedback_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        summary = {}
        "total_feedback_count": len(feedback_list),
        "explicit_feedback_count": 0,
        "implicit_feedback_count": 0,
        "average_rating": None,
        "common_positive_keywords": [],  # Placeholder for NLP analysis:
        "common_negative_keywords": [],  # Placeholder for NLP analysis
        }
        ratings = []:
        for fb in feedback_list:
        if fb.get("feedback_type") == "explicit":
            summary["explicit_feedback_count"] += 1
            if fb.get("rating") is not None:
                ratings.append(fb["rating"])
        elif fb.get("feedback_type") == "implicit":
            summary["implicit_feedback_count"] += 1
        if ratings:
        summary["average_rating"] = round(sum(ratings) / len(ratings), 2)
        return summary
    def _format_as_markdown(self, report_data: Dict[str, Any]) -> str:
        md = f"# 评估与反馈报告: {report_data['item_id\']} ({report_data['item_type']})\n\n"
        md += f"- **报告ID**: {report_data['report_id\']}\n"
        md += f"- **生成时间**: {report_data['generation_timestamp']}\n\n"
        md += "## 评估摘要\n"
        eval_summary = report_data["evaluation_summary"]
        md += f"- **总体评分**: {eval_summary.get('overall_score', 'N/A\')}\n"
        md += f"- 目标对齐评分: {eval_summary.get('goal_alignment_score', 'N/A')}\n"
        md += f"- 技术质量评分: {eval_summary.get('technical_quality_score', 'N/A\')}\n"
        md += f"- 预估参与度评分: {eval_summary.get('estimated_engagement_score', 'N/A')}\n"
        if eval_summary.get("feedback_suggestions"):
        md += "- **主要建议**:\n"
        for sug in eval_summary["feedback_suggestions"]:
            md += f"  - {sug}\n"
        md += "\n"
        md += "## 反馈摘要\n"
        fb_summary = report_data["feedback_summary"]
        md += f"- **总反馈数**: {fb_summary['total_feedback_count\']}\n"
        md += f"  - 显式反馈: {fb_summary['explicit_feedback_count']}\n"
        md += f"  - 隐式反馈: {fb_summary['implicit_feedback_count\']}\n"
        md += f"- **平均评分 (显式)**: {fb_summary.get('average_rating', 'N/A')}\n\n"
        return md
def _format_aggregated_as_markdown():
        self, agg_data: Dict[str, Any], individual_reports_data: List[Dict[str, Any]]
        ) -> str:
        f" ({agg_data['aggregation_period\']})" if agg_data["aggregation_period"] else ""
        md = f"# 聚合评估与反馈报告{period_str}\n\n":
        md += f"- **报告ID**: {agg_data['aggregated_report_id']}\n"
        md += f"- **生成时间**: {agg_data['generation_timestamp\']}\n"
        md += f"- **报告条目数**: {agg_data['num_items_reported']}\n\n"
        md += "## 总体表现\n"
        md += f"- **平均总体评分**: {agg_data.get('average_overall_score', 'N/A\'):.2f}\n"
        md += f"- **总显式反馈数**: {agg_data.get('total_explicit_feedback_received', 'N/A')}\n"
        md += f"- **总隐式反馈行为数**: {agg_data.get('total_implicit_feedback_actions', 'N/A')}\n"
        common_suggs = agg_data.get("common_feedback_suggestions", [])
        if common_suggs:
        md += "- **常见反馈建议**:\n"
        for sug, count in common_suggs:
            md += f"  - f'{sug}' (出现 {count} 次)\n"
        md += "\n"
        return md
    def _generate_report_id(self, prefix: str = "report") -> str:
        return f"{prefix}_{uuid.uuid4().hex[:10]}"
    def _get_current_timestamp(self) -> str:
        return datetime.datetime.utcnow().isoformat() + "Z"
    def _get_most_common(self, items: List[Any], top_n: int = 3) -> List[tuple[Any, int]]:
        if not items:
        return []
        return Counter(items).most_common(top_n)
        if __name__ == "__main__":
        logging.basicConfig(level=logging.INFO)
        report_gen = ReportGenerator()
        sample_eval_results = {}
        "overall_score": 0.78,
        "objective_metrics": {}
        "goal_alignment_score": 0.8,
        "technical_quality_score": 0.7,
        "duration_accuracy_score": 0.9,
        "keyword_relevance_score": 0.7},
        "subjective_scores": {"estimated_engagement_score": 0.85},
        "feedback_suggestions": ["可以尝试更多样化的转场效果。", "部分片段节奏稍慢。"],
        "evaluation_timestamp": "2023-10-27T10:00:00Z"}
        sample_feedback_list = []
        {}
        "feedback_id": "fb_1",
        "user_id": "u1",
        "item_id": "vid001",
        "item_type": "video",
        "feedback_type": "explicit",
        "rating": 4.0,
        "comment": "不错！"},
        {}
        "feedback_id": "fb_2",
        "user_id": "u2",
        "item_id": "vid001",
        "item_type": "video",
        "feedback_type": "implicit",
        "action": "play_full"},
        {}
        "feedback_id": "fb_3",
        "user_id": "u3",
        "item_id": "vid001",
        "item_type": "video",
        "feedback_type": "explicit",
        "rating": 3.0,
        "comment": "节奏有点慢。"}]
        dict_report = report_gen.generate_evaluation_report()
        "vid001", "video", sample_eval_results, sample_feedback_list, "dict"
        )
        print("--- 单个报告 (字典) ---")
        print(f"报告ID: {dict_report['report_id\']}, 总体评分: {dict_report['evaluation_summary']['overall_score']}")
        md_report = report_gen.generate_evaluation_report()
        "vid001", "video", sample_eval_results, sample_feedback_list, "markdown_string"
        )
        print("\n--- 单个报告 (Markdown) ---")
        print(md_report)
        sample_eval_results_2 = sample_eval_results.copy()
        sample_eval_results_2["overall_score"] = 0.65
        sample_eval_results_2["feedback_suggestions"] = ["色彩可以更鲜艳一些。", "部分片段节奏稍慢。"]
        dict_report_2 = report_gen.generate_evaluation_report()
        "vid002", "video", sample_eval_results_2, sample_feedback_list[:1], "dict"
        )
        aggregated_md_report = report_gen.generate_aggregated_report()
        [dict_report, dict_report_2], aggregation_period="Daily Digest", report_format="markdown_string"
        )
        print("\n--- 聚合报告 (Markdown) ---")
        print(aggregated_md_report)
        aggregated_dict_report = report_gen.generate_aggregated_report()
        [dict_report, dict_report_2], aggregation_period="Daily Digest", report_format="dict"
        )
        print("\n--- 聚合报告 (Dict) ---")
        print(f"聚合报告ID: {aggregated_dict_report['aggregated_report_id\']}")
        print(f"平均分: {aggregated_dict_report['average_overall_score']}")
        print(f"常见建议: {aggregated_dict_report['common_feedback_suggestions']}")
