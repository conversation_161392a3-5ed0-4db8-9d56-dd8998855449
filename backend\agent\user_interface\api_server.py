#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
IntelliCutAgent API 服务器接口
提供 RESTful API 接口，允许通过 HTTP 请求使用 IntelliCutAgent 的功能
"""

import json
import logging
import os
from typing import Any, Dict, List, Optional

import uvicorn
from fastapi import BackgroundTasks, Depends, FastAPI, File, Form, HTTPException, UploadFile
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from pydantic import BaseModel, Field

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# 定义数据模型
class AnalysisRequest(BaseModel):
    analysis_types: List[str] = Field(default=["basic", "scene_detection"], description="分析类型列表")

class EditRequest(BaseModel):
    duration: str = Field(default="60s", description="目标时长，例如: 60s, 5min")
    style: str = Field(default="standard", description="编辑风格")
    transitions: List[str] = Field(default=["fade"], description="转场效果列表")
    effects: List[str] = Field(default=[], description="视频效果列表")

class PublishRequest(BaseModel):
    platforms: List[str] = Field(..., description="目标平台列表")
    title: str = Field(..., description="视频标题")
    description: Optional[str] = Field(None, description="视频描述")
    tags: Optional[List[str]] = Field(None, description="视频标签")
    schedule_time: Optional[str] = Field(None, description="计划发布时间")

class CommandRequest(BaseModel):
    command: str = Field(..., description="命令字符串或JSON")

class PlatformAnalysisRequest(BaseModel):
    platforms: List[str] = Field(..., description="平台列表")
    content_types: Optional[List[str]] = Field(None, description="内容类型列表")
    time_period: str = Field(default="monthly", description="时间周期")

class VideoRevenueRequest(BaseModel):
    video_id: str = Field(..., description="视频ID")
    platform: str = Field(..., description="平台名称")
    content_type: str = Field(..., description="内容类型")
    metrics: Dict[str, Any] = Field(..., description="指标数据")

class OptimizationStrategyRequest(BaseModel):
    user_id: str = Field(..., description="用户ID")
    target_platforms: Optional[List[str]] = Field(None, description="目标平台列表")
    content_preferences: Optional[List[str]] = Field(None, description="内容偏好列表")

class RevenueTrendsRequest(BaseModel):
    user_id: str = Field(..., description="用户ID")
    time_periods: Optional[List[str]] = Field(None, description="时间周期列表")
    platforms: Optional[List[str]] = Field(None, description="平台列表")

class BatchProcessRequest(BaseModel):
    video_paths: List[str] = Field(..., description="视频文件路径列表")
    process_type: str = Field(default="edit", description="处理类型: analyze, edit, edit_and_publish")
    edit_rules: Optional[Dict[str, Any]] = Field(None, description="编辑规则")
    platforms: Optional[List[str]] = Field(None, description="目标平台列表")
    output_dir: Optional[str] = Field(None, description="输出目录")

class MaintenanceRequest(BaseModel):
    maintenance_type: str = Field(default="full", description="维护类型: full, cache, data, model")
    days_to_keep: int = Field(default=30, description="保留数据的天数")

# 创建 FastAPI 应用
    app = FastAPI(title="IntelliCutAgent API", description="智能视频剪辑代理系统 API 接口", version="0.1.0")

# 配置 CORS
    app.add_middleware()
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源，生产环境应该限制
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"])

# 后台任务
    background_tasks = {}

# 全局变量
    agent_core = None

# 依赖项
    def get_agent_core():
        if agent_core is None:
        raise HTTPException(status_code=500, detail="代理核心未初始化")
        return agent_core

# API 路由
        @app.get("/f")
        async def root():
        return {"name": "IntelliCutAgent API", "version": "0.1.0", "description": "智能视频剪辑代理系统 API 接口"}

        @app.post("/api/command")
        async def execute_command(request: CommandRequest, agent=Depends(get_agent_core)):
        try:
        result = await agent.process_request(request.command)
        return result
        except Exception as e:
        logger.error("操作失败")
        raise HTTPException(status_code=500, detail=f"命令执行失败: {e}")

        @app.post("/api/analyze/video")
        async def analyze_video():
        background_tasks: BackgroundTasks,
        video: UploadFile = File(...),
        request: str = Form(...),
        agent=Depends(get_agent_core)):
        try:
        # 解析请求参数
        request_data = json.loads(request)
        analysis_request = AnalysisRequest(**request_data)

        # 保存上传的视频文件
        temp_dir = os.path.join(os.getcwd(), "temp")
        os.makedirs(temp_dir, exist_ok=True)

        filename = video.filename or f"uploaded_video_{hash(str(video))}.mp4"
        video_path = os.path.join(temp_dir, filename)
        with open(video_path, "wb") as f:
            f.write(await video.read())

        # 创建任务ID
        task_id = f"analyze_{os.path.basename(video_path)}_{hash(str(analysis_request.analysis_types))}"

        # 构建请求
        command = {}
            "action": "analyze_video",
            "params": {"video_path": video_path, "analysis_types": analysis_request.analysis_types}}

        # 添加后台任务
        async def process_task():
            try:
                result = await agent.process_request(command)
                background_tasks[task_id] = {"status": "completed", "result": result}
            except Exception as e:
                logger.error(f"视频分析任务 {task_id} 失败: {e}")
                background_tasks[task_id] = {"status": "failed", "error": str(e)}

        background_tasks.add_task(process_task)

        # 初始化任务状态
        background_tasks[task_id] = {}
            "status": "processing",
            "video_path": video_path,
            "analysis_types": analysis_request.analysis_types}

        return {"task_id": task_id, "status": "processing", "message": "视频分析任务已提交"}
        except Exception as e:
        logger.error("操作失败")
        raise HTTPException(status_code=500, detail=f"提交视频分析任务失败: {e}")

        @app.get("/api/analyze/video/status/{task_id}")
        async def get_video_analysis_status(task_id: str):
        if task_id not in background_tasks:
        raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")

        task_status = background_tasks[task_id]

        return {"task_id": task_id, "status": task_status.get("status", "unknown")}

        @app.get("/api/analyze/video/result/{task_id}")
        async def get_video_analysis_result(task_id: str):
        if task_id not in background_tasks:
        raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")

        task_status = background_tasks[task_id]

        if task_status.get("status") != "completed":
        return {"task_id": task_id, "status": task_status.get("status", "unknown"), "message": "任务尚未完成"}

        return {"task_id": task_id, "status": "completed", "result": task_status.get("result", {})}

        @app.post("/api/edit/video")
        async def edit_video():
        background_tasks: BackgroundTasks,
        video: UploadFile = File(...),
        request: str = Form(...),
        agent=Depends(get_agent_core)):
        try:
        # 解析请求参数
        request_data = json.loads(request)
        edit_request = EditRequest(**request_data)

        # 保存上传的视频文件
        temp_dir = os.path.join(os.getcwd(), "temp")
        os.makedirs(temp_dir, exist_ok=True)

        filename = video.filename or f"uploaded_video_{hash(str(video))}.mp4"
        video_path = os.path.join(temp_dir, filename)
        with open(video_path, "wb") as f:
            f.write(await video.read())

        # 创建任务ID
        task_id = f"edit_{os.path.basename(video_path)}_{hash(str(edit_request.model_dump()))}"

        # 构建请求
        command = {}
            "action": "create_and_publish_video",
            "params": {"material_path": video_path, "platforms": [], "edit_rules": edit_request.model_dump()},  # 不发布
        }

        # 添加后台任务
        async def process_task():
            try:
                result = await agent.process_request(command)
                background_tasks[task_id] = {"status": "completed", "result": result}
            except Exception as e:
                logger.error(f"视频编辑任务 {task_id} 失败: {e}")
                background_tasks[task_id] = {"status": "failed", "error": str(e)}

        background_tasks.add_task(process_task)

        # 初始化任务状态
        background_tasks[task_id] = {}
            "status": "processing",
            "video_path": video_path,
            "edit_request": edit_request.model_dump()}

        return {"task_id": task_id, "status": "processing", "message": "视频编辑任务已提交"}
        except Exception as e:
        logger.error("操作失败")
        raise HTTPException(status_code=500, detail=f"提交视频编辑任务失败: {e}")

        @app.get("/api/edit/video/status/{task_id}")
        async def get_video_editing_status(task_id: str):
        if task_id not in background_tasks:
        raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")

        task_status = background_tasks[task_id]

        return {"task_id": task_id, "status": task_status.get("status", "unknown")}

        @app.get("/api/edit/video/result/{task_id}")
        async def get_video_editing_result(task_id: str):
        if task_id not in background_tasks:
        raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")

        task_status = background_tasks[task_id]

        if task_status.get("status") != "completed":
        return {"task_id": task_id, "status": task_status.get("status", "unknown"), "message": "任务尚未完成"}

        return {"task_id": task_id, "status": "completed", "result": task_status.get("result", {})}

        @app.get("/api/edit/video/download/{task_id}")
        async def download_edited_video(task_id: str):
        if task_id not in background_tasks:
        raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")

        task_status = background_tasks[task_id]

        if task_status.get("status") != "completed":
        raise HTTPException(status_code=400, detail="任务尚未完成")

        result = task_status.get("result", {})
        output_path = result.get("output_path")

        if not output_path or not os.path.exists(output_path):
        raise HTTPException(status_code=404, detail="编辑后的视频文件不存在")

        return FileResponse(output_path, filename=os.path.basename(output_path))

        @app.post("/api/publish/video")
        async def publish_video(video: UploadFile = File(...), request: str = Form(...), agent=Depends(get_agent_core)):
        try:
        # 解析请求参数
        request_data = json.loads(request)
        publish_request = PublishRequest(**request_data)

        # 保存上传的视频文件
        temp_dir = os.path.join(os.getcwd(), "temp")
        os.makedirs(temp_dir, exist_ok=True)

        filename = video.filename or f"uploaded_video_{hash(str(video))}.mp4"
        video_path = os.path.join(temp_dir, filename)
        with open(video_path, "wb") as f:
            f.write(await video.read())

        # 构建请求
        command = {}
            "action": "create_and_publish_video",
            "params": {}
                "material_path": video_path,
                "platforms": publish_request.platforms,
                "title": publish_request.title,
                "description": publish_request.description
                or "由IntelliCutAgent自动发布的视频: {publish_request.title}",
                "tags": publish_request.tags or ["IntelliCutAgent", "自动发布"],
                "schedule_time": publish_request.schedule_time}}

        # 处理请求
        result = await agent.process_request(command)

        return result
        except Exception as e:
        logger.error("操作失败")
        raise HTTPException(status_code=500, detail=f"视频发布失败: {e}")

        @app.post("/api/revenue/platform")
        async def analyze_platform_revenue(request: PlatformAnalysisRequest, agent=Depends(get_agent_core)):
        try:
        # 构建请求
        command = {}
            "action": "analyze_platform_potential",
            "params": {}
                "platforms": request.platforms,
                "content_types": request.content_types or [],
                "time_period": request.time_period}}

        # 处理请求
        result = await agent.process_request(command)

        return result
        except Exception as e:
        logger.error("操作失败")
        raise HTTPException(status_code=500, detail=f"平台收益潜力分析失败: {e}")

        @app.post("/api/revenue/video")
        async def analyze_video_revenue(request: VideoRevenueRequest, agent=Depends(get_agent_core)):
        try:
        # 保存指标数据到临时文件
        temp_dir = os.path.join(os.getcwd(), "temp")
        os.makedirs(temp_dir, exist_ok=True)

        metrics_file = os.path.join(temp_dir, f"metrics_{request.video_id}.json")
        with open(metrics_file, "w", encoding="utf-8") as f:
            json.dump(request.metrics, f, ensure_ascii=False, indent=2)

        # 构建请求
        command = {}
            "action": "analyze_video_revenue",
            "params": {}
                "video_id": request.video_id,
                "platform": request.platform,
                "content_type": request.content_type,
                "metrics_file": metrics_file}}

        # 处理请求
        result = await agent.process_request(command)

        return result
        except Exception as e:
        logger.error("操作失败")
        raise HTTPException(status_code=500, detail=f"视频收益表现分析失败: {e}")

        @app.post("/api/revenue/strategy")
        async def generate_optimization_strategy(request: OptimizationStrategyRequest, agent=Depends(get_agent_core)):
        try:
        # 构建请求
        command = {}
            "action": "generate_optimization_strategy",
            "params": {}
                "user_id": request.user_id,
                "platforms": request.target_platforms or [],
                "content_preferences": request.content_preferences or []}}

        # 处理请求
        result = await agent.process_request(command)

        return result
        except Exception as e:
        logger.error("操作失败")
        raise HTTPException(status_code=500, detail=f"收益优化策略生成失败: {e}")

        @app.post("/api/revenue/trends")
        async def track_revenue_trends(request: RevenueTrendsRequest, agent=Depends(get_agent_core)):
        try:
        # 构建请求
        command = {}
            "action": "track_revenue_trends",
            "params": {}
                "user_id": request.user_id,
                "time_periods": request.time_periods or ["weekly", "monthly", "quarterly"],
                "platforms": request.platforms or []}}

        # 处理请求
        result = await agent.process_request(command)

        return result
        except Exception as e:
        logger.error("操作失败")
        raise HTTPException(status_code=500, detail=f"收益趋势跟踪失败: {e}")

        @app.post("/api/batch/process")
        async def batch_process_videos(request: BatchProcessRequest, agent=Depends(get_agent_core)):
        try:
        # 构建请求
        command = {}
            "action": "batch_process_videos",
            "params": {}
                "video_paths": request.video_paths,
                "process_type": request.process_type,
                "edit_rules": request.edit_rules or {"duration": "60s", "style": "standard"},
                "platforms": request.platforms or [],
                "output_dir": request.output_dir}}

        # 处理请求
        result = await agent.process_request(command)

        return result
        except Exception as e:
        logger.error("操作失败")
        raise HTTPException(status_code=500, detail=f"批量处理视频失败: {e}")

        @app.post("/api/maintenance")
        async def system_maintenance(request: MaintenanceRequest, agent=Depends(get_agent_core)):
        try:
        # 构建请求
        command = {}
            "action": "system_maintenance",
            "params": {"maintenance_type": request.maintenance_type, "days_to_keep": request.days_to_keep}}

        # 处理请求
        result = await agent.process_request(command)

        return result
        except Exception as e:
        logger.error("操作失败")
        raise HTTPException(status_code=500, detail=f"系统维护失败: {e}")

        async def run_api_server(agent_core_param, host="127.0.0.1", port=8000):
        global agent_core
        agent_core = agent_core_param

        config = uvicorn.Config(app, host=host, port=port)
        server = uvicorn.Server(config)
        await server.serve()

        if __name__ == "__main__":
    # 测试代码

        uvicorn.run(app, host="127.0.0.1", port=8000)
