#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
平台数据分析示例脚本

这个脚本展示了如何使用API工厂和配置加载器获取和分析各平台的数据。
"""

import argparse
import datetime
import json
import logging
import os
import sys
from typing import Any, Dict, List, Optional

import matplotlib.pyplot as plt
import pandas as pd

# 添加项目根目录到 Python 路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# 导入API工厂
from backend.agent.tools.api_factory import APIFactory

# 导入配置加载器
from backend.utils.config_loader import ConfigLoader

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


def authenticate_platforms(api_factory: APIFactory, platforms: List[str]) -> Dict[str, bool]:
    """
    认证指定平台

    Args:
        api_factory: API工厂实例
        platforms: 平台列表

    Returns:
        认证结果
    """
    results = {}

    for platform in platforms:
        results[platform] = api_factory.authenticate_api(platform)

    return results


def get_platform_revenue(
    api_factory: APIFactory, platform: str, start_date: Optional[str] = None, end_date: Optional[str] = None
) -> Dict[str, Any]:
    """
    获取指定平台的收益数据

    Args:
        api_factory: API工厂实例
        platform: 平台名称
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)

    Returns:
        收益数据
    """
    # 设置默认日期范围
    if end_date is None:
        end_date = datetime.datetime.now().strftime("%Y-%m-%d")
    if start_date is None:
        # 默认获取最近 30 天的数据
        start_date = (datetime.datetime.now() - datetime.timedelta(days=30)).strftime("%Y-%m-%d")

    # 获取API客户端
    api = api_factory.get_api(platform)

    if not api:
        logger.error(f"获取 {platform} API客户端失败")
        return {"success": False, "error": "获取 {platform} API客户端失败"}

    # 获取收益数据
    if platform == "youtube":
        # 获取频道ID
        config_loader = ConfigLoader()
        credentials = config_loader.load_credentials(platform)
        channel_id = credentials.get("channel_id", "UC1234567890abcdefghij")

        # 获取频道分析数据
        analytics = api.get_channel_analytics(
            channel_id=channel_id,
            start_date=start_date,
            end_date=end_date,
            metrics=["views", "estimatedRevenue", "estimatedAdRevenue", "estimatedRedPartnerRevenue"],
        )

        # 构建收益数据结构
        revenue_data = {
            "platform": "YouTube",
            "start_date": start_date,
            "end_date": end_date,
            "data": analytics.get("data", []),
            "summary": {
                "total_revenue": analytics.get("totals", {}).get("estimatedRevenue", 0),
                "ad_revenue": analytics.get("totals", {}).get("estimatedAdRevenue", 0),
                "subscription_revenue": analytics.get("totals", {}).get("estimatedRedPartnerRevenue", 0),
                "total_views": analytics.get("totals", {}).get("views", 0),
            },
        }
    else:
        # 对于其他平台，直接调用收益数据API
        revenue_data = api.get_revenue_data(start_date=start_date, end_date=end_date)

        # 添加平台信息
        platform_names = {"xigua": "西瓜视频", "toutiao": "今日头条", "xiaohongshu": "小红书"}
        revenue_data["platform"] = platform_names.get(platform, platform)

    return revenue_data


def get_all_platforms_revenue(
    api_factory: APIFactory,
    platforms: Optional[List[str]] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
) -> Dict[str, Any]:
    """
    获取所有平台的收益数据

    Args:
        api_factory: API工厂实例
        platforms: 平台列表，如果为 None 则获取所有支持的平台
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)

    Returns:
        所有平台的收益数据
    """
    # 设置默认平台列表
    if platforms is None:
        platforms = ["youtube", "xigua", "toutiao", "xiaohongshu"]

    # 获取每个平台的收益数据
    all_revenue_data = {}
    total_revenue = 0

    for platform in platforms:
        try:
            revenue_data = get_platform_revenue(api_factory, platform, start_date, end_date)
            all_revenue_data[platform] = revenue_data

            # 累加总收益
            platform_total = revenue_data.get("summary", {}).get("total_revenue", 0)
            total_revenue += platform_total

            logger.info("获取 {platform} 收益数据成功，总收益: {platform_total:.2f}")
        except Exception as e:
            logger.error(f"获取 {platform} 收益数据失败: {e}")

    # 构建汇总结果
    result = {
        "start_date": start_date,
        "end_date": end_date,
        "platforms": all_revenue_data,
        "total_revenue": total_revenue,
        "timestamp": datetime.datetime.now().isoformat(),
    }

    return result


def analyze_revenue_data(revenue_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    分析收益数据

    Args:
        revenue_data: 收益数据

    Returns:
        分析结果
    """
    # 提取平台数据
    platforms_data = revenue_data.get("platforms", {})

    # 准备分析结果
    analysis = {"platform_comparison": {}, "daily_trends": {}, "revenue_sources": {}, "performance_metrics": {}}

    # 平台比较
    platform_totals = {}
    platform_views = {}
    platform_rpm = {}

    for platform, data in platforms_data.items():
        summary = data.get("summary", {})
        platform_totals[platform] = summary.get("total_revenue", 0)
        platform_views[platform] = summary.get("total_views", 0)

        # 计算RPM (每千次展示收益)
        views = summary.get("total_views", 0)
        revenue = summary.get("total_revenue", 0)
        rpm = (revenue * 1000 / views) if views > 0 else 0
        platform_rpm[platform] = rpm

    analysis["platform_comparison"] = {"revenue": platform_totals, "views": platform_views, "rpm": platform_rpm}

    # 收益来源分析
    for platform, data in platforms_data.items():
        summary = data.get("summary", {})

        # 提取不同收益来源
        revenue_sources = {}
        for key, value in summary.items():
            if "revenue" in key and key != "total_revenue":
                revenue_sources[key] = value

        analysis["revenue_sources"][platform] = revenue_sources

    # 日趋势分析
    all_daily_data = []

    for platform, data in platforms_data.items():
        daily_data = data.get("data", [])

        for entry in daily_data:
            if "date" in entry and "total_revenue" in entry:
                all_daily_data.append(
                    {
                        "date": entry["date"],
                        "platform": platform,
                        "revenue": entry["total_revenue"],
                        "views": entry.get("views", 0),
                    }
                )

    analysis["daily_trends"]["data"] = all_daily_data

    # 性能指标
    total_revenue = revenue_data.get("total_revenue", 0)
    total_views = sum(platform_views.values())
    avg_rpm = (total_revenue * 1000 / total_views) if total_views > 0 else 0

    analysis["performance_metrics"] = {
        "total_revenue": total_revenue,
        "total_views": total_views,
        "average_rpm": avg_rpm,
        "best_performing_platform": max(platform_totals.items(), key=lambda x: x[1])[0] if platform_totals else None,
        "highest_rpm_platform": max(platform_rpm.items(), key=lambda x: x[1])[0] if platform_rpm else None,
    }

    return analysis


def visualize_revenue_data(revenue_data: Dict[str, Any], analysis: Dict[str, Any], output_dir: str = "data"):
    """
    可视化收益数据

    Args:
        revenue_data: 收益数据
        analysis: 分析结果
        output_dir: 输出目录
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 平台收益比较
    platform_totals = analysis["platform_comparison"]["revenue"]

    plt.figure(figsize=(10, 6))
    plt.bar(platform_totals.keys(), platform_totals.values())
    plt.title("各平台收益比较")
    plt.xlabel("平台")
    plt.ylabel("收益 (元)")
    plt.grid(axis="y", linestyle="--", alpha=0.7)
    plt.savefig(os.path.join(output_dir, "platform_revenue_comparison.png"))

    # 平台RPM比较
    platform_rpm = analysis["platform_comparison"]["rpm"]

    plt.figure(figsize=(10, 6))
    plt.bar(platform_rpm.keys(), platform_rpm.values())
    plt.title("各平台RPM比较")
    plt.xlabel("平台")
    plt.ylabel("RPM (元/千次展示)")
    plt.grid(axis="y", linestyle="--", alpha=0.7)
    plt.savefig(os.path.join(output_dir, "platform_rpm_comparison.png"))

    # 日收益趋势
    daily_data = analysis["daily_trends"]["data"]

    if daily_data:
        # 转换为DataFrame
        df = pd.DataFrame(daily_data)

        # 确保日期格式正确
        df["date"] = pd.to_datetime(df["date"])

        # 按平台和日期分组
        pivot_df = df.pivot_table(index="date", columns="platform", values="revenue", aggfunc="sum")

        plt.figure(figsize=(12, 6))
        pivot_df.plot(kind="line", ax=plt.gca())
        plt.title("日收益趋势")
        plt.xlabel("日期")
        plt.ylabel("收益 (元)")
        plt.grid(True, linestyle="--", alpha=0.7)
        plt.legend(title="平台")
        plt.savefig(os.path.join(output_dir, "daily_revenue_trend.png"))

    # 收益来源分析
    for platform, sources in analysis["revenue_sources"].items():
        if sources:
            plt.figure(figsize=(8, 8))
            plt.pie(sources.values(), labels=sources.keys(), autopct="%1.1f%%")
            plt.title("{platform} 收益来源分析")
            plt.savefig(os.path.join(output_dir, "{platform}_revenue_sources.png"))


def save_data(data: Dict[str, Any], filename: str, output_dir: str = "data") -> None:
    """
    保存数据到文件

    Args:
        data: 要保存的数据
        filename: 文件名
        output_dir: 输出目录
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 完整文件路径
    filepath = os.path.join(output_dir, filename)

    try:
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info("数据已保存到: {filepath}")
    except Exception as e:
        logger.error("保存数据失败: {e}")


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="平台数据分析工具")
    parser.add_argument("--platforms", nargs="+", help="平台列表，用空格分隔")
    parser.add_argument("--start-date", help="开始日期 (YYYY-MM-DD)")
    parser.add_argument("--end-date", help="结束日期 (YYYY-MM-DD)")
    parser.add_argument("--output-dir", default="data", help="输出目录")
    parser.add_argument("--visualize", action="store_true", help="是否生成可视化图表")

    args = parser.parse_args()

    # 创建API工厂
    api_factory = APIFactory()

    # 认证平台
    platforms = args.platforms or ["youtube", "xigua", "toutiao", "xiaohongshu"]
    auth_results = authenticate_platforms(api_factory, platforms)

    # 检查认证结果
    authenticated_platforms = [p for p, result in auth_results.items() if result]

    if not authenticated_platforms:
        logger.error("所有平台认证失败，无法继续")
        return

    logger.info("成功认证的平台: {authenticated_platforms}")

    # 获取收益数据
    revenue_data = get_all_platforms_revenue(
        api_factory=api_factory, platforms=authenticated_platforms, start_date=args.start_date, end_date=args.end_date
    )

    # 分析收益数据
    analysis = analyze_revenue_data(revenue_data)

    # 保存数据
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    save_data(revenue_data, "revenue_data_{timestamp}.json", args.output_dir)
    save_data(analysis, "revenue_analysis_{timestamp}.json", args.output_dir)

    # 打印分析结果摘要
    print("\n===== 收益分析摘要 =====")
    print(f"分析时间范围: {revenue_data['start_date']} 至 {revenue_data['end_date']}")
    print(f"总收益: {analysis['performance_metrics']['total_revenue']:.2f} 元")
    print(f"总浏览量: {analysis['performance_metrics']['total_views']}")
    print(f"平均RPM: {analysis['performance_metrics']['average_rpm']:.2f} 元/千次展示")

    if analysis["performance_metrics"]["best_performing_platform"]:
        best_platform = analysis["performance_metrics"]["best_performing_platform"]
        print(
            f"表现最佳平台: {best_platform} (收益: {analysis['platform_comparison']['revenue'][best_platform]:.2f} 元)"
        )

    if analysis["performance_metrics"]["highest_rpm_platform"]:
        highest_rpm_platform = analysis["performance_metrics"]["highest_rpm_platform"]
        print(
            f"RPM最高平台: {highest_rpm_platform} (RPM: {analysis['platform_comparison']['rpm'][highest_rpm_platform]:.2f} 元/千次展示)"
        )

    print("\n各平台收益:")
    for platform, revenue in analysis["platform_comparison"]["revenue"].items():
        print("  {platform}: {revenue:.2f} 元")

    # 生成可视化图表
    if args.visualize:
        try:
            visualize_revenue_data(revenue_data, analysis, args.output_dir)
            print("\n可视化图表已保存到: {args.output_dir}")
        except Exception as e:
            logger.error("生成可视化图表失败: {e}")


if __name__ == "__main__":
    main()
