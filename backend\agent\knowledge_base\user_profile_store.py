#!/usr/bin/env python3
"""
user_profile_store module
"""

import json
import os
from typing import Any, Dict, Optional

"""存储和管理用户画像与偏好"""
    """
    初始化 UserProfileStore。
    Args:
        knowledge_base_path (str): 知识库数据存储的根路径。
    """
    """确保知识库目录存在"""
    """
    从JSON文件加载用户画像数据。
    Returns:
        Dict[str, Dict[str, Any]]: 加载的用户画像字典，键为用户ID，值为画像内容。
                                  如果文件不存在或解析失败则返回空字典。
    """
    """将当前用户画像保存到JSON文件"""
    """
    添加或更新一个用户的画像信息。
    Args:
        user_id (str): 用户的唯一标识符。
        profile_data (Dict[str, Any]): 用户的画像数据 (例如: {'preferred_style': 'cinematic', 'topic_interests': ['travel',
    'food']})。
    Returns:
        bool: 操作成功返回True。
    """
    """
    根据用户ID获取用户画像。
    Args:
        user_id (str): 用户的唯一标识符。
    Returns:
        Optional[Dict[str, Any]]: 用户画像数据字典，如果未找到则返回None。
    """
    """获取所有用户画像"""
    """
    删除一个用户的画像信息。
    Args:
        user_id (str): 要删除画像的用户ID。
    Returns:
        bool: 如果画像成功删除，则返回True，否则返回False (例如用户ID不存在)。
    """
    """
    获取用户的特定偏好设置。
    Args:
        user_id (str): 用户ID。
        preference_key (str): 偏好设置的键名 (e.g., 'preferred_style', 'default_platform\')。
        default (Optional[Any], optional): 如果偏好未设置或用户不存在，返回的默认值。Defaults to None.
    Returns:
        Optional[Any]: 用户的偏好值。
    """
DEFAULT_KB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..", "..", "data", "kb")
USER_PROFILES_FILE_NAME = "user_profiles.json"
class UserProfileStore:
    def __init__(self, knowledge_base_path: str = DEFAULT_KB_PATH):
        self.profiles_file_path = os.path.join(knowledge_base_path, USER_PROFILES_FILE_NAME)
        self._ensure_kb_directory(knowledge_base_path)
        self.user_profiles: Dict[str, Dict[str, Any]] = self._load_profiles()
    def _ensure_kb_directory(self, kb_path: str):
        if not os.path.exists(kb_path):
        os.makedirs(kb_path)
        print(f"知识库目录已创建: {kb_path}")
    def _load_profiles(self) -> Dict[str, Dict[str, Any]]:
        if os.path.exists(self.profiles_file_path):
        try:
            with open(self.profiles_file_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except json.JSONDecodeError:
            print(f"错误: 无法解析用户画像文件 {self.profiles_file_path}。将使用空画像集。")
        except Exception as e:
            print(f"加载用户画像文件 {self.profiles_file_path} 时出错: {e}")
        return {}
    def _save_profiles(self):
        try:
        with open(self.profiles_file_path, "w", encoding="utf-8") as f:
            json.dump(self.user_profiles, f, ensure_ascii=False, indent=4)
        except Exception as e:
        print(f"保存用户画像文件 {self.profiles_file_path} 时出错: {e}")
    def add_or_update_profile(self, user_id: str, profile_data: Dict[str, Any]) -> bool:
        if user_id in self.user_profiles:
        self.user_profiles[user_id].update(profile_data)
        action = "更新"
        else:
        self.user_profiles[user_id] = profile_data
        action = "添加"
        self._save_profiles()
        print(f"用户 '{user_id}f' 的画像已{action}。")
        return True
    def get_profile(self, user_id: str) -> Optional[Dict[str, Any]]:
        return self.user_profiles.get(user_id)
    def get_all_profiles(self) -> Dict[str, Dict[str, Any]]:
        return self.user_profiles
    def delete_profile(self, user_id: str) -> bool:
        if user_id in self.user_profiles:
        del self.user_profiles[user_id]
        self._save_profiles()
        print(f"用户 '{user_id}' 的画像已删除。")
        return True
        print(f"用户画像删除失败: 用户ID f'{user_id}' 未找到。")
        return False
    def get_user_preference(self, user_id: str, preference_key: str, default: Optional[Any] = None) -> Optional[Any]:
        profile = self.get_profile(user_id)
        if profile:
        return profile.get(preference_key, default)
        return default
        if __name__ == "__main__":
        test_kb_path = os.path.join(os.getcwd(), "test_kb_data_profiles")
        if not os.path.exists(test_kb_path):
        os.makedirs(test_kb_path)
        profile_store = UserProfileStore(knowledge_base_path=test_kb_path)
        profile_store.add_or_update_profile()
        "user123",
        {"preferred_style": "vlog", "topic_interests": ["technology", "gaming"], "preferred_duration_minutes": 5})
        profile_store.add_or_update_profile()
        "user456",
        {"preferred_style": "cinematic", "topic_interests": ["travel", "nature"], "preferred_music_genre": "epic"})
        print("所有用户画像:", profile_store.get_all_profiles())
        print("user123的画像:", profile_store.get_profile("user123"))
        style_user123 = profile_store.get_user_preference("user123", "preferred_style")
        print(f"user123的偏好风格: {style_user123}")
        music_user456 = profile_store.get_user_preference("user456", "preferred_music_genre", default="instrumental")
        print(f"user456的偏好音乐: {music_user456}")
        non_existent_pref = profile_store.get_user_preference("user123", "non_existent_key", default="N/A")
        print(f"user123不存在的偏好: {non_existent_pref}")
        profile_store.add_or_update_profile("user123", {"preferred_duration_minutes": 7, "last_active_date": "2024-07-28"})
        print("更新后的user123画像:", profile_store.get_profile("user123"))
        profile_store.delete_profile("user456")
        print("删除user456后所有画像:", profile_store.get_all_profiles())
