#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试脚本：用于测试IntelliCutAgent的各个模块功能
"""

import logging
import os
import sys

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

from backend.agent.perception_engine.audio_analyzer import AudioAnalyzer

# 导入模块
from backend.agent.perception_engine.input_parser import InputParser
from backend.agent.perception_engine.video_analyzer import VideoAnalyzer
from backend.agent.publisher.video_publisher import VideoPublisher
from backend.agent.smart_editor.smart_editor import SmartEditor

def test_input_parser():
    """测试InputParser模块"""
    logger.info("=== 测试 InputParser 模块 ===")

    parser = InputParser()

    # 测试解析JSON格式命令
    json_command = '{"action": "create_and_publish_video", "params": {"material_path": "/path/to/video.mp4", "platforms": ["douyin", "bilibili"]}}'
    parser.parse_user_command(json_command)
    logger.info("JSON命令解析结果: {result}")

    # 测试解析自然语言命令
    nl_command = "创建一个视频，素材路径：/path/to/video.mp4，平台：抖音、B站，时长：30秒，风格：动作"
    parser.parse_user_command(nl_command)
    logger.info("自然语言命令解析结果: {result}")

    # 测试解析配置文件
    config_path = os.path.join(os.getcwd(), "config", "editing_rules.json")
    if os.path.exists(config_path):
        parser.parse_config_file(config_path)
        logger.info("配置文件解析结果: {list(result.keys())}")
    else:
        logger.warning("配置文件不存在: {config_path}")

    # 测试获取命令帮助
    parser.get_command_help("create_and_publish_video")
    logger.info("命令帮助:\n{help_text}")

def test_video_analyzer():
    """测试VideoAnalyzer模块"""
    logger.info("=== 测试 VideoAnalyzer 模块 ===")

    analyzer = VideoAnalyzer()

    # 创建一个测试视频文件路径
    test_video_path = os.path.join(os.getcwd(), "test_video.mp4")

    # 如果文件不存在，创建一个空文件用于测试
    if not os.path.exists(test_video_path):
        with open(test_video_path, "w") as f:
            f.write("")
        logger.info("创建测试视频文件: {test_video_path}")

    # 测试视频分析
    analysis_types = ["basic", "scene_detection", "object_detection"]
    result = analyzer.analyze_video(test_video_path, analysis_types)

    # 打印分析结果的主要部分
    logger.info("视频分析结果:")
    for key in result:
        if key in ["video_path", "analysis_types", "duration", "resolution", "fps"]:
            logger.info("  {key}: {result[key]}")

    if "scenes" in result:
        logger.info(f"  检测到 {len(result['scenes'])} 个场景")

    if "objects" in result:
        logger.info(f"  检测到 {len(result['objects'])} 个物体")

    # 测试关键帧提取
    keyframes = analyzer.extract_keyframes(test_video_path, method="uniform", num_frames=5)
    logger.info("提取了 {len(keyframes)} 个关键帧")

    # 测试视频摘要生成
    summary = analyzer.generate_video_summary(test_video_path, result)
    logger.info(f"视频摘要: {summary['summary_text']}")

def test_audio_analyzer():
    """测试AudioAnalyzer模块"""
    logger.info("=== 测试 AudioAnalyzer 模块 ===")

    analyzer = AudioAnalyzer()

    # 创建一个测试音频文件路径
    test_audio_path = os.path.join(os.getcwd(), "test_audio.mp3")

    # 如果文件不存在，创建一个空文件用于测试
    if not os.path.exists(test_audio_path):
        with open(test_audio_path, "w") as f:
            f.write("")
        logger.info("创建测试音频文件: {test_audio_path}")

    # 测试音频分析
    analysis_types = ["basic", "speech_to_text", "silence_detection", "emotion_analysis"]
    result = analyzer.analyze_audio(test_audio_path, analysis_types)

    # 打印分析结果的主要部分
    logger.info("音频分析结果:")
    for key in ["status", "duration_sec", "sample_rate", "channels"]:
        if key in result:
            logger.info("  {key}: {result[key]}")

    if "transcription" in result:
        logger.info(f"  转录文本: {result['transcription'].get('full_text', '')[:100]}...")

    if "silence_segments" in result:
        logger.info(f"  检测到 {len(result['silence_segments'])} 个静音片段")

    if "emotions" in result:
        logger.info(f"  检测到 {len(result['emotions'])} 个情感片段")

    # 测试音频摘要生成
    summary = analyzer.generate_audio_summary(test_audio_path, result)
    logger.info(f"音频摘要: {summary['summary_text']}")

def test_smart_editor():
    """测试SmartEditor模块"""
    logger.info("=== 测试 SmartEditor 模块 ===")

    editor = SmartEditor()

    # 创建测试素材ID和分析结果
    material_ids = ["test_material_1", "test_material_2"]

    # 模拟分析结果
    analysis_results = {
        "scenes": [
            {"start_time": 0, "end_time": 5, "description": "开场镜头", "type": "开场"},
            {"start_time": 5, "end_time": 15, "description": "主要内容", "type": "对话"},
            {"start_time": 15, "end_time": 25, "description": "动作场景", "type": "动作"},
            {"start_time": 25, "end_time": 30, "description": "结尾", "type": "结尾"},
        ],
        "duration": 30,
    }

    # 测试编辑规则
    edit_rules = {
        "duration": "20s",
        "style": "action",
        "transitions": ["fade", "wipe"],
        "effects": ["contrast", "speed"],
    }

    # 测试自动剪辑
    result = editor.auto_edit_video(material_ids, analysis_results, edit_rules)
    logger.info("自动剪辑结果: {result}")

    # 测试添加背景音乐
    music_result = editor.add_background_music(result, "test_music.mp3", volume=0.3)
    logger.info("添加背景音乐结果: {music_result}")

    # 测试生成字幕
    subtitle_result = editor.generate_subtitles(
        result, "这是测试字幕内容", font="Arial", font_size=24, color="white", position="bottom"
    )
    logger.info(f"生成字幕结果: {subtitle_result}")

    # 测试应用视频效果
    effects = [
        {"type": "brightness", "value": 1.1},
        {"type": "contrast", "value": 1.2},
        {"type": "speed", "value": 1.5},
    ]
    editor.apply_video_effects(result, effects)
    logger.info("应用视频效果结果: {effects_result}")

def test_video_publisher():
    """测试VideoPublisher模块"""
    logger.info("=== 测试 VideoPublisher 模块 ===")

    publisher = VideoPublisher()

    # 测试视频发布
    video_path = "test_video.mp4"
    platforms = ["douyin", "bilibili"]
    metadata = {
        "title": "测试视频标题",
        "description": "这是一个测试视频描述，用于测试VideoPublisher模块。",
        "tags": ["测试", "IntelliCutAgent", "AI剪辑"],
    }

    # 发布视频
    result = publisher.publish_video(video_path, platforms, metadata)
    logger.info(f"视频发布结果: {result['status']}")

    for platform, platform_result in result.get("platforms", {}).items():
        logger.info(f"  平台 {platform}: {platform_result.get('status')}")
        if platform_result.get("status") == "success":
            logger.info(f"    视频ID: {platform_result.get('video_id')}")
            logger.info(f"    视频URL: {platform_result.get('video_url')}")

    # 测试获取平台限制
    for platform in platforms:
        limits = publisher.get_platform_limits(platform)
        logger.info("平台 {platform} 限制:")
        logger.info(f"  最大视频长度: {limits.get('limits', {}).get('max_video_length')}秒")
        logger.info(f"  最大文件大小: {limits.get('limits', {}).get('max_file_size')}MB")

    # 测试获取发布历史
    history = publisher.get_publish_history()
    logger.info(f"发布历史记录数量: {history.get('total')}")

def main():
    """主函数"""
    logger.info("开始测试IntelliCutAgent模块...")

    # 测试各个模块
    test_input_parser()
    test_video_analyzer()
    test_audio_analyzer()
    test_smart_editor()
    test_video_publisher()

    logger.info("测试完成！")

if __name__ == "__main__":
    main()
