#!/usr/bin/env python3
"""
IntelliCutAgent 自动修复脚本
修复6,237个代码质量问题
"""

import re
import subprocess
from pathlib import Path
from typing import Dict
from typing import Tuple

def run_command(command: str, description: str) -> bool:
    """运行命令并返回结果"""
    print("🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ {description} 完成")
            return True
        else:
            print("❌ {description} 失败: {result.stderr}")
            return False
    except Exception as e:
        print("❌ {description} 异常: {e}")
        return False

def install_tools() -> bool:
    """安装代码修复工具"""
    tools = ["autoflake", "black", "isort"]

    for tool in tools:
        if not run_command("pip install {tool}", "安装 {tool}"):
            return False

    return True

def fix_critical_errors() -> bool:
    """修复严重的运行时错误"""
    print("\n🚨 修复严重错误...")

    # 修复 test_dependencies.py 中的未定义变量
    test_deps_file = Path("test_dependencies.py")
    if test_deps_file.exists():
        content = test_deps_file.read_text(encoding="utf-8")

        # 修复 numpy 导入问题
        if "def test_opencv():" in content and "import numpy as np" not in content:
            content = content.replace(
                "def test_opencv():\n    import cv2", "def test_opencv():\n    import cv2\n    import numpy as np"
            )

            test_deps_file.write_text(content, encoding="utf-8")
            print("✅ 修复了 test_dependencies.py 中的 numpy 导入问题")

    return True

def fix_bare_except() -> bool:
    """修复裸露的except语句"""
    print("\n🔧 修复裸露的except语句...")

    python_files = list(Path(".").rglob("*.py"))

    for file_path in python_files:
        if "venv" in str(file_path) or "__pycache__" in str(file_path):
            continue

        try:
            content = file_path.read_text(encoding="utf-8")
            original_content = content

            # 替换裸露的except
            content = re.sub(r"except\s*:\s*\n", "except Exception as e:\n", content)

            if content != original_content:
                file_path.write_text(content, encoding="utf-8")
                print("✅ 修复了 {file_path} 中的裸露except")

        except Exception as e:
            print("⚠️ 处理文件 {file_path} 时出错: {e}")

    return True

def clean_unused_imports() -> bool:
    """清理未使用的导入"""
    print("\n🧹 清理未使用的导入...")

    command = "autoflake --remove-all-unused-imports --remove-unused-variables --in-place --recursive --exclude=venv ."
    return run_command(command, "清理未使用导入和变量")

def format_code() -> bool:
    """格式化代码"""
    print("\n🎨 格式化代码...")

    # 使用black格式化
    if not run_command("black --line-length 120 --exclude=venv .", "Black代码格式化"):
        return False

    # 使用isort排序导入
    if not run_command("isort --profile black --line-length 120 --skip=venv .", "导入语句排序"):
        return False

    return True

def fix_f_strings() -> bool:
    """修复不必要的f-string"""
    print("\n🔤 修复f-string问题...")

    python_files = list(Path(".").rglob("*.py"))

    for file_path in python_files:
        if "venv" in str(file_path) or "__pycache__" in str(file_path):
            continue

        try:
            content = file_path.read_text(encoding="utf-8")
            original_content = content

            # 修复没有变量的f-string
            # 匹配 "..." 或 f'...' 但不包含 {} 的情况
            content = re.sub(r'f(["\'])([^"\']*?)\1(?![^"\']*\{)', r"\1\2\1", content)

            if content != original_content:
                file_path.write_text(content, encoding="utf-8")
                print("✅ 修复了 {file_path} 中的f-string问题")

        except Exception as e:
            print("⚠️ 处理文件 {file_path} 时出错: {e}")

    return True

def add_missing_newlines() -> bool:
    """添加文件末尾的换行符"""
    print("\n📝 添加缺失的换行符...")

    python_files = list(Path(".").rglob("*.py"))

    for file_path in python_files:
        if "venv" in str(file_path) or "__pycache__" in str(file_path):
            continue

        try:
            content = file_path.read_text(encoding="utf-8")

            if content and not content.endswith("\n"):
                file_path.write_text(content + "\n", encoding="utf-8")
                print("✅ 为 {file_path} 添加了末尾换行符")

        except Exception as e:
            print("⚠️ 处理文件 {file_path} 时出错: {e}")

    return True

def run_final_check() -> Tuple[int, Dict[str, int]]:
    """运行最终检查"""
    print("\n🔍 运行最终代码质量检查...")

    try:
        result = subprocess.run(
            "python -m flake8 --statistics --count --max-line-length=120 --exclude=venv,__pycache__,.git .",
            shell=True,
            capture_output=True,
            text=True,
        )

        output = result.stdout
        lines = output.strip().split("\n")

        # 解析统计信息
        stats = {}
        total_issues = 0

        for line in lines:
            if line and not line.startswith("."):
                parts = line.strip().split()
                if len(parts) >= 2 and parts[0].isdigit():
                    count = int(parts[0])
                    error_type = parts[1]
                    stats[error_type] = count
                    total_issues += count

        return total_issues, stats

    except Exception as e:
        print(f"❌ 最终检查失败: {e}")
        return -1, {}

def main():
    """主修复流程"""
    print("🚀 IntelliCutAgent 自动修复工具")
    print("=" * 60)
    print("📊 预计修复 6,237 个代码质量问题")
    print("⏱️ 预计耗时: 5-10分钟")
    print()

    # 确认执行
    response = input("是否继续执行自动修复? (y/N): ").strip().lower()
    if response != "y":
        print("❌ 用户取消操作")
        return

    print("\n🔧 开始自动修复流程...")

    # 修复步骤
    steps = [
        ("安装修复工具", install_tools),
        ("修复严重错误", fix_critical_errors),
        ("修复裸露except", fix_bare_except),
        ("清理未使用导入", clean_unused_imports),
        ("格式化代码", format_code),
        ("修复f-string", fix_f_strings),
        ("添加换行符", add_missing_newlines),
    ]

    success_count = 0

    for step_name, step_func in steps:
        print("\n📋 步骤: {step_name}")
        try:
            if step_func():
                success_count += 1
                print("✅ {step_name} 完成")
            else:
                print("❌ {step_name} 失败")
        except Exception as e:
            print("❌ {step_name} 异常: {e}")

    # 最终检查
    print("\n" + "=" * 60)
    print("📊 修复结果统计")
    print("=" * 60)

    total_issues, stats = run_final_check()

    if total_issues >= 0:
        print("🎯 修复后剩余问题: {total_issues} 个")
        print("📈 修复率: {((6237 - total_issues) / 6237 * 100):.1f}%")

        if stats:
            print("\n📋 剩余问题分类:")
            for error_type, count in sorted(stats.items()):
                print("  {error_type}: {count}")
    else:
        print("❌ 无法获取最终统计信息")

    print("\n✅ 修复流程完成! 成功执行 {success_count}/{len(steps)} 个步骤")

    if total_issues < 100:
        print("🎉 恭喜! 代码质量已显著改善!")
    elif total_issues < 500:
        print("👍 不错! 大部分问题已修复，建议继续优化")
    else:
        print("⚠️ 仍有较多问题，建议手动检查和修复")

if __name__ == "__main__":
    main()
