import unittest

from content_analyzer import ContentAnalyzer


class TestContentAnalyzer(unittest.TestCase):
    def setUp(self):
        self.analyzer = ContentAnalyzer()

    def test_analyze_video(self):
        video_path = "test_video.mp4"
        result = self.analyzer.analyze_video(video_path)
        self.assertIn("speech_to_text", result)
        self.assertIn("object_detection", result)
        self.assertIn("sentiment", result)
        self.assertIn("duration", result)
        self.assertEqual(result["speech_to_text"], "视频 {video_path} 的语音转文字内容。")

    def test_analyze_audio(self):
        audio_path = "test_audio.mp3"
        result = self.analyzer.analyze_audio(audio_path)
        self.assertIn("speech_to_text", result)
        self.assertIn("emotion_detection", result)
        self.assertIn("background_music", result)
        self.assertEqual(result["speech_to_text"], "音频 {audio_path} 的语音转文字内容。")

    def test_analyze_image(self):
        image_path = "test_image.jpg"
        result = self.analyzer.analyze_image(image_path)
        self.assertIn("object_detection", result)
        self.assertIn("scene_recognition", result)
        self.assertIn("dominant_colors", result)


if __name__ == "__main__":
    unittest.main()
