#!/usr/bin/env python3
"""
IntelliCutAgent 快速修复脚本
修复关键问题，确保项目能正常启动
"""

import shutil
import subprocess
import sys
from pathlib import Path


def check_ffmpeg():
    """检查FFmpeg是否已安装"""
    print("🔍 检查FFmpeg安装状态...")

    ffmpeg_path = shutil.which("ffmpeg")
    ffprobe_path = shutil.which("ffprobe")

    if ffmpeg_path and ffprobe_path:
        print("✅ FFmpeg已安装: {ffmpeg_path}")
        print("✅ FFprobe已安装: {ffprobe_path}")
        return True
    else:
        print("❌ FFmpeg未安装或未添加到PATH")
        print("💡 请手动安装FFmpeg:")
        print("   Windows: choco install ffmpeg")
        print("   或下载: https://ffmpeg.org/download.html")
        return False


def fix_strategy_selector():
    """修复StrategySelector初始化问题"""
    print("\n🔧 修复StrategySelector初始化问题...")

    strategy_selector_path = Path("backend/agent/decision_planning_engine/strategy_selector.py")

    if not strategy_selector_path.exists():
        print("❌ 文件不存在: {strategy_selector_path}")
        return False

    try:
        # 读取文件内容
        with open(strategy_selector_path, "r", encoding="utf-8") as f:
            content = f.read()

        # 检查是否已经修复
        if "self.available_strategies = {" in content:
            print("✅ StrategySelector已经修复")
            return True

        # 查找__init__方法并添加缺失的属性
        lines = content.split("\n")
        new_lines = []
        in_init = False
        init_indent = 0
        added_fix = False

        for i, line in enumerate(lines):
            new_lines.append(line)

            # 检测__init__方法开始
            if "def __init__(sel" in line:
                in_init = True
                init_indent = len(line) - len(line.lstrip())
                continue

            # 在__init__方法中查找合适的位置添加修复代码
            if in_init and not added_fix:
                # 查找self.config_dir赋值后的位置
                if "self.config_dir = " in line:
                    # 添加缺失的属性初始化
                    indent = " " * (init_indent + 8)
                    fix_code = [
                        "",
                        "{indent}# 修复: 添加缺失的available_strategies属性",
                        "{indent}self.available_strategies = {{",
                        f'{indent}    "default": "default_strategy",',
                        f'{indent}    "fast": "fast_strategy",',
                        f'{indent}    "quality": "quality_strategy",',
                        f'{indent}    "balanced": "balanced_strategy"',
                        "{indent}}}",
                    ]
                    new_lines.extend(fix_code)
                    added_fix = True

            # 检测__init__方法结束
            if in_init and line.strip() and not line.startswith(" ") and not line.startswith("\t"):
                in_init = False

        if added_fix:
            # 写回文件
            with open(strategy_selector_path, "w", encoding="utf-8") as f:
                f.write("\n".join(new_lines))
            print("✅ StrategySelector修复完成")
            return True
        else:
            print("⚠️ 未找到合适的修复位置，请手动修复")
            return False

    except Exception as e:
        print("❌ 修复失败: {e}")
        return False


def fix_opencv_test():
    """修复OpenCV测试代码"""
    print("\n🔧 修复OpenCV测试代码...")

    test_file = Path("test_dependencies.py")

    if not test_file.exists():
        print("⚠️ test_dependencies.py不存在，跳过修复")
        return True

    try:
        with open(test_file, "r", encoding="utf-8") as f:
            content = f.read()

        # 修复cv2.zeros为np.zeros
        if "cv2.zeros" in content:
            content = content.replace("cv2.zeros", "np.zeros")

            with open(test_file, "w", encoding="utf-8") as f:
                f.write(content)
            print("✅ OpenCV测试代码修复完成")
        else:
            print("✅ OpenCV测试代码无需修复")

        return True

    except Exception as e:
        print("❌ 修复失败: {e}")
        return False


def update_requirements():
    """更新requirements.txt"""
    print("\n📝 更新requirements.txt...")

    requirements_path = Path("requirements.txt")

    try:
        with open(requirements_path, "r", encoding="utf-8") as f:
            content = f.read()

        # 添加seaborn和固定moviepy版本
        updates_needed = []

        if "seaborn" not in content:
            updates_needed.append("seaborn>=0.13.0")

        if "moviepy>=1.0.3" not in content and "moviepy==1.0.3" not in content:
            # 替换moviepy版本
            content = content.replace("moviepy>=1.0.3", "moviepy==1.0.3")
            if "moviepy==1.0.3" not in content:
                updates_needed.append("moviepy==1.0.3")

        if updates_needed:
            content += "\n\n# 新增依赖\n"
            for dep in updates_needed:
                content += "{dep}\n"

            with open(requirements_path, "w", encoding="utf-8") as f:
                f.write(content)
            print(f"✅ 已添加依赖: {', '.join(updates_needed)}")
        else:
            print("✅ requirements.txt无需更新")

        return True

    except Exception as e:
        print("❌ 更新失败: {e}")
        return False


def test_project_startup():
    """测试项目是否能正常启动"""
    print("\n🧪 测试项目启动...")

    try:
        # 测试导入主要模块
        result = subprocess.run(
            [sys.executable, "-c", 'import backend.agent_coordinator; print("✅ 模块导入成功")'],
            capture_output=True,
            text=True,
            timeout=30,
        )

        if result.returncode == 0:
            print("✅ 项目模块导入测试通过")
            return True
        else:
            print("❌ 模块导入失败: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print("❌ 测试超时")
        return False
    except Exception as e:
        print("❌ 测试失败: {e}")
        return False


def main():
    """主修复流程"""
    print("🚀 IntelliCutAgent 快速修复工具")
    print("=" * 50)

    fixes = [
        ("检查FFmpeg", check_ffmpeg),
        ("修复StrategySelector", fix_strategy_selector),
        ("修复OpenCV测试", fix_opencv_test),
        ("更新requirements.txt", update_requirements),
        ("测试项目启动", test_project_startup),
    ]

    results = []

    for name, fix_func in fixes:
        print("\n📋 执行: {name}")
        try:
            success = fix_func()
            results.append((name, success))
        except Exception as e:
            print("❌ {name} 执行失败: {e}")
            results.append((name, False))

    # 输出修复结果
    print("\n" + "=" * 50)
    print("📊 修复结果汇总:")

    success_count = 0
    for name, success in results:
        "✅ 成功" if success else "❌ 失败"
        print("  {name}: {status}")
        if success:
            success_count += 1

    print("\n🎯 修复完成: {success_count}/{len(results)} 项成功")

    if success_count == len(results):
        print("🎉 所有修复项目完成！项目应该可以正常运行了。")
        print("\n💡 下一步:")
        print("  1. 如果FFmpeg未安装，请手动安装")
        print("  2. 运行: python main.py --mode demo")
    else:
        print("⚠️ 部分修复失败，请查看上述错误信息并手动修复。")


if __name__ == "__main__":
    main()
