#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复脚本 - 修复最严重的语法错误
"""

import os
import re
import sys


def fix_syntax_errors(file_path: str) -> int:
    """修复语法错误"""
    fixes_count = 0
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复错误的f-string语法
        patterns = [
            # 修复 """ -> """
            (r'"""([^"]*?)""f"', r'"""\1"""'),
            (r"'''([^']*?)''f'", r"'''\1'''"),
            # 修复多余的f
            (r'([a-zA-Z_]\w*)f"', r'\1"'),
            (r'([a-zA-Z_]\w*)f\'', r'\1\''),
            # 修复错误的编码
            (r"encoding='utf-8'", r"encoding='utf-8'"),
            (r'encoding="utf-8"', r'encoding="utf-8"'),
        ]
        
        for pattern, replacement in patterns:
            new_content = re.sub(pattern, replacement, content)
            if new_content != content:
                fixes_count += len(re.findall(pattern, content))
                content = new_content
        
        # 只有在内容发生变化时才写入文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ 修复 {file_path}: {fixes_count} 个语法错误")
        
        return fixes_count
        
    except Exception as e:
        print(f"✗ 修复 {file_path} 失败: {e}")
        return 0


def main():
    """主函数"""
    print("🔧 快速修复语法错误")
    print("=" * 30)
    
    # 获取项目根目录
    project_root = os.path.dirname(os.path.abspath(__file__))
    
    # 需要修复的文件列表
    files_to_fix = []
    
    # 遍历所有Python文件
    for root, dirs, files in os.walk(project_root):
        # 跳过虚拟环境和缓存目录
        dirs[:] = [d for d in dirs if d not in ['venv', '__pycache__', '.git']]
        
        for file in files:
            if file.endswith('.py'):
                files_to_fix.append(os.path.join(root, file))
    
    total_fixes = 0
    
    # 修复每个文件
    for file_path in files_to_fix:
        relative_path = os.path.relpath(file_path, project_root)
        
        # 跳过一些特殊文件
        if any(skip in relative_path for skip in ['test_', '__pycache__', 'venv']):
            continue
        
        fixes = fix_syntax_errors(file_path)
        total_fixes += fixes
    
    print(f"\n🎉 修复完成！总共修复了 {total_fixes} 个语法错误")


if __name__ == "__main__":
    main()
