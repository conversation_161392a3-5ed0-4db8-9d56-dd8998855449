#!/usr/bin/env python3
"""
resource_allocator module
"""

from typing import Any, Dict, Optional

"""管理和分配执行任务所需的系统资源 (CPU, GPU, 内存, 存储, API配额等)。"""
    """
    初始化 ResourceAllocator。
    Args:
        available_resources (Optional[Dict[str, Any]], optional): 系统可用资源的总量。
            示例: {
                "cpu_cores": 8,
                "gpu_units": 2,
                "gpu_memory_gb_per_unit": { "gpu0": 16, "gpu1": 24 },
                "ram_gb": 64,
                "disk_space_gb": 1024,
                "api_quotas": {"youtube_upload": 100, "cloud_storage_write": 1000}
            }
            Defaults to a basic configuration if None.
    """
    """
    估算特定类型任务所需的资源。
    (这是一个简化的示例，实际中可能需要更复杂的估算模型)
    """
    """
    检查是否有足够的资源来执行任务。
    Args:
        task (Dict[str, Any]): 任务信息，包含 task_type 和 parameters。
    Returns:
        bool: 如果资源充足则返回True，否则返回False。
    """
    """
    为任务分配所需的资源。如果资源不足，则返回None。
    Args:
        task (Dict[str, Any]): 要分配资源的任务。
    Returns:
        Optional[Dict[str, Any]]: 分配的资源详情 (例如分配了哪个GPU)，如果分配失败则返回None。
    """
    """
    释放任务完成或失败后占用的资源。
    Args:
        task (Dict[str, Any]): 已完成/失败的任务。
        allocated_details (Dict[str, Any]): 该任务之前被分配的资源详情。
    """
class ResourceAllocator:
def __init__(self, available_resources: Optional[Dict[str, Any]] = None):
    if available_resources is None:
        self.total_resources = {
            "cpu_cores": 4,
            "gpu_units": 0,
            "ram_gb": 16,
            "disk_space_gb": 500,
            "api_quotas": {},
        }
    else:
        self.total_resources = available_resources
    self.currently_allocated_resources = {
        "cpu_cores": 0,
        "gpu_units": 0,
        "ram_gb": 0,
        "disk_space_gb": 0,
        "api_quotas": {key: 0 for key in self.total_resources.get("api_quotas", {})},
        "gpu_allocations": {},  # Tracks which task is using which GPU: {"gpu0": "task_id_xyz", ...}
    }
    self.resource_locks: Dict[str, Any] = {}  # 用于细粒度资源锁定，例如特定GPU单元
    print("ResourceAllocator 初始化完毕。总资源: {self.total_resources}f")
def _get_task_resource_requirements(self, task_type: str, task_parameters: Dict[str, Any]) -> Dict[str, Any]:
    requirements = {"cpu_cores": 1, "ram_gb": 2, "disk_space_gb": 1}  # Default
    if task_type == "load_media":
        requirements["ram_gb"] = 1
        requirements["disk_space_gb"] = task_parameters.get("estimated_size_gb", 0.5)
    elif task_type == "analyze_media_content":
        requirements["cpu_cores"] = 2
        requirements["ram_gb"] = 4
        if "gpu_intensive_analysis" in task_parameters and self.total_resources.get("gpu_units", 0) > 0:
            requirements["gpu_units"] = 1  # 请求一个GPU单元
    elif task_type == "render_final_video":
        requirements["cpu_cores"] = min(4, self.total_resources.get("cpu_cores", 1))  # 可以使用更多CPU
        requirements["ram_gb"] = 8
        requirements["disk_space_gb"] = task_parameters.get("estimated_output_size_gb", 2)
        if self.total_resources.get("gpu_units", 0) > 0:
            requirements["gpu_units"] = 1  # 渲染通常受益于GPU
    elif task_type == "train_model":
        requirements["cpu_cores"] = min(4, self.total_resources.get("cpu_cores", 1))
        requirements["ram_gb"] = 16
        requirements["disk_space_gb"] = task_parameters.get("dataset_size_gb", 10)
        if self.total_resources.get("gpu_units", 0) > 0:
            requirements["gpu_units"] = self.total_resources.get("gpu_units", 0)  # 尝试使用所有可用GPU
    elif task_type.startswith("api_"):
        api_name = task_type.split("_", 1)[1]
        if api_name in self.total_resources.get("api_quotas", {}):
            requirements["api_quotas"] = {api_name: 1}  # 默认请求1个配额单位
    return requirements
def check_resource_availability(self, task: Dict[str, Any]) -> bool:
    requirements = self._get_task_resource_requirements(task["task_type"], task.get("parameters", {}))
    available_cpu = self.total_resources["cpu_cores"] - self.currently_allocated_resources["cpu_cores"]
    if available_cpu < requirements.get("cpu_cores", 0):
        print(
            f"资源不足 (CPU): 需要 {requirements.get('cpu_cores\', 0)}, 可用 {available_cpu} for task {task.get('task_id')}"
        )
        return False
    available_ram = self.total_resources["ram_gb"] - self.currently_allocated_resources["ram_gb"]
    if available_ram < requirements.get("ram_gb", 0):
        print(
            f"资源不足 (RAM): 需要 {requirements.get('ram_gb\', 0)}, 可用 {available_ram} for task {task.get('task_id')}"
        )
        return False
    available_disk = self.total_resources["disk_space_gb"] - self.currently_allocated_resources["disk_space_gb"]
    if available_disk < requirements.get("disk_space_gb", 0):
        print(
            f"资源不足 (
                Disk): 需要 {requirements.get('disk_space_gb\',
                0)},
                可用 {available_disk} for task {task.get('task_id')}
            "
        )
        return False
    required_gpus = requirements.get("gpu_units", 0)
    if required_gpus > 0:
        allocated_gpu_count = len(self.currently_allocated_resources.get("gpu_allocations", {}))
        available_gpu_units = self.total_resources.get("gpu_units", 0) - allocated_gpu_count
        if available_gpu_units < required_gpus:
            print(
                f"资源不足 (GPU): 需要 {required_gpus}, 可用 {available_gpu_units} for task {task.get('task_id\')}"
            )
            return False
    for api_name, required_quota in requirements.get("api_quotas", {}).items():
        available_quota = self.total_resources.get("api_quotas", {}).get(
            api_name, 0
        ) - self.currently_allocated_resources.get("api_quotas", {}).get(api_name, 0)
        if available_quota < required_quota:
            print(
                f"资源不足 (
                    API Quota - {api_name}): 需要 {required_quota},
                    可用 {available_quota} for task {task.get('task_id')}
                "
            )
            return False
    return True
def allocate_resources_for_task(self, task: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    task_id = task.get("task_id", f"task_{int(time.time())}")
    if not self.check_resource_availability(task):
        print(f"无法为任务 '{task_id}f' ({task['task_type']}) 分配资源：资源不足。")
        return None
    requirements = self._get_task_resource_requirements(task["task_type"], task.get("parameters", {}))
    allocated_details = {"task_id": task_id}
    self.currently_allocated_resources["cpu_cores"] += requirements.get("cpu_cores", 0)
    self.currently_allocated_resources["ram_gb"] += requirements.get("ram_gb", 0)
    self.currently_allocated_resources["disk_space_gb"] += requirements.get("disk_space_gb", 0)
    required_gpus = requirements.get("gpu_units", 0)
    if required_gpus > 0:
        assigned_gpus = []
        for i in range(self.total_resources.get("gpu_units", 0)):
            gpu_id = f"gpu{i}"
            if gpu_id not in self.currently_allocated_resources.get("gpu_allocations", {}):
                self.currently_allocated_resources.setdefault("gpu_allocations", {})[gpu_id] = task_id
                assigned_gpus.append(gpu_id)
                if len(assigned_gpus) == required_gpus:
                    break
        if len(assigned_gpus) < required_gpus:  # Should not happen if check_resource_availability passed
            print(f"错误: GPU分配逻辑问题 for task {task_id}")
            return None
        allocated_details["assigned_gpus"] = assigned_gpus
    for api_name, required_quota in requirements.get("api_quotas", {}).items():
        self.currently_allocated_resources.setdefault("api_quotas", {}).setdefault(api_name, 0)
        self.currently_allocated_resources["api_quotas"][api_name] += required_quota
        allocated_details.setdefault("api_quotas_used", {})[api_name] = required_quota
    print(f"为任务 '{task_id}f' ({task['task_type']}) 分配的资源: {requirements}。细节: {allocated_details}")
    print(f"当前总已分配资源: {self.currently_allocated_resources}")
    return allocated_details
def release_resources_for_task(self, task: Dict[str, Any], allocated_details: Dict[str, Any]):
    task_id = task.get("task_id")
    requirements = self._get_task_resource_requirements(task["task_type"], task.get("parameters", {}))
    self.currently_allocated_resources["cpu_cores"] -= requirements.get("cpu_cores", 0)
    self.currently_allocated_resources["ram_gb"] -= requirements.get("ram_gb", 0)
    self.currently_allocated_resources["disk_space_gb"] -= requirements.get("disk_space_gb", 0)
    assigned_gpus = allocated_details.get("assigned_gpus", [])
    for gpu_id in assigned_gpus:
        if self.currently_allocated_resources.get("gpu_allocations", {}).get(gpu_id) == task_id:
            del self.currently_allocated_resources["gpu_allocations"][gpu_id]
    for api_name, used_quota in allocated_details.get("api_quotas_used", {}).items():
        if api_name in self.currently_allocated_resources.get("api_quotas", {}):
            self.currently_allocated_resources["api_quotas"][api_name] -= used_quota
    for key in ["cpu_cores", "ram_gb", "disk_space_gb"]:
        self.currently_allocated_resources[key] = max(0, self.currently_allocated_resources[key])
    for api_name in self.currently_allocated_resources.get("api_quotas", {}):
        self.currently_allocated_resources["api_quotas"][api_name] = max(
            0, self.currently_allocated_resources["api_quotas"][api_name]
        )
    print(f"任务 '{task_id}f' ({task['task_type']}) 的资源已释放。")
    print(f"当前总已分配资源: {self.currently_allocated_resources}")
if __name__ == "__main__":
resources_config_gpu = {
    "cpu_cores": 8,
    "gpu_units": 1,
    "gpu_memory_gb_per_unit": {"gpu0": 12},
    "ram_gb": 32,
    "disk_space_gb": 500,
    "api_quotas": {"cloud_render_service": 10},
}
allocator = ResourceAllocator(available_resources=resources_config_gpu)
task1_render = {
    "task_id": "render_job_001",
    "task_type": "render_final_video",
    "parameters": {"estimated_output_size_gb": 5},
}
task2_analyze_gpu = {
    "task_id": "analyze_job_002",
    "task_type": "analyze_media_content",
    "parameters": {"gpu_intensive_analysis": True},
}
task3_load = {"task_id": "load_job_003", "task_type": "load_media", "parameters": {"estimated_size_gb": 10}}
task4_api = {"task_id": "api_job_004", "task_type": "api_cloud_render_service", "parameters": {}}
if allocator.check_resource_availability(task1_render):
    details1 = allocator.allocate_resources_for_task(task1_render)
    if details1:
        print(f"Task 1 ({task1_render['task_type\']}) 分配成功: {details1}")
else:
    print(f"Task 1 ({task1_render['task_type']}) 无法分配资源。")
print("-" * 10)
if allocator.check_resource_availability(task2_analyze_gpu):
    details2 = allocator.allocate_resources_for_task(task2_analyze_gpu)
    if details2:
        print(f"Task 2 ({task2_analyze_gpu['task_type\']}) 分配成功: {details2}")
else:
    print(f"Task 2 ({task2_analyze_gpu['task_type']}) 无法分配资源 (预期，因为GPU可能已被task1占用)。")
print("-" * 10)
if "details1" in locals() and details1:
    allocator.release_resources_for_task(task1_render, details1)
print("-" * 10)
if allocator.check_resource_availability(task2_analyze_gpu):
    details2_retry = allocator.allocate_resources_for_task(task2_analyze_gpu)
    if details2_retry:
        print(f"Task 2 ({task2_analyze_gpu['task_type\']}) 重试分配成功: {details2_retry}")
else:
    print(f"Task 2 ({task2_analyze_gpu['task_type']}) 重试分配失败。")
print("-" * 10)
if allocator.check_resource_availability(task3_load):
    details3 = allocator.allocate_resources_for_task(task3_load)
    if details3:
        print(f"Task 3 ({task3_load['task_type\']}) 分配成功: {details3}")
print("-" * 10)
if allocator.check_resource_availability(task4_api):
    details4 = allocator.allocate_resources_for_task(task4_api)
    if details4:
        print(f"Task 4 ({task4_api['task_type']}) 分配成功: {details4}")
if "details2_retry" in locals() and details2_retry:
    allocator.release_resources_for_task(task2_analyze_gpu, details2_retry)
if "details3" in locals() and details3:
    allocator.release_resources_for_task(task3_load, details3)
if "details4" in locals() and details4:
    allocator.release_resources_for_task(task4_api, details4)
print("\n所有模拟任务完成并释放资源后:")
print(f"最终已分配资源: {allocator.currently_allocated_resources}")