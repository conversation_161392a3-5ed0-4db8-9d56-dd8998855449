#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资源分配器
管理和分配执行任务所需的系统资源 (CPU, GPU, 内存, 存储, API配额等)
"""

import logging
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)


class ResourceAllocator:
    """
    资源分配器类
    
    功能：
    1. 管理系统资源
    2. 分配任务资源
    3. 释放任务资源
    4. 检查资源可用性
    """

    def __init__(self, available_resources: Optional[Dict[str, Any]] = None):
        """
        初始化 ResourceAllocator
        
        Args:
            available_resources: 系统可用资源的总量
        """
        if available_resources is None:
            self.total_resources = {
                "cpu_cores": 4,
                "ram_gb": 16,
                "disk_space_gb": 100,
                "gpu_count": 1,
                "api_quotas": {
                    "openai": 1000,
                    "google": 500,
                    "azure": 800
                }
            }
        else:
            self.total_resources = available_resources
        
        # 当前已分配的资源
        self.currently_allocated_resources = {
            "cpu_cores": 0,
            "ram_gb": 0,
            "disk_space_gb": 0,
            "gpu_count": 0,
            "api_quotas": {key: 0 for key in self.total_resources.get("api_quotas", {})},
            "gpu_allocations": {},  # 跟踪哪个任务在使用哪个GPU
        }
        
        # 资源锁定机制
        self.resource_locks = {}
        
        logger.info(f"ResourceAllocator 初始化完毕。总资源: {self.total_resources}")

    def _get_task_resource_requirements(self, task_type: str, task_parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取任务的资源需求
        
        Args:
            task_type: 任务类型
            task_parameters: 任务参数
            
        Returns:
            资源需求字典
        """
        # 默认资源需求
        requirements = {"cpu_cores": 1, "ram_gb": 2, "disk_space_gb": 1}
        
        # 根据任务类型调整资源需求
        if task_type == "load_media":
            requirements["ram_gb"] = 1
            requirements["disk_space_gb"] = 2
        elif task_type == "video_analysis":
            requirements["cpu_cores"] = 2
            requirements["ram_gb"] = 4
            requirements["disk_space_gb"] = 3
        elif task_type == "video_editing":
            requirements["cpu_cores"] = 2
            requirements["ram_gb"] = 6
            requirements["disk_space_gb"] = 5
            requirements["gpu_count"] = 1  # 需要GPU加速
        elif task_type == "video_rendering":
            requirements["cpu_cores"] = 3
            requirements["ram_gb"] = 8
            requirements["disk_space_gb"] = 10
            requirements["gpu_count"] = 1
        elif task_type == "content_publishing":
            requirements["cpu_cores"] = 1
            requirements["ram_gb"] = 2
            requirements["disk_space_gb"] = 1
        elif task_type.startswith("api_"):
            # API调用任务
            api_name = task_type.split("_", 1)[1]
            if api_name in self.total_resources.get("api_quotas", {}):
                requirements["api_quotas"] = {api_name: 1}  # 默认请求1个配额单位
        
        return requirements

    def check_resource_availability(self, task: Dict[str, Any]) -> bool:
        """
        检查任务所需资源是否可用
        
        Args:
            task: 任务信息
            
        Returns:
            资源是否可用
        """
        requirements = self._get_task_resource_requirements(task["task_type"], task.get("parameters", {}))
        
        # 检查CPU
        available_cpu = self.total_resources["cpu_cores"] - self.currently_allocated_resources["cpu_cores"]
        if available_cpu < requirements.get("cpu_cores", 0):
            logger.warning(f"资源不足 (CPU): 需要 {requirements.get('cpu_cores', 0)}, 可用 {available_cpu} for task {task.get('task_id')}")
            return False
        
        # 检查内存
        available_ram = self.total_resources["ram_gb"] - self.currently_allocated_resources["ram_gb"]
        if available_ram < requirements.get("ram_gb", 0):
            logger.warning(f"资源不足 (RAM): 需要 {requirements.get('ram_gb', 0)}GB, 可用 {available_ram}GB for task {task.get('task_id')}")
            return False
        
        # 检查磁盘空间
        available_disk = self.total_resources["disk_space_gb"] - self.currently_allocated_resources["disk_space_gb"]
        if available_disk < requirements.get("disk_space_gb", 0):
            logger.warning(f"资源不足 (Disk): 需要 {requirements.get('disk_space_gb', 0)}GB, 可用 {available_disk}GB for task {task.get('task_id')}")
            return False
        
        # 检查GPU
        available_gpu = self.total_resources["gpu_count"] - self.currently_allocated_resources["gpu_count"]
        if available_gpu < requirements.get("gpu_count", 0):
            logger.warning(f"资源不足 (GPU): 需要 {requirements.get('gpu_count', 0)}, 可用 {available_gpu} for task {task.get('task_id')}")
            return False
        
        # 检查API配额
        if "api_quotas" in requirements:
            for api_name, quota_needed in requirements["api_quotas"].items():
                available_quota = (self.total_resources.get("api_quotas", {}).get(api_name, 0) - 
                                 self.currently_allocated_resources.get("api_quotas", {}).get(api_name, 0))
                if available_quota < quota_needed:
                    logger.warning(f"资源不足 (API {api_name}): 需要 {quota_needed}, 可用 {available_quota} for task {task.get('task_id')}")
                    return False
        
        return True

    def allocate_resources_for_task(self, task: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        为任务分配资源
        
        Args:
            task: 任务信息
            
        Returns:
            分配的资源详情，如果分配失败则返回None
        """
        if not self.check_resource_availability(task):
            return None
        
        requirements = self._get_task_resource_requirements(task["task_type"], task.get("parameters", {}))
        
        # 分配资源
        allocated_details = {
            "task_id": task.get("task_id"),
            "cpu_cores": requirements.get("cpu_cores", 0),
            "ram_gb": requirements.get("ram_gb", 0),
            "disk_space_gb": requirements.get("disk_space_gb", 0),
            "gpu_count": requirements.get("gpu_count", 0),
            "assigned_gpus": [],
            "api_quotas": requirements.get("api_quotas", {})
        }
        
        # 更新已分配资源
        self.currently_allocated_resources["cpu_cores"] += allocated_details["cpu_cores"]
        self.currently_allocated_resources["ram_gb"] += allocated_details["ram_gb"]
        self.currently_allocated_resources["disk_space_gb"] += allocated_details["disk_space_gb"]
        self.currently_allocated_resources["gpu_count"] += allocated_details["gpu_count"]
        
        # 分配具体的GPU
        if allocated_details["gpu_count"] > 0:
            for i in range(self.total_resources["gpu_count"]):
                gpu_id = f"gpu{i}"
                if gpu_id not in self.currently_allocated_resources["gpu_allocations"]:
                    self.currently_allocated_resources["gpu_allocations"][gpu_id] = task.get("task_id")
                    allocated_details["assigned_gpus"].append(gpu_id)
                    if len(allocated_details["assigned_gpus"]) >= allocated_details["gpu_count"]:
                        break
        
        # 分配API配额
        for api_name, quota_needed in allocated_details["api_quotas"].items():
            if api_name in self.currently_allocated_resources["api_quotas"]:
                self.currently_allocated_resources["api_quotas"][api_name] += quota_needed
        
        logger.info(f"为任务 {task.get('task_id')} 分配资源: {allocated_details}")
        return allocated_details

    def release_resources_for_task(self, task: Dict[str, Any], allocated_details: Dict[str, Any]):
        """
        释放任务的资源
        
        Args:
            task: 任务信息
            allocated_details: 该任务之前被分配的资源详情
        """
        # 释放基础资源
        self.currently_allocated_resources["cpu_cores"] -= allocated_details.get("cpu_cores", 0)
        self.currently_allocated_resources["ram_gb"] -= allocated_details.get("ram_gb", 0)
        self.currently_allocated_resources["disk_space_gb"] -= allocated_details.get("disk_space_gb", 0)
        self.currently_allocated_resources["gpu_count"] -= allocated_details.get("gpu_count", 0)
        
        # 释放GPU
        for gpu_id in allocated_details.get("assigned_gpus", []):
            if gpu_id in self.currently_allocated_resources["gpu_allocations"]:
                del self.currently_allocated_resources["gpu_allocations"][gpu_id]
        
        # 释放API配额
        for api_name, quota_used in allocated_details.get("api_quotas", {}).items():
            if api_name in self.currently_allocated_resources["api_quotas"]:
                self.currently_allocated_resources["api_quotas"][api_name] -= quota_used
        
        logger.info(f"释放任务 {task.get('task_id')} 的资源")

    def get_resource_status(self) -> Dict[str, Any]:
        """
        获取当前资源状态
        
        Returns:
            资源状态信息
        """
        status = {
            "total_resources": self.total_resources,
            "allocated_resources": self.currently_allocated_resources,
            "available_resources": {},
            "utilization_percentage": {}
        }
        
        # 计算可用资源
        status["available_resources"]["cpu_cores"] = (
            self.total_resources["cpu_cores"] - self.currently_allocated_resources["cpu_cores"]
        )
        status["available_resources"]["ram_gb"] = (
            self.total_resources["ram_gb"] - self.currently_allocated_resources["ram_gb"]
        )
        status["available_resources"]["disk_space_gb"] = (
            self.total_resources["disk_space_gb"] - self.currently_allocated_resources["disk_space_gb"]
        )
        status["available_resources"]["gpu_count"] = (
            self.total_resources["gpu_count"] - self.currently_allocated_resources["gpu_count"]
        )
        
        # 计算利用率
        status["utilization_percentage"]["cpu"] = round(
            (self.currently_allocated_resources["cpu_cores"] / self.total_resources["cpu_cores"]) * 100, 2
        )
        status["utilization_percentage"]["ram"] = round(
            (self.currently_allocated_resources["ram_gb"] / self.total_resources["ram_gb"]) * 100, 2
        )
        status["utilization_percentage"]["disk"] = round(
            (self.currently_allocated_resources["disk_space_gb"] / self.total_resources["disk_space_gb"]) * 100, 2
        )
        status["utilization_percentage"]["gpu"] = round(
            (self.currently_allocated_resources["gpu_count"] / self.total_resources["gpu_count"]) * 100, 2
        ) if self.total_resources["gpu_count"] > 0 else 0
        
        return status


# 演示函数
def main():
    """演示资源分配器功能"""
    print("=== 资源分配器演示 ===")
    
    # 初始化资源分配器
    allocator = ResourceAllocator()
    
    # 显示初始资源状态
    print("\n初始资源状态:")
    status = allocator.get_resource_status()
    print(f"总CPU核心: {status['total_resources']['cpu_cores']}")
    print(f"总内存: {status['total_resources']['ram_gb']}GB")
    print(f"总GPU: {status['total_resources']['gpu_count']}")
    
    # 模拟任务
    tasks = [
        {"task_id": "task_1", "task_type": "video_analysis"},
        {"task_id": "task_2", "task_type": "video_editing"},
        {"task_id": "task_3", "task_type": "video_rendering"},
    ]
    
    allocated_resources = []
    
    # 为任务分配资源
    for task in tasks:
        print(f"\n为任务 {task['task_id']} 分配资源...")
        details = allocator.allocate_resources_for_task(task)
        if details:
            allocated_resources.append((task, details))
            print(f"分配成功: {details}")
        else:
            print("分配失败：资源不足")
    
    # 显示当前资源状态
    print("\n当前资源状态:")
    status = allocator.get_resource_status()
    print(f"CPU利用率: {status['utilization_percentage']['cpu']}%")
    print(f"内存利用率: {status['utilization_percentage']['ram']}%")
    print(f"GPU利用率: {status['utilization_percentage']['gpu']}%")
    
    # 释放资源
    print("\n释放任务资源...")
    for task, details in allocated_resources:
        allocator.release_resources_for_task(task, details)
        print(f"释放任务 {task['task_id']} 的资源")
    
    # 显示最终资源状态
    print("\n最终资源状态:")
    final_status = allocator.get_resource_status()
    print(f"CPU利用率: {final_status['utilization_percentage']['cpu']}%")
    print(f"内存利用率: {final_status['utilization_percentage']['ram']}%")
    print(f"GPU利用率: {final_status['utilization_percentage']['gpu']}%")


if __name__ == "__main__":
    main()
