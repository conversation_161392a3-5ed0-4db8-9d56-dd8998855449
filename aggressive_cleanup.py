#!/usr/bin/env python3
"""
激进的代码清理脚本 - 专门处理F841未使用变量问题
"""

import ast
import re
import subprocess
from pathlib import Path
from typing import List


def find_unused_variables(file_path: Path) -> List[str]:
    """使用AST分析找出未使用的变量"""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        ast.parse(content)

        # 这里可以实现更复杂的AST分析
        # 暂时返回空列表，使用正则表达式方法
        return []
    except Exception:
        return []


def clean_unused_exception_variables(file_path: Path) -> bool:
    """清理未使用的异常变量"""
    try:
        content = file_path.read_text(encoding="utf-8")
        original_content = content

        # 模式1: except Exception as e: pass
        content = re.sub(
            r"except\s+(\w+(?:\.\w+)*)\s+as\s+\w+\s*:\s*\n(\s*)pass\s*\n", r"except \1:\n\2pass\n", content
        )

        # 模式2: except Exception as e: return/continue/break
        content = re.sub(
            r"except\s+(\w+(?:\.\w+)*)\s+as\s+\w+\s*:\s*\n(\s*)(return|continue|break)([^\n]*)\n",
            r"except \1:\n\2\3\4\n",
            content,
        )

        # 模式3: except Exception as e: 单行语句(不使用e)
        lines = content.split("\n")
        new_lines = []
        i = 0

        while i < len(lines):
            line = lines[i]

            # 查找except语句
            except_match = re.match(r"(\s*)except\s+(\w+(?:\.\w+)*)\s+as\s+(\w+)\s*:\s*$", line)
            if except_match:
                indent = except_match.group(1)
                exception_type = except_match.group(2)
                var_name = except_match.group(3)

                # 检查下一行是否使用了异常变量
                if i + 1 < len(lines):
                    next_line = lines[i + 1]
                    if var_name not in next_line:
                        # 不使用异常变量，删除变量名
                        new_lines.append(f"{indent}except {exception_type}:")
                    else:
                        new_lines.append(line)
                else:
                    new_lines.append(line)
            else:
                new_lines.append(line)

            i += 1

        content = "\n".join(new_lines)

        if content != original_content:
            file_path.write_text(content, encoding="utf-8")
            return True

        return False

    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False


def clean_unused_assignments(file_path: Path) -> bool:
    """清理明显未使用的赋值语句"""
    try:
        content = file_path.read_text(encoding="utf-8")
        original_content = content

        # 常见的未使用变量模式
        patterns = [
            # base_name = os.path.splitext(...)
            r"(\s*)base_name\s*=\s*os\.path\.splitext\([^)]+\)\[0\]\s*\n",
            #            r'(\s*)ffmpeg_cmd\s*=\s*["\'][^"\']*["\'][^#\n]*\n',
            # timestamp = datetime.now()...
            r"(\s*)timestamp\s*=\s*datetime\.datetime\.now\(\)[^#\n]*\n",
            #            r'(\s*)random_suffix\s*=\s*["\']["\']\.join\([^)]+\)[^#\n]*\n',
            #            r'(\s*)upload_time\s*=\s*[^#\n]*\n',
            #            r'(\s*)hours\s*=\s*int\(seconds\s*//\s*3600\)[^#\n]*\n',
            #            r'(\s*)minutes\s*=\s*int\(\([^)]+\)\s*//\s*60\)[^#\n]*\n',
            # milliseconds = int(...)
            r"(\s*)milliseconds\s*=\s*int\([^)]+\*\s*1000\)[^#\n]*\n",
        ]

        for pattern in patterns:
            content = re.sub(pattern, "", content)

        # 清理连续的空行
        content = re.sub(r"\n\s*\n\s*\n", "\n\n", content)

        if content != original_content:
            file_path.write_text(content, encoding="utf-8")
            return True

        return False

    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False


def clean_unused_variables_in_functions(file_path: Path) -> bool:
    """清理函数中明显未使用的变量"""
    try:
        content = file_path.read_text(encoding="utf-8")
        original_content = content

        # 特定的未使用变量模式
        unused_patterns = [
            # 各种effect参数
            r"(\s*)brightness\s*=\s*params\.get\([^)]+\)[^#\n]*\n",
            r"(\s*)contrast\s*=\s*params\.get\([^)]+\)[^#\n]*\n",
            r"(\s*)saturation\s*=\s*params\.get\([^)]+\)[^#\n]*\n",
            r"(\s*)speed\s*=\s*params\.get\([^)]+\)[^#\n]*\n",
            r"(\s*)blur\s*=\s*params\.get\([^)]+\)[^#\n]*\n",
            # 时间相关
            r"(\s*)start_time_str\s*=\s*[^#\n]*\n",
            r"(\s*)end_time_str\s*=\s*[^#\n]*\n",
            # 位置相关
            r"(\s*)y_position\s*=\s*[^#\n]*\n",
            # 其他常见未使用变量
            r"(\s*)total_entities\s*=\s*sum\([^)]+\)[^#\n]*\n",
        ]

        for pattern in unused_patterns:
            content = re.sub(pattern, "", content)

        if content != original_content:
            file_path.write_text(content, encoding="utf-8")
            return True

        return False

    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return False


def aggressive_cleanup():
    """执行激进清理"""
    print("🔥 开始激进清理未使用变量...")

    python_files = list(Path(".").rglob("*.py"))

    exception_fixed = 0
    assignment_fixed = 0
    function_fixed = 0

    for file_path in python_files:
        if "venv" in str(file_path) or "__pycache__" in str(file_path):
            continue

        print(f"处理: {file_path}")

        # 清理异常变量
        if clean_unused_exception_variables(file_path):
            exception_fixed += 1

        # 清理赋值语句
        if clean_unused_assignments(file_path):
            assignment_fixed += 1

        # 清理函数中的变量
        if clean_unused_variables_in_functions(file_path):
            function_fixed += 1

    print(f"✅ 异常变量清理: {exception_fixed} 个文件")
    print(f"✅ 赋值语句清理: {assignment_fixed} 个文件")
    print(f"✅ 函数变量清理: {function_fixed} 个文件")


def run_autoflake_aggressive():
    """运行更激进的autoflake"""
    print("🧹 运行激进autoflake清理...")

    command = [
        "autoflake",
        "--remove-all-unused-imports",
        "--remove-unused-variables",
        "--remove-duplicate-keys",
        "--in-place",
        "--recursive",
        "--exclude=venv",
        ".",
    ]

    try:
        result = subprocess.run(command, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ autoflake清理完成")
        else:
            print(f"❌ autoflake失败: {result.stderr}")
    except Exception as e:
        print(f"❌ autoflake异常: {e}")


def final_check():
    """最终检查"""
    print("🔍 运行最终检查...")

    try:
        result = subprocess.run(
            [
                "python",
                "-m",
                "flake8",
                "--statistics",
                "--count",
                "--max-line-length=120",
                "--exclude=venv,__pycache__,.git",
                ".",
            ],
            capture_output=True,
            text=True,
        )

        output = result.stdout
        lines = output.strip().split("\n")

        total_issues = 0
        f841_count = 0

        for line in lines:
            if line and not line.startswith(".") and not line.startswith("flake8"):
                parts = line.strip().split()
                if len(parts) >= 2 and parts[0].isdigit():
                    count = int(parts[0])
                    error_type = parts[1]
                    total_issues += count
                    if error_type == "F841":
                        f841_count = count

        print(f"📊 剩余问题总数: {total_issues}")
        print(f"📊 剩余F841问题: {f841_count}")

        return total_issues, f841_count

    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return -1, -1


def main():
    """主函数"""
    print("🚀 激进清理脚本")
    print("=" * 40)

    # 执行清理
    aggressive_cleanup()

    # 运行autoflake
    run_autoflake_aggressive()

    # 最终检查
    total, f841 = final_check()

    if total >= 0:
        print("\n🎯 清理效果:")
        print(f"  总问题数: {total}")
        print(f"  F841问题: {f841}")

        if f841 < 50:
            print("🎉 F841问题已大幅减少!")
        elif f841 < 100:
            print("👍 F841问题有所改善")
        else:
            print("⚠️ F841问题仍然较多，需要手动处理")


if __name__ == "__main__":
    main()
