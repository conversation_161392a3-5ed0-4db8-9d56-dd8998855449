#!/usr/bin/env python3
"""
rule_store module
"""

import json
import os
from typing import Any, Dict, List, Optional

"""存储和管理混剪规则"""
    """
    初始化 RuleStore。
    Args:
        knowledge_base_path (str): 知识库数据存储的根路径。
    """
    """确保知识库目录存在"""
    """
    从JSON文件加载混剪规则。
    Returns:
        Dict[str, Dict[str, Any]]: 加载的规则字典，键为规则ID，值为规则内容。
                                  如果文件不存在或解析失败则返回空字典。
    """
    """将当前规则保存到JSON文件"""
    """
    添加一条新的混剪规则。
    Args:
        rule_id (str): 规则的唯一标识符。
        rule_content (Dict[str, Any]): 规则的具体内容 (例如: {'condition': 'duration > 60', 'action': 'add_intro',
    'priority': 1})。
        overwrite (bool, optional): 如果规则ID已存在，是否覆盖。Defaults to False.
    Returns:
        bool: 如果规则成功添加或更新，则返回True，否则返回False。
    """
    """
    根据ID获取一条规则。
    Args:
        rule_id (str): 规则的唯一标识符。
    Returns:
        Optional[Dict[str, Any]]: 规则内容字典，如果未找到则返回None。
    """
    """获取所有规则"""
    """
    更新现有规则。
    Args:
        rule_id (str): 要更新的规则ID。
        updated_content (Dict[str, Any]): 更新后的规则内容。
    Returns:
        bool: 如果规则成功更新，则返回True，否则返回False (例如规则ID不存在)。
    """
    """
    删除一条规则。
    Args:
        rule_id (str): 要删除的规则ID。
    Returns:
        bool: 如果规则成功删除，则返回True，否则返回False (例如规则ID不存在)。
    """
    """
    查找符合特定条件的规则 (简单示例)。
    实际应用中可能需要更复杂的匹配逻辑或规则引擎。
    Args:
        criteria (Dict[str, Any]): 匹配条件，例如 {'action': 'add_intro'}。
    Returns:
        List[Dict[str, Any]]: 符合条件的规则列表。
    """
DEFAULT_KB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..", "..", "data", "kb")
RULES_FILE_NAME = "editing_rules.json"
class RuleStore:
def __init__(self, knowledge_base_path: str = DEFAULT_KB_PATH):
    self.rules_file_path = os.path.join(knowledge_base_path, RULES_FILE_NAME)
    self._ensure_kb_directory(knowledge_base_path)
    self.rules: Dict[str, Dict[str, Any]] = self._load_rules()
def _ensure_kb_directory(self, kb_path: str):
    if not os.path.exists(kb_path):
        os.makedirs(kb_path)
        print("知识库目录已创建: {kb_path}")
def _load_rules(self) -> Dict[str, Dict[str, Any]]:
    if os.path.exists(self.rules_file_path):
        try:
            with open(self.rules_file_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except json.JSONDecodeError:
            print("错误: 无法解析规则文件 {self.rules_file_path}。将使用空规则集。")
        except Exception as e:
            print(f"加载规则文件 {self.rules_file_path} 时出错: {e}")
    return {}
def _save_rules(self):
    try:
        with open(self.rules_file_path, "w", encoding="utf-8") as f:
            json.dump(self.rules, f, ensure_ascii=False, indent=4)
    except Exception as e:
        print("保存规则文件 {self.rules_file_path} 时出错: {e}")
def add_rule(self, rule_id: str, rule_content: Dict[str, Any], overwrite: bool = False) -> bool:
    if not overwrite and rule_id in self.rules:
        print(f"规则添加失败: 规则ID '{rule_id}' 已存在，且不允许覆盖。")
        return False
    self.rules[rule_id] = rule_content
    self._save_rules()
    print(f"规则 '{rule_id}' 已添加/更新。")
    return True
def get_rule(self, rule_id: str) -> Optional[Dict[str, Any]]:
    return self.rules.get(rule_id)
def get_all_rules(self) -> Dict[str, Dict[str, Any]]:
    return self.rules
def update_rule(self, rule_id: str, updated_content: Dict[str, Any]) -> bool:
    if rule_id not in self.rules:
        print(f"规则更新失败: 规则ID '{rule_id}' 未找到。")
        return False
    self.rules[rule_id].update(updated_content)  # 合并更新
    self._save_rules()
    print(f"规则 '{rule_id}' 已更新。")
    return True
def delete_rule(self, rule_id: str) -> bool:
    if rule_id in self.rules:
        del self.rules[rule_id]
        self._save_rules()
        print(f"规则 '{rule_id}' 已删除。")
        return True
    print(f"规则删除失败: 规则ID '{rule_id}' 未找到。")
    return False
def find_rules_matching_criteria(self, criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
    matched_rules = []
    for rule_id, rule_content in self.rules.items():
        match = True
        for key, value in criteria.items():
            if rule_content.get(key) != value:
                match = False
                break
        if match:
            rule_with_id = rule_content.copy()
            rule_with_id["id"] = rule_id
            matched_rules.append(rule_with_id)
    return matched_rules
if __name__ == "__main__":
test_kb_path = os.path.join(os.getcwd(), "test_kb_data_rules")
if not os.path.exists(test_kb_path):
    os.makedirs(test_kb_path)
rule_store = RuleStore(knowledge_base_path=test_kb_path)
rule_store.add_rule("R001", {"condition": "video_type == 'tutorial'", "action": "add_intro_card", "priority": 10})
rule_store.add_rule("R002", {"condition": "duration < 30", "action": "use_fast_music", "priority": 5})
rule_store.add_rule("R003", {"condition": "contains_speech == True", "action": "generate_subtitles", "priority": 8})
print("所有规则:", rule_store.get_all_rules())
print("规则R001:", rule_store.get_rule("R001"))
rule_store.update_rule("R002", {"priority": 7, "music_genre": "upbeat"})
print("更新后的规则R002:", rule_store.get_rule("R002"))
intro_rules = rule_store.find_rules_matching_criteria({"action": "add_intro_card"})
print("需要片头卡片的规则:", intro_rules)
rule_store.delete_rule("R003")
print("删除R003后所有规则:", rule_store.get_all_rules())