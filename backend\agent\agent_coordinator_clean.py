#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代理协调器
负责协调各个组件，处理用户请求，管理整个视频处理工作流
"""

import asyncio
import logging
from typing import Any, Dict, Optional, Union

from .batch_publisher.batch_publisher import BatchPublisher
from .content_analyzer.content_analyzer import ContentAnalyzer
from .material_manager.material_manager import MaterialManager
from .perception_engine.input_parser import InputParser
from .platform_adapter.platform_adapter import PlatformAdapter
from .smart_editor.smart_editor import SmartEditor

logger = logging.getLogger(__name__)


class AgentCoordinator:
    """
    代理协调器类
    
    功能：
    1. 协调各个组件
    2. 处理用户请求
    3. 管理工作流
    4. 错误处理和恢复
    """

    def __init__(self):
        """初始化代理协调器"""
        # 初始化各个组件
        self.input_parser = InputParser()
        self.material_manager = MaterialManager()
        self.content_analyzer = ContentAnalyzer()
        self.smart_editor = SmartEditor()
        self.platform_adapter = PlatformAdapter()
        self.batch_publisher = BatchPublisher()
        
        logger.info("AgentCoordinator 初始化完成")

    async def process_request(
        self, 
        user_command: Union[str, Dict[str, Any]], 
        config_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        处理用户请求
        
        Args:
            user_command: 用户命令
            config_data: 配置数据
            
        Returns:
            处理结果
        """
        logger.info(f"开始处理请求: {user_command}")
        
        try:
            # 1. 解析用户命令
            parsed_command = self.input_parser.parse_user_command(user_command)
            action = parsed_command.get("action")
            params = parsed_command.get("params", {})
            
            logger.info(f"解析后的命令: action={action}, params={params}")
            
            # 2. 根据动作类型执行相应的处理流程
            if action == "create_and_publish_video":
                return await self._handle_create_and_publish_video(params, config_data)
            elif action == "analyze_video":
                return await self._handle_analyze_video(params)
            elif action == "edit_video":
                return await self._handle_edit_video(params)
            elif action == "help":
                return self._handle_help(params)
            elif action == "error":
                return {
                    "status": "error",
                    "message": parsed_command.get("message", "命令解析失败")
                }
            else:
                return {
                    "status": "error",
                    "message": f"不支持的操作: {action}"
                }
                
        except Exception as e:
            logger.error(f"处理请求时发生错误: {e}")
            return {
                "status": "error",
                "message": f"处理请求时发生错误: {str(e)}"
            }

    async def _handle_create_and_publish_video(
        self, 
        params: Dict[str, Any], 
        config_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """处理创建并发布视频的请求"""
        logger.info("开始创建并发布视频流程")
        
        material_path = params.get("material_path")
        if not material_path:
            return {
                "status": "error",
                "message": "缺少必需参数: material_path"
            }
        
        try:
            # 1. 素材管理
            logger.info("步骤1: 素材管理")
            material_result = await self.material_manager.process_material(material_path)
            if material_result.get("status") != "success":
                return material_result
            
            # 2. 内容分析
            logger.info("步骤2: 内容分析")
            analysis_result = await self.content_analyzer.analyze_content(
                material_result.get("processed_files", [])
            )
            
            # 3. 智能编辑
            logger.info("步骤3: 智能编辑")
            edit_rules = params.get("edit_rules", {})
            edit_result = await self.smart_editor.edit_video(
                material_result.get("processed_files", []),
                analysis_result,
                edit_rules
            )
            
            if edit_result.get("status") != "success":
                return edit_result
            
            # 4. 平台适配
            logger.info("步骤4: 平台适配")
            platforms = params.get("platforms", ["douyin"])
            adapted_videos = {}
            
            for platform in platforms:
                platform_result = await self.platform_adapter.adapt_for_platform(
                    edit_result.get("output_path") or "",
                    platform
                )
                if platform_result.get("status") == "success":
                    adapted_videos[platform] = platform_result.get("output_path")
            
            # 5. 批量发布
            logger.info("步骤5: 批量发布")
            publish_params = {
                "title": params.get("title", "AI生成视频"),
                "description": params.get("description", ""),
                "tags": params.get("tags", [])
            }
            
            publish_result = await self.batch_publisher.publish_to_platforms(
                adapted_videos,
                publish_params
            )
            
            return {
                "status": "success",
                "message": "视频创建并发布完成",
                "material_info": material_result,
                "analysis_info": analysis_result,
                "edit_info": edit_result,
                "adapted_videos": adapted_videos,
                "publish_results": publish_result
            }
            
        except Exception as e:
            logger.error(f"创建并发布视频时发生错误: {e}")
            return {
                "status": "error",
                "message": f"创建并发布视频时发生错误: {str(e)}"
            }

    async def _handle_analyze_video(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理视频分析请求"""
        logger.info("开始视频分析流程")
        
        video_path = params.get("video_path")
        if not video_path:
            return {
                "status": "error",
                "message": "缺少必需参数: video_path"
            }
        
        try:
            analysis_result = await self.content_analyzer.analyze_content([video_path])
            return {
                "status": "success",
                "message": "视频分析完成",
                "analysis_result": analysis_result
            }
        except Exception as e:
            logger.error(f"视频分析时发生错误: {e}")
            return {
                "status": "error",
                "message": f"视频分析时发生错误: {str(e)}"
            }

    async def _handle_edit_video(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理视频编辑请求"""
        logger.info("开始视频编辑流程")
        
        video_path = params.get("video_path")
        if not video_path:
            return {
                "status": "error",
                "message": "缺少必需参数: video_path"
            }
        
        try:
            # 先分析视频
            analysis_result = await self.content_analyzer.analyze_content([video_path])
            
            # 然后编辑视频
            edit_rules = params.get("edit_rules", {})
            edit_result = await self.smart_editor.edit_video(
                [video_path],
                analysis_result,
                edit_rules
            )
            
            return {
                "status": "success",
                "message": "视频编辑完成",
                "edit_result": edit_result
            }
        except Exception as e:
            logger.error(f"视频编辑时发生错误: {e}")
            return {
                "status": "error",
                "message": f"视频编辑时发生错误: {str(e)}"
            }

    def _handle_help(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理帮助请求"""
        command = params.get("command")
        help_text = self.input_parser.get_command_help(command)
        
        return {
            "status": "success",
            "message": "帮助信息",
            "help_text": help_text
        }

    async def shutdown(self):
        """关闭协调器，清理资源"""
        logger.info("正在关闭 AgentCoordinator...")
        
        # 这里可以添加清理逻辑
        # 例如关闭数据库连接、清理临时文件等
        
        logger.info("AgentCoordinator 已关闭")


if __name__ == "__main__":
    # 简单测试
    async def test():
        coordinator = AgentCoordinator()
        result = await coordinator.process_request({
            "action": "help"
        })
        print(f"测试结果: {result}")
    
    asyncio.run(test())
