"""
类型存根文件，用于帮助 Pylance 识别 moviepy.video.fx.all 模块
"""
from typing import Any, Callable

# 视频效果函数
def resize(clip: Any, newsize: Any = None, height: Any = None, width: Any = None, 
           apply_to_mask: bool = True) -> Any:
    pass

def rotate(clip: Any, angle: float, unit: str = 'deg', resample: str = 'bicubic',
           expand: bool = True, bg_color: Any = None, translate: Any = None) -> Any:
    pass

def mirror_x(clip: Any) -> Any:
    pass

def mirror_y(clip: Any) -> Any:
    pass

def crop(clip: Any, x1: int = None, y1: int = None, x2: int = None, y2: int = None,
         width: int = None, height: int = None, x_center: int = None, y_center: int = None) -> Any:
    pass

def fadein(clip: Any, duration: float, initial_color: Any = None) -> Any:
    pass

def fadeout(clip: Any, duration: float, final_color: Any = None) -> Any:
    pass

def speedx(clip: Any, factor: float = None, final_duration: float = None) -> Any:
    pass

def colorx(clip: Any, factor: float) -> Any:
    pass

def blackwhite(clip: Any, RGB: Any = None, preserve_luminosity: bool = True) -> Any:
    pass

def invert_colors(clip: Any) -> Any:
    pass

def loop(clip: Any, n: int = None, duration: float = None, padding: int = 0) -> Any:
    pass

# 导出所有函数
__all__ = ['resize', 'rotate', 'mirror_x', 'mirror_y', 'crop', 'fadein', 'fadeout',
           'speedx', 'colorx', 'blackwhite', 'invert_colors', 'loop']