import datetime
import hashlib
import json
import logging
import os
import shutil
from typing import Any, Dict, List, Optional, Tuple

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


class MaterialManager:
    """
    素材管理器：负责视频、音频、图片等素材的上传、存储、分类、检索和管理。
    """

    def __init__(self, storage_dir: str = None, db_path: str = None):
        """
        初始化素材管理器。

        Args:
            storage_dir: 素材存储目录，默认为当前目录下的 'data/materials'
            db_path: 素材数据库文件路径，默认为 'data/materials_db.json'
        """
        self.storage_dir = storage_dir or os.path.join(os.getcwd(), "data", "materials")
        self.db_path = db_path or os.path.join(os.getcwd(), "data", "materials_db.json")

        # 确保目录存在
        os.makedirs(self.storage_dir, exist_ok=True)
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

        # 素材类型目录
        self.type_dirs = {
            "video": os.path.join(self.storage_dir, "videos"),
            "audio": os.path.join(self.storage_dir, "audios"),
            "image": os.path.join(self.storage_dir, "images"),
            "subtitle": os.path.join(self.storage_dir, "subtitles"),
            "other": os.path.join(self.storage_dir, "others"),
        }

        # 确保素材类型目录存在
        for dir_path in self.type_dirs.values():
            os.makedirs(dir_path, exist_ok=True)

        # 加载素材数据库
        self.materials = self._load_materials_db()

        # 获取下一个ID
        self.next_id = self._get_next_id()

        logger.info("MaterialManager 初始化完成。存储目录: {self.storage_dir}")

    def _load_materials_db(self) -> Dict[str, Dict[str, Any]]:
        """
        加载素材数据库。

        Returns:
            素材数据库字典
        """
        if os.path.exists(self.db_path):
            try:
                with open(self.db_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"加载素材数据库失败: {e}")
                return {}
        else:
            logger.info(f"素材数据库文件不存在，将创建新的数据库")
            return {}

    def _save_materials_db(self) -> None:
        """保存素材数据库"""
        try:
            with open(self.db_path, "w", encoding="utf-8") as f:
                json.dump(self.materials, f, ensure_ascii=False, indent=2)
            logger.info("素材数据库已保存到: {self.db_path}")
        except Exception as e:
            logger.error("保存素材数据库失败: {e}")

    def _get_next_id(self) -> int:
        """
        获取下一个素材ID。

        Returns:
            下一个素材ID
        """
        if not self.materials:
            return 1

        # 找到当前最大ID
        max_id = 0
        for material_id in self.materials:
            try:
                id_num = int(material_id)
                max_id = max(max_id, id_num)
            except ValueError:
                pass

        return max_id + 1

    def _calculate_file_hash(self, file_path: str) -> str:
        """
        计算文件的MD5哈希值。

        Args:
            file_path: 文件路径

        Returns:
            文件的MD5哈希值
        """
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error("计算文件哈希值失败: {e}")
            return ""

    def upload_material(
        self,
        file_path: str,
        material_type: str,
        tags: List[str] = None,
        metadata: Dict[str, Any] = None,
        copy_file: bool = True,
    ) -> Dict[str, Any]:
        """
        上传素材文件。

        Args:
            file_path: 素材文件路径
            material_type: 素材类型（如：video, audio, image）
            tags: 素材标签列表
            metadata: 素材元数据
            copy_file: 是否复制文件到素材库，如果为False则只记录引用

        Returns:
            上传结果字典
        """
        logger.info("上传素材: {file_path}, 类型: {material_type}")

        # 检查文件是否存在
        if not os.path.exists(file_path):
            logger.error(f"文件不存在: {file_path}")
            return {"success": False, "message": "文件不存在: {file_path}"}

        # 检查素材类型是否有效
        if material_type not in self.type_dirs:
            logger.warning(f"未知的素材类型: {material_type}，将使用 'other' 类型")
            material_type = "other"

        # 生成素材ID
        material_id = str(self.next_id)
        self.next_id += 1

        # 计算文件哈希值
        file_hash = self._calculate_file_hash(file_path)

        # 检查是否已存在相同的文件
        for existing_id, material in self.materials.items():
            if material.get("file_hash") == file_hash and file_hash:
                logger.warning(f"发现重复文件: {file_path}，已存在ID: {existing_id}")
                return {"success": False, "message": "文件已存在，ID: {existing_id}", "duplicate_id": existing_id}

        # 确定目标存储路径
        filename = os.path.basename(file_path)
        target_dir = self.type_dirs[material_type]
        target_path = os.path.join(target_dir, "{material_id}_{filename}")

        # 复制或引用文件
        stored_path = target_path if copy_file else file_path

        if copy_file:
            try:
                shutil.copy2(file_path, target_path)
                logger.info("文件已复制到: {target_path}")
            except Exception as e:
                logger.error(f"复制文件失败: {e}")
                return {"success": False, "message": f"复制文件失败: {e}"}

        # 获取文件信息
        file_size = os.path.getsize(file_path)
        file_extension = os.path.splitext(file_path)[1].lower()

        # 创建素材记录
        self.materials[material_id] = {
            "id": material_id,
            "file_path": stored_path,
            "original_path": file_path,
            "material_type": material_type,
            "tags": tags if tags is not None else [],
            "metadata": metadata if metadata is not None else {},
            "file_hash": file_hash,
            "file_size": file_size,
            "file_extension": file_extension,
            "upload_time": datetime.datetime.now().isoformat(),
            "is_reference": not copy_file,
        }

        # 保存素材数据库
        self._save_materials_db()

        logger.info(f"素材上传成功，ID: {material_id}")
        return {
            "success": True,
            "material_id": material_id,
            "message": "素材上传成功",
            "material_info": self.materials[material_id],
        }

    def get_material(self, material_id: str) -> Optional[Dict[str, Any]]:
        """
        根据素材ID获取素材信息。

        Args:
            material_id: 素材ID

        Returns:
            素材信息字典，如果不存在则返回None
        """
        logger.info("获取素材: {material_id}")

        if material_id not in self.materials:
            logger.warning("素材不存在: {material_id}")
            return None

        material = self.materials[material_id]

        # 检查文件是否存在
        if not os.path.exists(material["file_path"]):
            logger.warning(f"素材文件不存在: {material['file_path']}")
            material["file_exists"] = False
        else:
            material["file_exists"] = True

        return material

    def search_materials(
        self,
        query: str = "",
        material_type: str = None,
        tags: List[str] = None,
        metadata_filters: Dict[str, Any] = None,
        sort_by: str = "upload_time",
        sort_order: str = "desc",
        limit: int = 100,
        offset: int = 0,
    ) -> Dict[str, Any]:
        """
        搜索素材。

        Args:
            query: 搜索关键词
            material_type: 素材类型过滤
            tags: 标签过滤
            metadata_filters: 元数据过滤
            sort_by: 排序字段
            sort_order: 排序顺序，'asc' 或 'desc'
            limit: 返回结果数量限制
            offset: 结果偏移量

        Returns:
            搜索结果字典
        """
        logger.info(f"搜索素材: query='{query}', type='{material_type}', tags={tags}")

        # 过滤素材
        results = []
        for material_id, material_info in self.materials.items():
            match = True

            # 类型过滤
            if material_type and material_info["material_type"] != material_type:
                match = False

            # 标签过滤
            if tags and match:
                if not all(tag in material_info["tags"] for tag in tags):
                    match = False

            # 元数据过滤
            if metadata_filters and match:
                for key, value in metadata_filters.items():
                    if key not in material_info.get("metadata", {}) or material_info["metadata"][key] != value:
                        match = False
                        break

            # 关键词搜索
            if query and match:
                query_lower = query.lower()
                # 搜索文件路径
                if query_lower in material_info["file_path"].lower():
                    pass  # 匹配，继续
                # 搜索原始路径
                elif "original_path" in material_info and query_lower in material_info["original_path"].lower():
                    pass  # 匹配，继续
                # 搜索类型
                elif query_lower in material_info["material_type"].lower():
                    pass  # 匹配，继续
                # 搜索标签
                elif any(query_lower in tag.lower() for tag in material_info["tags"]):
                    pass  # 匹配，继续
                # 搜索元数据
                elif any(query_lower in str(v).lower() for v in material_info.get("metadata", {}).values()):
                    pass  # 匹配，继续
                else:
                    match = False

            if match:
                results.append(material_info)

        # 排序
        if sort_by in ["upload_time", "file_size", "id"]:
            reverse = sort_order.lower() == "desc"
            results.sort(key=lambda x: x.get(sort_by, ""), reverse=reverse)

        # 计算总数
        total_count = len(results)

        # 分页
        paginated_results = results[offset : offset + limit]

        return {
            "total": total_count,
            "limit": limit,
            "offset": offset,
            "query": query,
            "material_type": material_type,
            "tags": tags,
            "results": paginated_results,
        }

    def update_material(
        self, material_id: str, tags: List[str] = None, metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        更新素材信息。

        Args:
            material_id: 素材ID
            tags: 新的标签列表，如果为None则不更新
            metadata: 新的元数据，如果为None则不更新

        Returns:
            更新结果字典
        """
        logger.info("更新素材: {material_id}")

        if material_id not in self.materials:
            logger.warning(f"素材不存在: {material_id}")
            return {"success": False, "message": "素材不存在: {material_id}"}

        # 更新标签
        if tags is not None:
            self.materials[material_id]["tags"] = tags

        # 更新元数据
        if metadata is not None:
            if "metadata" not in self.materials[material_id]:
                self.materials[material_id]["metadata"] = {}
            self.materials[material_id]["metadata"].update(metadata)

        # 保存素材数据库
        self._save_materials_db()

        logger.info(f"素材更新成功: {material_id}")
        return {"success": True, "message": "素材更新成功", "material_info": self.materials[material_id]}

    def delete_material(self, material_id: str, delete_file: bool = True) -> Dict[str, Any]:
        """
        删除素材。

        Args:
            material_id: 素材ID
            delete_file: 是否删除文件，如果为False则只删除记录

        Returns:
            删除结果字典
        """
        logger.info("删除素材: {material_id}, delete_file={delete_file}")

        if material_id not in self.materials:
            logger.warning(f"素材不存在: {material_id}")
            return {"success": False, "message": "素材不存在: {material_id}"}

        material = self.materials[material_id]
        file_path = material["file_path"]

        # 删除文件
        if delete_file and not material.get("is_reference", False):
            if os.path.exists(file_path):
                try:
                    os.remove(file_path)
                    logger.info("文件已删除: {file_path}")
                except Exception as e:
                    logger.error(f"删除文件失败: {e}")
                    return {"success": False, "message": "删除文件失败: {e}"}
            else:
                logger.warning("文件不存在: {file_path}")

        # 删除记录
        del self.materials[material_id]

        # 保存素材数据库
        self._save_materials_db()

        logger.info(f"素材删除成功: {material_id}")
        return {"success": True, "message": "素材删除成功", "material_id": material_id}

    def get_material_file(self, material_id: str) -> Tuple[bool, str]:
        """
        获取素材文件路径。

        Args:
            material_id: 素材ID

        Returns:
            (成功标志, 文件路径)
        """
        logger.info("获取素材文件: {material_id}")

        if material_id not in self.materials:
            logger.warning("素材不存在: {material_id}")
            return False, ""

        material = self.materials[material_id]
        file_path = material["file_path"]

        if not os.path.exists(file_path):
            logger.warning("素材文件不存在: {file_path}")
            return False, ""

        return True, file_path

    def get_material_types(self) -> List[str]:
        """
        获取所有素材类型。

        Returns:
            素材类型列表
        """
        return list(self.type_dirs.keys())

    def get_all_tags(self) -> List[str]:
        """
        获取所有标签。

        Returns:
            标签列表
        """
        all_tags = set()
        for material in self.materials.values():
            all_tags.update(material.get("tags", []))
        return list(all_tags)

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取素材统计信息。

        Returns:
            统计信息字典
        """
        stats = {
            "total_count": len(self.materials),
            "total_size": 0,
            "type_counts": {},
            "tag_counts": {},
            "extension_counts": {},
        }

        for material in self.materials.values():
            # 统计总大小
            stats["total_size"] += material.get("file_size", 0)

            # 统计类型数量
            material_type = material.get("material_type", "unknown")
            stats["type_counts"][material_type] = stats["type_counts"].get(material_type, 0) + 1

            # 统计标签数量
            for tag in material.get("tags", []):
                stats["tag_counts"][tag] = stats["tag_counts"].get(tag, 0) + 1

            # 统计扩展名数量
            extension = material.get("file_extension", "").lower()
            if extension:
                stats["extension_counts"][extension] = stats["extension_counts"].get(extension, 0) + 1

        # 转换总大小为可读格式
        stats["total_size_readable"] = self._format_size(stats["total_size"])

        return stats

    def _format_size(self, size_bytes: int) -> str:
        """
        格式化文件大小。

        Args:
            size_bytes: 文件大小（字节）

        Returns:
            格式化后的大小字符串
        """
        for unit in ["B", "KB", "MB", "GB", "TB"]:
            if size_bytes < 1024.0:
                return "{size_bytes:.2f} {unit}"
            size_bytes /= 1024.0
        return "{size_bytes:.2f} PB"
