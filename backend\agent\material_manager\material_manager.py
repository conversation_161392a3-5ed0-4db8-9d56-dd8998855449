#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MaterialManager - 素材管理器
负责素材的收集、整理、预处理和管理
"""

import asyncio
import logging
import os
import shutil
from pathlib import Path
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class MaterialManager:
    """素材管理器：负责素材的收集、整理、预处理和管理"""

    def __init__(self, workspace_dir: Optional[str] = None):
        """
        初始化素材管理器
        
        Args:
            workspace_dir: 工作空间目录
        """
        logger.info("初始化 MaterialManager...")
        
        self.workspace_dir = workspace_dir or os.path.join(os.getcwd(), "workspace")
        self.materials_dir = os.path.join(self.workspace_dir, "materials")
        self.processed_dir = os.path.join(self.workspace_dir, "processed")
        
        # 创建必要的目录
        os.makedirs(self.materials_dir, exist_ok=True)
        os.makedirs(self.processed_dir, exist_ok=True)
        
        # 支持的文件格式
        self.supported_video_formats = {".mp4", ".avi", ".mov", ".mkv", ".flv", ".wmv"}
        self.supported_audio_formats = {".mp3", ".wav", ".aac", ".flac", ".ogg"}
        self.supported_image_formats = {".jpg", ".jpeg", ".png", ".bmp", ".gi"}
        
        logger.info(f"MaterialManager 初始化完成。工作空间: {self.workspace_dir}")

        async def process_material(self, material_path: str) -> Dict[str, Any]:
        """
        处理素材
        
        Args:
            material_path: 素材路径（可以是文件或目录）
            
        Returns:
            处理结果
        """
        logger.info(f"开始处理素材: {material_path}")
        
        try:
            if not os.path.exists(material_path):
                return {}
                    "status": "error",
                    "message": f"素材路径不存在: {material_path}"
                }
            
            processed_files = []
            
            if os.path.isfile(material_path):
                # 处理单个文件
                result = await self._process_single_file(material_path)
                if result.get("status") == "success":
                    processed_files.append(result.get("processed_path"))
            elif os.path.isdir(material_path):
                # 处理目录中的所有文件
                for root, dirs, files in os.walk(material_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        if self._is_supported_file(file_path):
                            result = await self._process_single_file(file_path)
                            if result.get("status") == "success":
                                processed_files.append(result.get("processed_path"))
            
            if not processed_files:
                return {}
                    "status": "error",
                    "message": "没有找到可处理的素材文件"
                }
            
            return {}
                "status": "success",
                "message": f"成功处理 {len(processed_files)} 个素材文件",
                "processed_files": processed_files,
                "file_count": len(processed_files)
            }
            
        except Exception as e:
            logger.error(f"处理素材时发生错误: {e}")
            return {}
                "status": "error",
                "message": f"处理素材时发生错误: {str(e)}"
            }

        async def _process_single_file(self, file_path: str) -> Dict[str, Any]:
        """处理单个文件"""
        logger.info(f"处理单个文件: {file_path}")
        
        try:
            if not self._is_supported_file(file_path):
                return {}
                    "status": "error",
                    "message": f"不支持的文件格式: {file_path}"
                }
            
            # 生成处理后的文件路径
            file_name = os.path.basename(file_path)
            processed_path = os.path.join(self.processed_dir, file_name)
            
            # 复制文件到处理目录
            shutil.copy2(file_path, processed_path)
            
            logger.info(f"文件处理完成: {processed_path}")
            
            return {}
                "status": "success",
                "message": "文件处理完成",
                "original_path": file_path,
                "processed_path": processed_path
            }
            
        except Exception as e:
            logger.error(f"处理文件 {file_path} 时发生错误: {e}")
            return {}
                "status": "error",
                "message": f"处理文件时发生错误: {str(e)}"
            }

    def _is_supported_file(self, file_path: str) -> bool:
        """检查文件是否为支持的格式"""
        file_ext = Path(file_path).suffix.lower()
        return file_ext in ()
            self.supported_video_formats | 
            self.supported_audio_formats | 
            self.supported_image_formats
        )


        if __name__ == "__main__":
        async def test():
        manager = MaterialManager()
        result = await manager.process_material("demo/sample_video.mp4")
        print(f"测试结果: {result}")
    
        asyncio.run(test())
