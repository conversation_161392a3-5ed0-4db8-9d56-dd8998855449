#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IntelliCutAgent CLI 交互接口
提供命令行交互界面，允许用户通过命令行使用 IntelliCutAgent 的功能
"""

import os
import sys
import json
import logging
import asyncio
import argparse
from typing import Dict, Any, List, Optional, Union

# 配置日志
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CLIInterface:
    """命令行交互接口"""
    
    def __init__(self, agent_core):
        """
        初始化命令行交互接口
        
        Args:
            agent_core: 代理核心实例
        """
        self.agent_core = agent_core
        self.running = False
        self.commands = {
            "help": self._cmd_help,
            "exit": self._cmd_exit,
            "create": self._cmd_create_video,
            "analyze": self._cmd_analyze_video,
            "publish": self._cmd_publish_video,
            "revenue": self._cmd_revenue_analysis,
            "batch": self._cmd_batch_process,
            "maintenance": self._cmd_maintenance
        }
    
    async def start(self):
        """启动命令行交互循环"""
        self.running = True
        
        print("\n欢迎使用 IntelliCutAgent 命令行界面！")
        print("输入 'help' 查看可用命令，输入 'exit' 退出。\n")
        
        while self.running:
            try:
                command = input("IntelliCutAgent> ").strip()
                
                if not command:
                    continue
                
                cmd_parts = command.split(maxsplit=1)
                cmd_name = cmd_parts[0].lower()
                cmd_args = cmd_parts[1] if len(cmd_parts) > 1 else ""
                
                if cmd_name in self.commands:
                    await self.commands[cmd_name](cmd_args)
                else:
                    print(f"未知命令: {cmd_name}。输入 'help' 查看可用命令。")
            
            except KeyboardInterrupt:
                print("\n收到中断信号，正在退出...")
                self.running = False
            except Exception as e:
                logger.error(f"命令执行错误: {e}")
                print(f"命令执行错误: {e}")
    
    async def _cmd_help(self, args):
        """显示帮助信息"""
        print("\nIntelliCutAgent 命令行界面帮助:")
        print("  help                     - 显示此帮助信息")
        print("  exit                     - 退出程序")
        print("  create [options]         - 创建并编辑视频")
        print("  analyze [options]        - 分析视频内容")
        print("  publish [options]        - 发布视频到平台")
        print("  revenue [options]        - 收益分析功能")
        print("  batch [options]          - 批量处理视频")
        print("  maintenance [options]    - 系统维护")
        print("\n使用 '[command] --help' 查看特定命令的详细帮助。")
    
    async def _cmd_exit(self, args):
        """退出程序"""
        print("正在退出 IntelliCutAgent...")
        self.running = False
    
    async def _cmd_create_video(self, args):
        """创建并编辑视频"""
        if args == "--help":
            print("\n创建并编辑视频命令帮助:")
            print("  create --material=<path> --duration=<duration> --style=<style> [--output=<path>]")
            print("\n参数:")
            print("  --material=<path>     - 素材文件路径")
            print("  --duration=<duration> - 目标时长，例如: 30s, 2min")
            print("  --style=<style>       - 编辑风格，例如: fast_paced, cinematic")
            print("  --output=<path>       - 输出文件路径 (可选)")
            return
        
        # 解析参数
        parser = argparse.ArgumentParser(description="创建并编辑视频")
        parser.add_argument("--material", required=True, help="素材文件路径")
        parser.add_argument("--duration", default="60s", help="目标时长")
        parser.add_argument("--style", default="standard", help="编辑风格")
        parser.add_argument("--output", help="输出文件路径")
        
        try:
            # 将命令行参数转换为适合 argparse 的格式
            args_list = []
            for arg in args.split():
                if arg.startswith("--"):
                    key_value = arg.split("=", 1)
                    if len(key_value) == 2:
                        args_list.append(key_value[0])
                        args_list.append(key_value[1])
                    else:
                        args_list.append(key_value[0])
                else:
                    args_list.append(arg)
            
            parsed_args = parser.parse_args(args_list)
            
            # 构建请求
            request = {
                "action": "create_and_publish_video",
                "params": {
                    "material_path": parsed_args.material,
                    "platforms": [],  # 不发布
                    "edit_rules": {
                        "duration": parsed_args.duration,
                        "style": parsed_args.style
                    },
                    "output": parsed_args.output
                }
            }
            
            print(f"正在创建视频: 素材={parsed_args.material}, 时长={parsed_args.duration}, 风格={parsed_args.style}")
            
            # 处理请求
            result = await self.agent_core.process_request(request)
            
            if result.get("status") == "success":
                print(f"视频创建成功: {result.get('message')}")
                if "output_path" in result:
                    print(f"输出文件: {result['output_path']}")
            else:
                print(f"视频创建失败: {result.get('message')}")
        
        except Exception as e:
            logger.error(f"解析参数错误: {e}")
            print(f"解析参数错误: {e}")
            print("使用 'create --help' 查看帮助。")
    
    async def _cmd_analyze_video(self, args):
        """分析视频内容"""
        if args == "--help":
            print("\n分析视频内容命令帮助:")
            print("  analyze --video=<path> [--types=<type1,type2,...>] [--output=<path>]")
            print("\n参数:")
            print("  --video=<path>        - 视频文件路径")
            print("  --types=<types>       - 分析类型，逗号分隔，例如: basic,scene_detection,speech_to_text")
            print("  --output=<path>       - 输出文件路径 (可选)")
            return
        
        # 解析参数
        parser = argparse.ArgumentParser(description="分析视频内容")
        parser.add_argument("--video", required=True, help="视频文件路径")
        parser.add_argument("--types", default="basic,scene_detection", help="分析类型，逗号分隔")
        parser.add_argument("--output", help="输出文件路径")
        
        try:
            # 将命令行参数转换为适合 argparse 的格式
            args_list = []
            for arg in args.split():
                if arg.startswith("--"):
                    key_value = arg.split("=", 1)
                    if len(key_value) == 2:
                        args_list.append(key_value[0])
                        args_list.append(key_value[1])
                    else:
                        args_list.append(key_value[0])
                else:
                    args_list.append(arg)
            
            parsed_args = parser.parse_args(args_list)
            
            # 构建请求
            request = {
                "action": "analyze_video",
                "params": {
                    "video_path": parsed_args.video,
                    "analysis_types": parsed_args.types.split(","),
                    "output": parsed_args.output
                }
            }
            
            print(f"正在分析视频: {parsed_args.video}, 分析类型: {parsed_args.types}")
            
            # 处理请求
            result = await self.agent_core.process_request(request)
            
            if result.get("status") == "success":
                print(f"视频分析成功: {result.get('message')}")
                if parsed_args.output and "analysis_results" in result:
                    with open(parsed_args.output, "w", encoding="utf-8") as f:
                        json.dump(result["analysis_results"], f, ensure_ascii=False, indent=2)
                    print(f"分析结果已保存到: {parsed_args.output}")
            else:
                print(f"视频分析失败: {result.get('message')}")
        
        except Exception as e:
            logger.error(f"解析参数错误: {e}")
            print(f"解析参数错误: {e}")
            print("使用 'analyze --help' 查看帮助。")
    
    async def _cmd_publish_video(self, args):
        """发布视频到平台"""
        if args == "--help":
            print("\n发布视频到平台命令帮助:")
            print("  publish --video=<path> --platforms=<platform1,platform2,...> --title=<title> [--description=<desc>] [--tags=<tag1,tag2,...>]")
            print("\n参数:")
            print("  --video=<path>        - 视频文件路径")
            print("  --platforms=<platforms> - 目标平台，逗号分隔，例如: douyin,bilibili")
            print("  --title=<title>       - 视频标题")
            print("  --description=<desc>  - 视频描述 (可选)")
            print("  --tags=<tags>         - 视频标签，逗号分隔 (可选)")
            return
        
        # 解析参数
        parser = argparse.ArgumentParser(description="发布视频到平台")
        parser.add_argument("--video", required=True, help="视频文件路径")
        parser.add_argument("--platforms", required=True, help="目标平台，逗号分隔")
        parser.add_argument("--title", required=True, help="视频标题")
        parser.add_argument("--description", help="视频描述")
        parser.add_argument("--tags", help="视频标签，逗号分隔")
        
        try:
            # 将命令行参数转换为适合 argparse 的格式
            args_list = []
            for arg in args.split():
                if arg.startswith("--"):
                    key_value = arg.split("=", 1)
                    if len(key_value) == 2:
                        args_list.append(key_value[0])
                        args_list.append(key_value[1])
                    else:
                        args_list.append(key_value[0])
                else:
                    args_list.append(arg)
            
            parsed_args = parser.parse_args(args_list)
            
            # 构建请求
            request = {
                "action": "create_and_publish_video",
                "params": {
                    "material_path": parsed_args.video,
                    "platforms": parsed_args.platforms.split(","),
                    "title": parsed_args.title,
                    "description": parsed_args.description or f"由IntelliCutAgent发布的视频: {parsed_args.title}",
                    "tags": parsed_args.tags.split(",") if parsed_args.tags else ["IntelliCutAgent"]
                }
            }
            
            print(f"正在发布视频: {parsed_args.video}, 平台: {parsed_args.platforms}, 标题: {parsed_args.title}")
            
            # 处理请求
            result = await self.agent_core.process_request(request)
            
            if result.get("status") == "success":
                print(f"视频发布成功: {result.get('message')}")
                if "publish_results" in result:
                    for platform, platform_result in result["publish_results"].items():
                        print(f"  平台 {platform} 发布结果: {platform_result.get('status')}")
                        if platform_result.get("url"):
                            print(f"    视频URL: {platform_result.get('url')}")
            else:
                print(f"视频发布失败: {result.get('message')}")
        
        except Exception as e:
            logger.error(f"解析参数错误: {e}")
            print(f"解析参数错误: {e}")
            print("使用 'publish --help' 查看帮助。")
    
    async def _cmd_revenue_analysis(self, args):
        """收益分析功能"""
        if args == "--help":
            print("\n收益分析功能命令帮助:")
            print("  revenue --type=<analysis_type> [--options]")
            print("\n分析类型:")
            print("  --type=platform        - 分析平台收益潜力")
            print("  --type=video           - 分析视频收益表现")
            print("  --type=strategy        - 生成收益优化策略")
            print("  --type=trends          - 跟踪收益趋势")
            print("\n使用 'revenue --type=<analysis_type> --help' 查看特定分析类型的详细帮助。")
            return
        
        # 解析参数
        parser = argparse.ArgumentParser(description="收益分析功能")
        parser.add_argument("--type", required=True, choices=["platform", "video", "strategy", "trends"], help="分析类型")
        parser.add_argument("--help", action="store_true", help="显示帮助信息")
        
        try:
            # 将命令行参数转换为适合 argparse 的格式
            args_list = []
            for arg in args.split():
                if arg.startswith("--"):
                    key_value = arg.split("=", 1)
                    if len(key_value) == 2:
                        args_list.append(key_value[0])
                        args_list.append(key_value[1])
                    else:
                        args_list.append(key_value[0])
                else:
                    args_list.append(arg)
            
            # 只解析 --type 和 --help 参数
            parsed_args, remaining_args = parser.parse_known_args(args_list)
            
            if parsed_args.help:
                if parsed_args.type == "platform":
                    print("\n分析平台收益潜力命令帮助:")
                    print("  revenue --type=platform --platforms=<platform1,platform2,...> [--content-types=<type1,type2,...>] [--time-period=<period>] [--output=<path>]")
                    print("\n参数:")
                    print("  --platforms=<platforms>     - 平台列表，逗号分隔")
                    print("  --content-types=<types>     - 内容类型列表，逗号分隔 (可选)")
                    print("  --time-period=<period>      - 时间周期，例如: daily, weekly, monthly (可选，默认: monthly)")
                    print("  --output=<path>             - 输出文件路径 (可选)")
                elif parsed_args.type == "video":
                    print("\n分析视频收益表现命令帮助:")
                    print("  revenue --type=video --video-id=<id> --platform=<platform> --content-type=<type> --metrics-file=<path> [--output=<path>]")
                    print("\n参数:")
                    print("  --video-id=<id>             - 视频ID")
                    print("  --platform=<platform>       - 平台名称")
                    print("  --content-type=<type>       - 内容类型")
                    print("  --metrics-file=<path>       - 指标文件路径")
                    print("  --output=<path>             - 输出文件路径 (可选)")
                elif parsed_args.type == "strategy":
                    print("\n生成收益优化策略命令帮助:")
                    print("  revenue --type=strategy --user-id=<id> [--platforms=<platform1,platform2,...>] [--content-preferences=<pref1,pref2,...>] [--output=<path>]")
                    print("\n参数:")
                    print("  --user-id=<id>              - 用户ID")
                    print("  --platforms=<platforms>     - 目标平台列表，逗号分隔 (可选)")
                    print("  --content-preferences=<prefs> - 内容偏好列表，逗号分隔 (可选)")
                    print("  --output=<path>             - 输出文件路径 (可选)")
                elif parsed_args.type == "trends":
                    print("\n跟踪收益趋势命令帮助:")
                    print("  revenue --type=trends --user-id=<id> [--time-periods=<period1,period2,...>] [--platforms=<platform1,platform2,...>] [--output=<path>]")
                    print("\n参数:")
                    print("  --user-id=<id>              - 用户ID")
                    print("  --time-periods=<periods>    - 时间周期列表，逗号分隔 (可选，默认: weekly,monthly,quarterly)")
                    print("  --platforms=<platforms>     - 平台列表，逗号分隔 (可选)")
                    print("  --output=<path>             - 输出文件路径 (可选)")
                return
            
            # 根据分析类型处理不同的参数
            if parsed_args.type == "platform":
                await self._cmd_revenue_platform(remaining_args)
            elif parsed_args.type == "video":
                await self._cmd_revenue_video(remaining_args)
            elif parsed_args.type == "strategy":
                await self._cmd_revenue_strategy(remaining_args)
            elif parsed_args.type == "trends":
                await self._cmd_revenue_trends(remaining_args)
        
        except Exception as e:
            logger.error(f"解析参数错误: {e}")
            print(f"解析参数错误: {e}")
            print("使用 'revenue --help' 查看帮助。")
    
    async def _cmd_revenue_platform(self, args):
        """分析平台收益潜力"""
        # 解析参数
        parser = argparse.ArgumentParser(description="分析平台收益潜力")
        parser.add_argument("--platforms", required=True, help="平台列表，逗号分隔")
        parser.add_argument("--content-types", help="内容类型列表，逗号分隔")
        parser.add_argument("--time-period", default="monthly", help="时间周期")
        parser.add_argument("--output", help="输出文件路径")
        
        try:
            # 将命令行参数转换为适合 argparse 的格式
            args_list = []
            for arg in args:
                if arg.startswith("--"):
                    key_value = arg.split("=", 1)
                    if len(key_value) == 2:
                        args_list.append(key_value[0])
                        args_list.append(key_value[1])
                    else:
                        args_list.append(key_value[0])
                else:
                    args_list.append(arg)
            
            parsed_args = parser.parse_args(args_list)
            
            # 构建请求
            request = {
                "action": "analyze_platform_potential",
                "params": {
                    "platforms": parsed_args.platforms.split(","),
                    "content_types": parsed_args.content_types.split(",") if parsed_args.content_types else [],
                    "time_period": parsed_args.time_period,
                    "output": parsed_args.output
                }
            }
            
            print(f"正在分析平台收益潜力: 平台={parsed_args.platforms}, 时间周期={parsed_args.time_period}")
            
            # 处理请求
            result = await self.agent_core.process_request(request)
            
            if result.get("status") == "success":
                print(f"平台收益潜力分析成功: {result.get('message')}")
                if parsed_args.output and "result" in result:
                    with open(parsed_args.output, "w", encoding="utf-8") as f:
                        json.dump(result["result"], f, ensure_ascii=False, indent=2)
                    print(f"分析结果已保存到: {parsed_args.output}")
            else:
                print(f"平台收益潜力分析失败: {result.get('message')}")
        
        except Exception as e:
            logger.error(f"解析参数错误: {e}")
            print(f"解析参数错误: {e}")
            print("使用 'revenue --type=platform --help' 查看帮助。")
    
    async def _cmd_revenue_video(self, args):
        """分析视频收益表现"""
        # 解析参数
        parser = argparse.ArgumentParser(description="分析视频收益表现")
        parser.add_argument("--video-id", required=True, help="视频ID")
        parser.add_argument("--platform", required=True, help="平台名称")
        parser.add_argument("--content-type", required=True, help="内容类型")
        parser.add_argument("--metrics-file", required=True, help="指标文件路径")
        parser.add_argument("--output", help="输出文件路径")
        
        try:
            # 将命令行参数转换为适合 argparse 的格式
            args_list = []
            for arg in args:
                if arg.startswith("--"):
                    key_value = arg.split("=", 1)
                    if len(key_value) == 2:
                        args_list.append(key_value[0])
                        args_list.append(key_value[1])
                    else:
                        args_list.append(key_value[0])
                else:
                    args_list.append(arg)
            
            parsed_args = parser.parse_args(args_list)
            
            # 构建请求
            request = {
                "action": "analyze_video_revenue",
                "params": {
                    "video_id": parsed_args.video_id,
                    "platform": parsed_args.platform,
                    "content_type": parsed_args.content_type,
                    "metrics_file": parsed_args.metrics_file,
                    "output": parsed_args.output
                }
            }
            
            print(f"正在分析视频收益表现: 视频ID={parsed_args.video_id}, 平台={parsed_args.platform}")
            
            # 处理请求
            result = await self.agent_core.process_request(request)
            
            if result.get("status") == "success":
                print(f"视频收益表现分析成功: {result.get('message')}")
                if parsed_args.output and "result" in result:
                    with open(parsed_args.output, "w", encoding="utf-8") as f:
                        json.dump(result["result"], f, ensure_ascii=False, indent=2)
                    print(f"分析结果已保存到: {parsed_args.output}")
            else:
                print(f"视频收益表现分析失败: {result.get('message')}")
        
        except Exception as e:
            logger.error(f"解析参数错误: {e}")
            print(f"解析参数错误: {e}")
            print("使用 'revenue --type=video --help' 查看帮助。")
    
    async def _cmd_revenue_strategy(self, args):
        """生成收益优化策略"""
        # 解析参数
        parser = argparse.ArgumentParser(description="生成收益优化策略")
        parser.add_argument("--user-id", required=True, help="用户ID")
        parser.add_argument("--platforms", help="目标平台列表，逗号分隔")
        parser.add_argument("--content-preferences", help="内容偏好列表，逗号分隔")
        parser.add_argument("--output", help="输出文件路径")
        
        try:
            # 将命令行参数转换为适合 argparse 的格式
            args_list = []
            for arg in args:
                if arg.startswith("--"):
                    key_value = arg.split("=", 1)
                    if len(key_value) == 2:
                        args_list.append(key_value[0])
                        args_list.append(key_value[1])
                    else:
                        args_list.append(key_value[0])
                else:
                    args_list.append(arg)
            
            parsed_args = parser.parse_args(args_list)
            
            # 构建请求
            request = {
                "action": "generate_optimization_strategy",
                "params": {
                    "user_id": parsed_args.user_id,
                    "platforms": parsed_args.platforms.split(",") if parsed_args.platforms else [],
                    "content_preferences": parsed_args.content_preferences.split(",") if parsed_args.content_preferences else [],
                    "output": parsed_args.output
                }
            }
            
            print(f"正在生成收益优化策略: 用户ID={parsed_args.user_id}")
            
            # 处理请求
            result = await self.agent_core.process_request(request)
            
            if result.get("status") == "success":
                print(f"收益优化策略生成成功: {result.get('message')}")
                if parsed_args.output and "result" in result:
                    with open(parsed_args.output, "w", encoding="utf-8") as f:
                        json.dump(result["result"], f, ensure_ascii=False, indent=2)
                    print(f"策略已保存到: {parsed_args.output}")
            else:
                print(f"收益优化策略生成失败: {result.get('message')}")
        
        except Exception as e:
            logger.error(f"解析参数错误: {e}")
            print(f"解析参数错误: {e}")
            print("使用 'revenue --type=strategy --help' 查看帮助。")
    
    async def _cmd_revenue_trends(self, args):
        """跟踪收益趋势"""
        # 解析参数
        parser = argparse.ArgumentParser(description="跟踪收益趋势")
        parser.add_argument("--user-id", required=True, help="用户ID")
        parser.add_argument("--time-periods", help="时间周期列表，逗号分隔")
        parser.add_argument("--platforms", help="平台列表，逗号分隔")
        parser.add_argument("--output", help="输出文件路径")
        
        try:
            # 将命令行参数转换为适合 argparse 的格式
            args_list = []
            for arg in args:
                if arg.startswith("--"):
                    key_value = arg.split("=", 1)
                    if len(key_value) == 2:
                        args_list.append(key_value[0])
                        args_list.append(key_value[1])
                    else:
                        args_list.append(key_value[0])
                else:
                    args_list.append(arg)
            
            parsed_args = parser.parse_args(args_list)
            
            # 构建请求
            request = {
                "action": "track_revenue_trends",
                "params": {
                    "user_id": parsed_args.user_id,
                    "time_periods": parsed_args.time_periods.split(",") if parsed_args.time_periods else ["weekly", "monthly", "quarterly"],
                    "platforms": parsed_args.platforms.split(",") if parsed_args.platforms else [],
                    "output": parsed_args.output
                }
            }
            
            print(f"正在跟踪收益趋势: 用户ID={parsed_args.user_id}")
            
            # 处理请求
            result = await self.agent_core.process_request(request)
            
            if result.get("status") == "success":
                print(f"收益趋势跟踪成功: {result.get('message')}")
                if parsed_args.output and "result" in result:
                    with open(parsed_args.output, "w", encoding="utf-8") as f:
                        json.dump(result["result"], f, ensure_ascii=False, indent=2)
                    print(f"趋势分析已保存到: {parsed_args.output}")
            else:
                print(f"收益趋势跟踪失败: {result.get('message')}")
        
        except Exception as e:
            logger.error(f"解析参数错误: {e}")
            print(f"解析参数错误: {e}")
            print("使用 'revenue --type=trends --help' 查看帮助。")
    
    async def _cmd_batch_process(self, args):
        """批量处理视频"""
        if args == "--help":
            print("\n批量处理视频命令帮助:")
            print("  batch --videos=<path1,path2,...> --type=<process_type> [--options]")
            print("\n参数:")
            print("  --videos=<videos>      - 视频文件路径列表，逗号分隔")
            print("  --type=<type>          - 处理类型: analyze, edit, edit_and_publish")
            print("  --duration=<duration>  - 目标时长，例如: 30s, 2min (用于编辑)")
            print("  --style=<style>        - 编辑风格，例如: fast_paced, cinematic (用于编辑)")
            print("  --platforms=<platforms> - 目标平台，逗号分隔 (用于发布)")
            print("  --output-dir=<dir>     - 输出目录 (可选)")
            return
        
        # 解析参数
        parser = argparse.ArgumentParser(description="批量处理视频")
        parser.add_argument("--videos", required=True, help="视频文件路径列表，逗号分隔")
        parser.add_argument("--type", required=True, choices=["analyze", "edit", "edit_and_publish"], help="处理类型")
        parser.add_argument("--duration", default="60s", help="目标时长")
        parser.add_argument("--style", default="standard", help="编辑风格")
        parser.add_argument("--platforms", help="目标平台，逗号分隔")
        parser.add_argument("--output-dir", help="输出目录")
        
        try:
            # 将命令行参数转换为适合 argparse 的格式
            args_list = []
            for arg in args.split():
                if arg.startswith("--"):
                    key_value = arg.split("=", 1)
                    if len(key_value) == 2:
                        args_list.append(key_value[0])
                        args_list.append(key_value[1])
                    else:
                        args_list.append(key_value[0])
                else:
                    args_list.append(arg)
            
            parsed_args = parser.parse_args(args_list)
            
            # 构建请求
            request = {
                "action": "batch_process_videos",
                "params": {
                    "video_paths": parsed_args.videos.split(","),
                    "process_type": parsed_args.type,
                    "edit_rules": {
                        "duration": parsed_args.duration,
                        "style": parsed_args.style
                    },
                    "platforms": parsed_args.platforms.split(",") if parsed_args.platforms else [],
                    "output_dir": parsed_args.output_dir
                }
            }
            
            print(f"正在批量处理视频: 类型={parsed_args.type}, 视频数量={len(parsed_args.videos.split(','))}")
            
            # 处理请求
            result = await self.agent_core.process_request(request)
            
            if result.get("status") == "success":
                print(f"批量处理视频成功: {result.get('message')}")
                if "results" in result:
                    for i, video_result in enumerate(result["results"]):
                        print(f"  视频 {i+1}: {video_result.get('video_path')}")
                        print(f"    状态: {video_result.get('status')}")
                        if video_result.get("edited_video_path"):
                            print(f"    编辑后的视频: {video_result.get('edited_video_path')}")
                        if video_result.get("publish_results"):
                            for platform, platform_result in video_result["publish_results"].items():
                                print(f"    平台 {platform} 发布结果: {platform_result.get('status')}")
            else:
                print(f"批量处理视频失败: {result.get('message')}")
        
        except Exception as e:
            logger.error(f"解析参数错误: {e}")
            print(f"解析参数错误: {e}")
            print("使用 'batch --help' 查看帮助。")
    
    async def _cmd_maintenance(self, args):
        """系统维护"""
        if args == "--help":
            print("\n系统维护命令帮助:")
            print("  maintenance --type=<maintenance_type> [--days-to-keep=<days>]")
            print("\n参数:")
            print("  --type=<type>          - 维护类型: full, cache, data, model")
            print("  --days-to-keep=<days>  - 保留数据的天数 (用于数据清理，默认: 30)")
            return
        
        # 解析参数
        parser = argparse.ArgumentParser(description="系统维护")
        parser.add_argument("--type", default="full", choices=["full", "cache", "data", "model"], help="维护类型")
        parser.add_argument("--days-to-keep", type=int, default=30, help="保留数据的天数")
        
        try:
            # 将命令行参数转换为适合 argparse 的格式
            args_list = []
            for arg in args.split():
                if arg.startswith("--"):
                    key_value = arg.split("=", 1)
                    if len(key_value) == 2:
                        args_list.append(key_value[0])
                        args_list.append(key_value[1])
                    else:
                        args_list.append(key_value[0])
                else:
                    args_list.append(arg)
            
            parsed_args = parser.parse_args(args_list)
            
            # 构建请求
            request = {
                "action": "system_maintenance",
                "params": {
                    "maintenance_type": parsed_args.type,
                    "days_to_keep": parsed_args.days_to_keep
                }
            }
            
            print(f"正在执行系统维护: 类型={parsed_args.type}")
            
            # 处理请求
            result = await self.agent_core.process_request(request)
            
            if result.get("status") == "success":
                print(f"系统维护成功: {result.get('message')}")
                if "results" in result:
                    for maintenance_type, maintenance_result in result["results"].items():
                        print(f"  {maintenance_type}: {maintenance_result.get('status')}")
                        if maintenance_result.get("details"):
                            print(f"    详情: {maintenance_result.get('details')}")
            else:
                print(f"系统维护失败: {result.get('message')}")
        
        except Exception as e:
            logger.error(f"解析参数错误: {e}")
            print(f"解析参数错误: {e}")
            print("使用 'maintenance --help' 查看帮助。")

async def run_cli_interface(agent_core):
    """
    运行命令行交互界面
    
    Args:
        agent_core: 代理核心实例
    """
    cli = CLIInterface(agent_core)
    await cli.start()

if __name__ == "__main__":
    # 测试代码
    from main import IntelliCutAgentCore
    
    async def test_cli():
        agent = IntelliCutAgentCore()
        await run_cli_interface(agent)
    
    # 运行测试
    asyncio.run(test_cli())