#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IntelliCutAgent CLI 交互接口
提供命令行交互界面，允许用户通过命令行使用 IntelliCutAgent 的功能
"""

import argparse
import asyncio
import json
import logging
import sys
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class CLIInterface:
    """命令行交互接口"""

    def __init__(self, agent_core):
        """
        初始化命令行交互接口
        
        Args:
            agent_core: 代理核心实例
        """
        self.agent_core = agent_core
        self.running = False
        
        # 命令映射
        self.commands = {
            "help": self._cmd_help,
            "exit": self._cmd_exit,
            "create": self._cmd_create_video,
            "analyze": self._cmd_analyze_video,
            "publish": self._cmd_publish_video,
            "revenue": self._cmd_revenue_analysis,
            "batch": self._cmd_batch_process,
            "maintenance": self._cmd_maintenance,
        }
        
        logger.info("CLI接口初始化完成")

    async def run(self):
        """运行命令行交互界面"""
        self.running = True
        print("\n🎬 欢迎使用 IntelliCutAgent 命令行界面!")
        print("输入 'help' 查看可用命令，输入 'exit' 退出程序。\n")
        
        while self.running:
            try:
                user_input = input("IntelliCutAgent> ").strip()
                if not user_input:
                    continue
                
                # 解析命令和参数
                parts = user_input.split()
                command = parts[0].lower()
                args = parts[1:] if len(parts) > 1 else []
                
                # 执行命令
                if command in self.commands:
                    await self.commands[command](args)
                else:
                    print(f"未知命令: {command}. 输入 'help' 查看可用命令。")
                    
            except KeyboardInterrupt:
                print("\n\n正在退出...")
                self.running = False
            except Exception as e:
                logger.error(f"命令执行错误: {e}")
                print(f"命令执行错误: {e}")

    async def _cmd_help(self, args: List[str]):
        """显示帮助信息"""
        print("\nIntelliCutAgent 命令行界面帮助:")
        print("  help                     - 显示此帮助信息")
        print("  exit                     - 退出程序")
        print("  create [options]         - 创建和编辑视频")
        print("  analyze [options]        - 分析视频内容")
        print("  publish [options]        - 发布视频到平台")
        print("  revenue [options]        - 收益分析和预测")
        print("  batch [options]          - 批量处理视频")
        print("  maintenance [options]    - 系统维护")
        print("\n使用 '[command] --help' 查看特定命令的详细帮助。")

    async def _cmd_exit(self, args: List[str]):
        """退出程序"""
        print("正在退出 IntelliCutAgent...")
        self.running = False

    async def _cmd_create_video(self, args: List[str]):
        """创建和编辑视频"""
        parser = argparse.ArgumentParser(description="创建和编辑视频")
        parser.add_argument("--input", "-i", required=True, help="输入视频文件路径")
        parser.add_argument("--output", "-o", help="输出文件路径")
        parser.add_argument("--duration", "-d", default="30s", help="目标时长 (例如: 30s, 1m)")
        parser.add_argument("--style", "-s", default="standard", help="编辑风格")
        parser.add_argument("--platforms", "-p", nargs="+", default=["douyin"], help="目标平台")
        
        try:
            parsed_args = parser.parse_args(args)
            
            print(f"开始创建视频...")
            print(f"  输入文件: {parsed_args.input}")
            print(f"  输出路径: {parsed_args.output or '自动生成'}")
            print(f"  目标时长: {parsed_args.duration}")
            print(f"  编辑风格: {parsed_args.style}")
            print(f"  目标平台: {', '.join(parsed_args.platforms)}")
            
            # 构建请求参数
            request_data = {
                "action": "create_and_publish_video",
                "params": {
                    "material_path": parsed_args.input,
                    "platforms": parsed_args.platforms,
                    "edit_rules": {
                        "duration": parsed_args.duration,
                        "style": parsed_args.style,
                        "transitions": ["fade", "wipe"],
                        "effects": ["zoom", "slow_motion"]
                    },
                    "title": "AI生成视频",
                    "description": "由IntelliCutAgent自动生成",
                    "tags": ["AI", "自动生成"]
                }
            }
            
            if parsed_args.output:
                request_data["params"]["output_path"] = parsed_args.output
            
            # 调用代理核心
            result = await self.agent_core.process_request(request_data)
            
            if result.get("status") == "success":
                print("✅ 视频创建成功!")
                if "publish_results" in result:
                    print("\n📤 发布结果:")
                    for platform, platform_result in result["publish_results"].items():
                        status = platform_result.get("status", "unknown")
                        if status == "success":
                            url = platform_result.get("url", "N/A")
                            print(f"  {platform}: ✅ 成功 - {url}")
                        else:
                            error = platform_result.get("error", "未知错误")
                            print(f"  {platform}: ❌ 失败 - {error}")
            else:
                print(f"❌ 视频创建失败: {result.get('message', '未知错误')}")
                
        except SystemExit:
            # argparse 调用 sys.exit() 时会抛出 SystemExit
            pass
        except Exception as e:
            logger.error(f"解析参数错误: {e}")
            print(f"解析参数错误: {e}")
            print("使用 'create --help' 查看帮助。")

    async def _cmd_analyze_video(self, args: List[str]):
        """分析视频内容"""
        parser = argparse.ArgumentParser(description="分析视频内容")
        parser.add_argument("--input", "-i", required=True, help="输入视频文件路径")
        parser.add_argument("--types", "-t", nargs="+", default=["basic", "speech_to_text"], help="分析类型")
        parser.add_argument("--output", "-o", help="分析结果输出文件路径")
        
        try:
            parsed_args = parser.parse_args(args)
            
            print(f"开始分析视频...")
            print(f"  输入文件: {parsed_args.input}")
            print(f"  分析类型: {', '.join(parsed_args.types)}")
            
            # 构建请求参数
            request_data = {
                "action": "analyze_audio",
                "params": {
                    "audio_path": parsed_args.input,
                    "analysis_types": parsed_args.types
                }
            }
            
            if parsed_args.output:
                request_data["params"]["output_path"] = parsed_args.output
            
            # 调用代理核心
            result = await self.agent_core.process_request(request_data)
            
            if result.get("status") == "success":
                print("✅ 视频分析完成!")
                if "result" in result:
                    print("\n📊 分析结果:")
                    analysis_result = result["result"]
                    for analysis_type, data in analysis_result.items():
                        print(f"  {analysis_type}: {json.dumps(data, ensure_ascii=False, indent=2)}")
            else:
                print(f"❌ 视频分析失败: {result.get('message', '未知错误')}")
                
        except SystemExit:
            pass
        except Exception as e:
            logger.error(f"解析参数错误: {e}")
            print(f"解析参数错误: {e}")
            print("使用 'analyze --help' 查看帮助。")

    async def _cmd_publish_video(self, args: List[str]):
        """发布视频到平台"""
        parser = argparse.ArgumentParser(description="发布视频到平台")
        parser.add_argument("--input", "-i", required=True, help="输入视频文件路径")
        parser.add_argument("--platforms", "-p", nargs="+", required=True, help="目标平台")
        parser.add_argument("--title", "-t", required=True, help="视频标题")
        parser.add_argument("--description", "-d", default="", help="视频描述")
        parser.add_argument("--tags", nargs="+", default=[], help="视频标签")
        
        try:
            parsed_args = parser.parse_args(args)
            
            print(f"开始发布视频...")
            print(f"  输入文件: {parsed_args.input}")
            print(f"  目标平台: {', '.join(parsed_args.platforms)}")
            print(f"  标题: {parsed_args.title}")
            
            # 构建请求参数
            request_data = {
                "action": "publish_video",
                "params": {
                    "video_path": parsed_args.input,
                    "platforms": parsed_args.platforms,
                    "metadata": {
                        "title": parsed_args.title,
                        "description": parsed_args.description,
                        "tags": parsed_args.tags
                    }
                }
            }
            
            # 调用代理核心
            result = await self.agent_core.process_request(request_data)
            
            if result.get("status") == "success":
                print("✅ 视频发布完成!")
                if "result" in result:
                    publish_result = result["result"]
                    for platform, platform_result in publish_result.items():
                        status = platform_result.get("status", "unknown")
                        if status == "success":
                            url = platform_result.get("url", "N/A")
                            print(f"  {platform}: ✅ 成功 - {url}")
                        else:
                            error = platform_result.get("error", "未知错误")
                            print(f"  {platform}: ❌ 失败 - {error}")
            else:
                print(f"❌ 视频发布失败: {result.get('message', '未知错误')}")
                
        except SystemExit:
            pass
        except Exception as e:
            logger.error(f"解析参数错误: {e}")
            print(f"解析参数错误: {e}")
            print("使用 'publish --help' 查看帮助。")

    async def _cmd_revenue_analysis(self, args: List[str]):
        """收益分析和预测"""
        parser = argparse.ArgumentParser(description="收益分析和预测")
        parser.add_argument("--platform", "-p", required=True, help="平台名称")
        parser.add_argument("--video-id", "-v", help="视频ID")
        parser.add_argument("--type", "-t", default="analysis", choices=["analysis", "prediction", "comparison"], help="分析类型")
        
        try:
            parsed_args = parser.parse_args(args)
            
            print(f"开始收益分析...")
            print(f"  平台: {parsed_args.platform}")
            print(f"  分析类型: {parsed_args.type}")
            
            if parsed_args.type == "analysis":
                action = "analyze_revenue"
            elif parsed_args.type == "prediction":
                action = "predict_revenue"
            else:
                action = "compare_platforms"
            
            # 构建请求参数
            request_data = {
                "action": action,
                "params": {
                    "platform": parsed_args.platform
                }
            }
            
            if parsed_args.video_id:
                request_data["params"]["video_id"] = parsed_args.video_id
            
            # 调用代理核心
            result = await self.agent_core.process_request(request_data)
            
            if result.get("status") == "success":
                print("✅ 收益分析完成!")
                if "revenue_analysis" in result:
                    analysis = result["revenue_analysis"]
                    print(f"\n💰 收益分析结果:")
                    print(json.dumps(analysis, ensure_ascii=False, indent=2))
            else:
                print(f"❌ 收益分析失败: {result.get('message', '未知错误')}")
                
        except SystemExit:
            pass
        except Exception as e:
            logger.error(f"解析参数错误: {e}")
            print(f"解析参数错误: {e}")
            print("使用 'revenue --help' 查看帮助。")

    async def _cmd_batch_process(self, args: List[str]):
        """批量处理视频"""
        parser = argparse.ArgumentParser(description="批量处理视频")
        parser.add_argument("--input-dir", "-i", required=True, help="输入目录路径")
        parser.add_argument("--output-dir", "-o", help="输出目录路径")
        parser.add_argument("--platforms", "-p", nargs="+", default=["douyin"], help="目标平台")
        parser.add_argument("--style", "-s", default="standard", help="编辑风格")
        
        try:
            parsed_args = parser.parse_args(args)
            
            print(f"开始批量处理...")
            print(f"  输入目录: {parsed_args.input_dir}")
            print(f"  输出目录: {parsed_args.output_dir or '自动生成'}")
            print(f"  目标平台: {', '.join(parsed_args.platforms)}")
            print(f"  编辑风格: {parsed_args.style}")
            
            # 这里可以实现批量处理逻辑
            print("⚠️ 批量处理功能正在开发中...")
            
        except SystemExit:
            pass
        except Exception as e:
            logger.error(f"解析参数错误: {e}")
            print(f"解析参数错误: {e}")
            print("使用 'batch --help' 查看帮助。")

    async def _cmd_maintenance(self, args: List[str]):
        """系统维护"""
        parser = argparse.ArgumentParser(description="系统维护")
        parser.add_argument("--action", "-a", required=True, choices=["clean", "backup", "status"], help="维护操作")
        
        try:
            parsed_args = parser.parse_args(args)
            
            print(f"执行系统维护: {parsed_args.action}")
            
            if parsed_args.action == "clean":
                print("🧹 清理临时文件...")
                print("✅ 清理完成")
            elif parsed_args.action == "backup":
                print("💾 备份系统数据...")
                print("✅ 备份完成")
            elif parsed_args.action == "status":
                print("📊 系统状态检查...")
                print("✅ 系统运行正常")
            
        except SystemExit:
            pass
        except Exception as e:
            logger.error(f"解析参数错误: {e}")
            print(f"解析参数错误: {e}")
            print("使用 'maintenance --help' 查看帮助。")


async def run_cli_interface(agent_core):
    """
    运行命令行交互界面
    
    Args:
        agent_core: 代理核心实例
    """
    cli = CLIInterface(agent_core)
    await cli.run()


# 演示函数
async def main():
    """演示CLI接口"""
    # 这里需要导入实际的代理核心
    from main import IntelliCutAgentCore
    
    agent_core = IntelliCutAgentCore()
    await run_cli_interface(agent_core)


if __name__ == "__main__":
    asyncio.run(main())
