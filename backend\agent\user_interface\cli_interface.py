#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
命令行界面
提供用户与系统交互的命令行接口
"""

import json
import logging
import os
import sys
from typing import Any, Dict, List, Optional

# 添加项目根目录到路径
current_dir = os.path.dirname(__file__)
backend_dir = os.path.join(current_dir, "..", "..")
sys.path.insert(0, os.path.abspath(backend_dir))

from agent.agent_coordinator import AgentCoordinator

logger = logging.getLogger(__name__)


class CLIInterface:
    """
    命令行界面类
    
    功能：
    1. 命令解析
    2. 用户交互
    3. 结果展示
    4. 帮助信息
    """

    def __init__(self):
        """初始化CLI界面"""
        self.coordinator = AgentCoordinator()
        self.commands = {}
            "help": self.show_help,
            "analyze": self.analyze_video,
            "edit": self.edit_video,
            "publish": self.publish_video,
            "status": self.show_status,
            "config": self.manage_config,
            "exit": self.exit_program,
            "quit": self.exit_program
        }
        
        logger.info("CLI界面初始化完成")

    def run(self):
        """运行CLI界面"""
        print("🎬 欢迎使用 IntelliCutAgent 智能视频处理系统")
        print("输入 'help' 查看可用命令，输入 'exit' 退出程序")
        print("-" * 50)
        
        while True:
            try:
                user_input = input("\n> ").strip()
                
                if not user_input:
                    continue
                
                # 解析命令
                parts = user_input.split()
                command = parts[0].lower()
                args = parts[1:] if len(parts) > 1 else []
                
                # 执行命令:
                if command in self.commands:
                    self.commands[command](args)
                else:
                    print(f"❌ 未知命令: {command}")
                    print("输入 'help' 查看可用命令")
                    
            except KeyboardInterrupt:
                print("\n\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")
                logger.error(f"CLI错误: {e}")

    def show_help(self, args: List[str]):
        """显示帮助信息"""
        if args and args[0] in self.commands:
            # 显示特定命令的帮助
            command = args[0]
            help_text = self._get_command_help(command)
            print(help_text)
        else:
            # 显示所有命令的帮助
            print("\n📖 可用命令:")
            print("  help [command]     - 显示帮助信息")
            print("  analyze <video>    - 分析视频文件")
            print("  edit <video>       - 编辑视频文件")
            print("  publish <video>    - 发布视频到平台")
            print("  status             - 显示系统状态")
            print("  config [option]    - 管理配置")
            print("  exit/quit          - 退出程序")
            print("\n💡 提示: 使用 'help <command>' 查看具体命令的详细说明")

    def analyze_video(self, args: List[str]):
        """分析视频"""
        if not args:
            print("❌ 请指定视频文件路径")
            print("用法: analyze <video_path> [analysis_types]")
            return
        
        video_path = args[0]
        analysis_types = args[1:] if len(args) > 1 else ["basic"]
        :
        if not os.path.exists(video_path):
            print(f"❌ 视频文件不存在: {video_path}")
            return
        
        print(f"🔍 开始分析视频: {video_path}")
        print(f"分析类型: {', '.join(analysis_types)}")
        
        try:
            # 构建请求
            request_data = {}
                "action": "analyze_video",
                "params": {}
                    "video_path": video_path,
                    "analysis_types": analysis_types
                }
            }
            
            # 执行分析
            result = self.coordinator.process_request(request_data)
            
            if result.get("status") == "success":
                print("✅ 视频分析完成!")
                self._display_analysis_result(result.get("result", {}))
            else:
                print(f"❌ 分析失败: {result.get('message', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 分析过程中发生错误: {e}")

    def edit_video(self, args: List[str]):
        """编辑视频"""
        if not args:
            print("❌ 请指定视频文件路径")
            print("用法: edit <video_path> [output_path]")
            return
        
        video_path = args[0]
        output_path = args[1] if len(args) > 1 else None
        :
        if not os.path.exists(video_path):
            print(f"❌ 视频文件不存在: {video_path}")
            return
        
        print(f"✂️ 开始编辑视频: {video_path}")
        
        try:
            # 构建请求
            request_data = {}
                "action": "edit_video",
                "params": {}
                    "video_path": video_path,
                    "output_path": output_path
                }
            }
            
            # 执行编辑
            result = self.coordinator.process_request(request_data)
            
            if result.get("status") == "success":
                print("✅ 视频编辑完成!")
                print(f"输出文件: {result.get('edited_video_path')}")
            else:
                print(f"❌ 编辑失败: {result.get('message', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 编辑过程中发生错误: {e}")

    def publish_video(self, args: List[str]):
        """发布视频"""
        if not args:
            print("❌ 请指定视频文件路径")
            print("用法: publish <video_path> [platforms...]")
            return
        
        video_path = args[0]
        platforms = args[1:] if len(args) > 1 else ["douyin", "bilibili"]
        :
        if not os.path.exists(video_path):
            print(f"❌ 视频文件不存在: {video_path}")
            return
        
        print(f"📤 开始发布视频: {video_path}")
        print(f"目标平台: {', '.join(platforms)}")
        
        try:
            # 构建请求
            request_data = {}
                "action": "publish_video",
                "params": {}
                    "video_path": video_path,
                    "platforms": platforms
                }
            }
            
            # 执行发布
            result = self.coordinator.process_request(request_data)
            
            if result.get("status") == "success":
                print("✅ 视频发布完成!")
                self._display_publish_result(result.get("result", {}))
            else:
                print(f"❌ 发布失败: {result.get('message', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 发布过程中发生错误: {e}")

    def show_status(self, args: List[str]):
        """显示系统状态"""
        print("📊 系统状态:")
        
        try:
            # 获取任务历史
            task_history = self.coordinator.get_task_history(limit=5)
            
            if task_history.get("status") == "success":
                tasks = task_history.get("tasks", [])
                print(f"  总任务数: {task_history.get('total', 0)}")
                print(f"  最近任务:")
                
                for task in tasks:
                    status_icon = "✅" if task.get("status") == "success" else "❌" if task.get("status") == "error" else "⏳"
                    print(f"    {status_icon} {task.get('action', 'unknown')} - {task.get('start_time', 'unknown')}"):
            else:
                print("  无法获取任务历史")
            
            # 显示配置信息
            user_preferences = self.coordinator.get_user_preferences()
            if user_preferences.get("status") == "success":
                config = user_preferences.get("user_config", {})
                print(f"  默认平台: {', '.join(config.get('preferred_platforms', []))}")
                print(f"  默认时长: {config.get('default_edit_rules', {}).get('duration', 'unknown')}")
            
        except Exception as e:
            print(f"❌ 获取状态失败: {e}")

    def manage_config(self, args: List[str]):
        """管理配置"""
        if not args:
            print("📋 配置管理:")
            print("  config show        - 显示当前配置")
            print("  config platforms   - 设置默认平台")
            print("  config duration    - 设置默认时长")
            return
        
        action = args[0].lower()
        
        if action == "show":
            self._show_config()
        elif action == "platforms":
            self._set_platforms(args[1:])
        elif action == "duration":
            self._set_duration(args[1:])
        else:
            print(f"❌ 未知配置选项: {action}")

    def exit_program(self, args: List[str]):
        """退出程序"""
        print("👋 再见！")
        sys.exit(0)

    def _display_analysis_result(self, result: Dict[str, Any]):
        """显示分析结果"""
        print("\n📊 分析结果:")
        
        if "basic_info" in result:
            basic = result["basic_info"]
            print(f"  时长: {basic.get('duration', 'unknown')}秒")
            print(f"  分辨率: {basic.get('resolution', 'unknown')}")
            print(f"  帧率: {basic.get('fps', 'unknown')}fps")
        
        if "scenes" in result:
            scenes = result["scenes"]
            print(f"  场景数: {len(scenes)}")
            for i, scene in enumerate(scenes[:3], 1):  # 只显示前3个场景
                print(f"    场景{i}: {scene.get('start_time', 0):.1f}s - {scene.get('end_time', 0):.1f}s")
        
        if "quality" in result:
            quality = result["quality"]
            print(f"  质量评分: {quality.get('overall_score', 0):.2f}")
            print(f"  质量等级: {quality.get('quality_grade', 'unknown')}")

    def _display_publish_result(self, result: Dict[str, Any]):
        """显示发布结果"""
        print("\n📤 发布结果:")
        
        for platform, platform_result in result.items():
            status_icon = "✅" if platform_result.get("status") == "success" else "❌":
            print(f"  {status_icon} {platform}: {platform_result.get('message', 'unknown')}")
            
            if platform_result.get("status") == "success" and "url" in platform_result:
                print(f"    链接: {platform_result['url']}")

    def _show_config(self):
        """显示当前配置"""
        try:
            user_preferences = self.coordinator.get_user_preferences()
            
            if user_preferences.get("status") == "success":
                config = user_preferences.get("user_config", {})
                print("\n⚙️ 当前配置:")
                print(f"  默认平台: {', '.join(config.get('preferred_platforms', []))}")
                
                edit_rules = config.get('default_edit_rules', {})
                print(f"  默认时长: {edit_rules.get('duration', 'unknown')}")
                print(f"  默认风格: {edit_rules.get('style', 'unknown')}")
                print(f"  默认转场: {', '.join(edit_rules.get('transitions', []))}")
                
                metadata = config.get('default_metadata', {})
                print(f"  默认标题: {metadata.get('title', 'unknown')}")
                print(f"  默认标签: {', '.join(metadata.get('tags', []))}")
            else:
                print("❌ 无法获取配置信息")
                
        except Exception as e:
            print(f"❌ 显示配置失败: {e}")

    def _set_platforms(self, platforms: List[str]):
        """设置默认平台"""
        if not platforms:
            print("❌ 请指定平台")
            print("可用平台: douyin, bilibili, youtube, xiaohongshu, toutiao")
            return
        
        available_platforms = ["douyin", "bilibili", "youtube", "xiaohongshu", "toutiao"]
        valid_platforms = [p for p in platforms if p in available_platforms]
        :
        if not valid_platforms:
            print("❌ 没有有效的平台")
            return
        
        try:
            preferences = {"preferred_platforms": valid_platforms}
            result = self.coordinator.update_user_preferences(preferences)
            
            if result.get("status") == "success":
                print(f"✅ 默认平台已设置为: {', '.join(valid_platforms)}")
            else:
                print("❌ 设置失败")
                
        except Exception as e:
            print(f"❌ 设置平台失败: {e}")

    def _set_duration(self, duration_args: List[str]):
        """设置默认时长"""
        if not duration_args:
            print("❌ 请指定时长")
            print("用法: config duration <seconds>")
            return
        
        try:
            duration = duration_args[0]
            preferences = {}
                "default_edit_rules": {}
                    "duration": duration,
                    "style": "standard",
                    "transitions": ["fade"],
                    "effects": []
                }
            }
            
            result = self.coordinator.update_user_preferences(preferences)
            
            if result.get("status") == "success":
                print(f"✅ 默认时长已设置为: {duration}")
            else:
                print("❌ 设置失败")
                
        except Exception as e:
            print(f"❌ 设置时长失败: {e}")

    def _get_command_help(self, command: str) -> str:
        """获取特定命令的帮助信息"""
        help_texts = {}
            "analyze": """
        📖 analyze 命令帮助:
        用法: analyze <video_path> [analysis_types...]
  
        参数:
        video_path      - 视频文件路径
        analysis_types  - 分析类型 (可选)
                     可选值: basic, scene_detection, quality_assessment
  
        示例:
        analyze video.mp4
        analyze video.mp4 basic scene_detection
            """,
            "edit": """
        📖 edit 命令帮助:
        用法: edit <video_path> [output_path]
  
        参数:
        video_path   - 输入视频文件路径
        output_path  - 输出视频文件路径 (可选)
  
        示例:
        edit input.mp4
        edit input.mp4 output.mp4
            """,
            "publish": """
        📖 publish 命令帮助:
        用法: publish <video_path> [platforms...]
  
        参数:
        video_path  - 视频文件路径
        platforms   - 目标平台 (可选，默认为 douyin bilibili)
                 可选值: douyin, bilibili, youtube, xiaohongshu, toutiao
  
        示例:
        publish video.mp4
        publish video.mp4 douyin bilibili
        publish video.mp4 youtube
            """
        }
        
        return help_texts.get(command, f"❌ 没有找到命令 '{command}' 的帮助信息")


# 演示函数
    def main():
        """运行CLI界面"""
        try:
        cli = CLIInterface()
        cli.run()
        except Exception as e:
        print(f"❌ CLI启动失败: {e}")
        logger.error(f"CLI启动失败: {e}")


        if __name__ == "__main__":
        main()
