import os
import sys
import unittest

# 获取当前文件所在的目录，即 smart_editor 目录
current_dir = os.path.dirname(__file__)
# 获取 backend 目录的路径
backend_dir = os.path.join(current_dir, "..", "..")
# 将 backend 目录的绝对路径添加到 sys.path
sys.path.insert(0, os.path.abspath(backend_dir))

from agent.smart_editor.smart_editor import SmartEditor


class TestSmartEditor(unittest.TestCase):
    """
    测试SmartEditor类的功能。
    """

    def setUp(self):
        """
        在每个测试方法执行前设置。
        """
        self.editor = SmartEditor()
        self.test_video_path = "/path/to/test_video.mp4"
        self.test_music_path = "/path/to/test_music.mp3"
        self.test_text_content = "Hello, this is a test subtitle."

    def test_auto_edit_video(self):
        """
        测试自动剪辑视频功能。
        """
        material_ids = ["mat_001", "mat_002"]
        analysis_results = {"video_length": 120, "highlights": [{"start": 10, "end": 20}, {"start": 50, "end": 60}]}
        edit_rules = {"add_intro": True, "add_outro": True}
        edited_path = self.editor.auto_edit_video(material_ids, analysis_results, edit_rules)
        self.assertTrue(edited_path.startswith("/path/to/edited_video_with_effects_"))
        self.assertTrue(edited_path.endswith("_materials.mp4"))
        print("\n测试自动剪辑视频: {edited_path}")

    def test_add_background_music(self):
        """
        测试添加背景音乐功能。
        """
        video_with_music_path = self.editor.add_background_music(self.test_video_path, self.test_music_path)
        self.assertEqual(video_with_music_path, f"{self.test_video_path.replace('.mp4', '')}_with_music.mp4")
        print("\n测试添加背景音乐: {video_with_music_path}")

    def test_generate_subtitles(self):
        """
        测试生成字幕功能。
        """
        video_with_subtitles_path = self.editor.generate_subtitles(self.test_video_path, self.test_text_content)
        self.assertEqual(video_with_subtitles_path, f"{self.test_video_path.replace('.mp4', '')}_with_subtitles.mp4")
        print("\n测试生成字幕: {video_with_subtitles_path}")


if __name__ == "__main__":
    unittest.main()
