#!/usr/bin/env python3
"""
strategy_selector module
"""

import json
import logging
import os
from typing import Any, Callable, Dict, List, Optional

from ..knowledge_base.rule_store import RuleStore
from ..knowledge_base.user_profile_store import UserProfileStore
from ..learning_engine.trend_analyzer import TrendAnalyzer

"""根据当前目标、上下文、可用资源和学习到的知识选择最佳的执行策略。"""
    """
    初始化 StrategySelector。
    Args:
        rule_store: 规则存储实例。
        user_profile_store: 用户画像存储实例。
        trend_analyzer: 趋势分析器实例。
        strategies_dir: 策略配置目录，默认为当前目录下的 'config/strategies'
    """
    """加载策略配置"""
    """
    为给定的目标选择最合适的执行策略。
    Args:
        goal (Dict[str, Any]): 当前要实现的目标。
            (e.g., from GoalProcessor: {'goal_type': 'video_creation', 'parameters': {'theme': 'epic_wins', ...}})
        current_context (Dict[str, Any]): 当前的上下文信息。
            (e.g., {'user_id': 'user123', 'time_of_day': 'evening', 'platform_target': 'youtube'})
        available_resources_summary (Dict[str, Any]): 可用资源的摘要。
            (e.g., {'gpu_available': True, 'cpu_load': 'low', 'api_quota_remaining': 50})
    Returns:
        Optional[Dict[str, Any]]: 选定的策略及其配置，如果找不到合适的策略则返回None。
            示例: {
                "strategy_name": "fast_highlight_reel",
                "confidence": 0.85,
                "parameters_override": {"clip_selection_method": "action_intensity_high"},
                "reasoning": "Goal is highlight reel, user prefers fast pace, GPU available."
            }
    """
    """
    获取用户偏好
    Args:
        user_id: 用户ID
    Returns:
        用户偏好字典
    """
    """
    获取当前趋势
    Returns:
        趋势数据字典
    """
    """
    获取适用于当前目标的规则
    Args:
        goal_type: 目标类型
        goal_params: 目标参数
    Returns:
        适用规则列表
    """
    """
    默认视频创作策略
    适用于：通用视频创作，没有特定风格要求的情况
    Args:
        goal_params: 目标参数
    Returns:
        策略指导参数
    """
    """
    快速精彩集锦策略
    适用于：体育、游戏、动作场景的精彩片段剪辑
    Args:
        goal_params: 目标参数
    Returns:
        策略指导参数
    """
    """
    电影感叙事策略
    适用于：纪录片、电影风格、长篇叙事内容
    Args:
        goal_params: 目标参数
    Returns:
        策略指导参数
    """
    """
    教程解说策略
    适用于：教程、指南、操作演示
    Args:
        goal_params: 目标参数
    Returns:
        策略指导参数
    """
    """
    快速社交媒体短片策略
    适用于：短视频平台、社交媒体内容
    Args:
        goal_params: 目标参数
    Returns:
        策略指导参数
    """
    """
    Vlog风格策略
    适用于：日常生活记录、个人视频博客
    Args:
        goal_params: 目标参数
    Returns:
        策略指导参数
    """
    """
    教育内容策略
    适用于：教育视频、知识分享、科普内容
    Args:
        goal_params: 目标参数
    Returns:
        策略指导参数
    """
    """
    产品展示策略
    适用于：产品评测、开箱视频、商业展示
    Args:
        goal_params: 目标参数
    Returns:
        策略指导参数
    """
    """
    游戏精彩集锦策略
    适用于：游戏实况、精彩操作、游戏评测
    Args:
        goal_params: 目标参数
    Returns:
        策略指导参数
    """
    """
    新闻摘要策略
    适用于：新闻报道、事件总结、时事分析
    Args:
        goal_params: 目标参数
    Returns:
        策略指导参数
    """
    """
    访谈汇编策略
    适用于：人物访谈、专家观点、多人讨论
    Args:
        goal_params: 目标参数
    Returns:
        策略指导参数
    """
    """
    旅行蒙太奇策略
    适用于：旅行视频、风景展示、冒险记录
    Args:
        goal_params: 目标参数
    Returns:
        策略指导参数
    """
    """
    音乐视频策略
    适用于：音乐MV、演唱会集锦、乐队表演
    Args:
        goal_params: 目标参数
    Returns:
        策略指导参数
    """
    """
    喜剧短剧策略
    适用于：搞笑视频、喜剧短剧、幽默内容
    Args:
        goal_params: 目标参数
    Returns:
        策略指导参数
    """
    """
    纪录片风格策略
    适用于：纪录片、深度报道、历史回顾
    Args:
        goal_params: 目标参数
    Returns:
        策略指导参数
    """
"""测试策略选择器"""
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)
class StrategySelector:
def __init__(
    self,
    rule_store: Optional[RuleStore] = None,
    user_profile_store: Optional[UserProfileStore] = None,
    trend_analyzer: Optional[TrendAnalyzer] = None,
    strategies_dir: str = None,
):
    self.rule_store = rule_store
    self.user_profile_store = user_profile_store
    self.trend_analyzer = trend_analyzer
    self.strategies_dir = strategies_dir or os.path.join(os.getcwd(), "config", "strategies")
    os.makedirs(self.strategies_dir, exist_ok=True)
    self.available_strategies: Dict[str, Callable[[Dict[str, Any]], Dict[str, Any]]] = {
        "default_video_creation": self._strategy_default_video_creation,
        "fast_highlight_reel": self._strategy_fast_highlight_reel,
        "cinematic_storytelling": self._strategy_cinematic_storytelling,
        "tutorial_narration": self._strategy_tutorial_narration,
        "quick_social_clip": self._strategy_quick_social_clip,
        "vlog_style": self._strategy_vlog_style,
        "educational_content": self._strategy_educational_content,
        "product_showcase": self._strategy_product_showcase,
        "gaming_highlights": self._strategy_gaming_highlights,
        "news_summary": self._strategy_news_summary,
        "interview_compilation": self._strategy_interview_compilation,
        "travel_montage": self._strategy_travel_montage,
        "music_video": self._strategy_music_video,
        "comedy_sketch": self._strategy_comedy_sketch,
        "documentary_style": self._strategy_documentary_style,
    }
    self.strategy_configs = self._load_strategy_configs()
    logger.info("StrategySelector 初始化完成。可用策略: {list(self.available_strategies.keys())}")
def _load_strategy_configs(self) -> Dict[str, Any]:
    configs = {}
    default_configs = {
        "default_video_creation": {
            "display_name": "默认视频创作",
            "description": "通用视频创作策略",
            "suitable_for": ["general", "all_platforms"],
            "resource_requirements": {"gpu": "low", "cpu": "medium", "memory": "medium"},
            "parameters": {
                "clip_selection": {"method": "relevance_score_default"},
                "pacing": "medium",
                "music_suggestion_tags": ["general"],
            },
        },
        "fast_highlight_reel": {
            "display_name": "快速精彩集锦",
            "description": "快节奏的精彩片段剪辑",
            "suitable_for": ["sports", "gaming", "action", "short_form"],
            "resource_requirements": {"gpu": "medium", "cpu": "high", "memory": "medium"},
            "parameters": {
                "clip_selection": {
                    "method": "action_intensity_high",
                    "min_clip_duration": 1,
                    "max_clip_duration": 5,
                },
                "transitions": "fast_cuts",
                "pacing": "fast",
                "music_suggestion_tags": ["upbeat", "energetic"],
            },
        },
        "cinematic_storytelling": {
            "display_name": "电影叙事风格",
            "description": "电影感强的叙事剪辑",
            "suitable_for": ["documentary", "film", "long_form", "storytelling"],
            "resource_requirements": {"gpu": "high", "cpu": "high", "memory": "high"},
            "parameters": {
                "clip_selection": {"method": "narrative_flow", "min_clip_duration": 3, "max_clip_duration": 15},
                "transitions": ["crossfade", "smooth_zoom"],
                "pacing": "slow_to_medium",
                "color_grading_preset": "cinematic_warm",
                "music_suggestion_tags": ["orchestral", "emotional"],
            },
        },
    }
    for strategy_name in self.available_strategies.keys():
        config_file = os.path.join(self.strategies_dir, "{strategy_name}.json")
        if os.path.exists(config_file):
            try:
                with open(config_file, "r", encoding="utf-8") as f:
                    custom_config = json.load(f)
                if strategy_name in default_configs:
                    configs[strategy_name] = {**default_configs[strategy_name], **custom_config}
                else:
                    configs[strategy_name] = custom_config
            except Exception:
                logger.error("操作失败")
                if strategy_name in default_configs:
                    configs[strategy_name] = default_configs[strategy_name]
        elif strategy_name in default_configs:
            configs[strategy_name] = default_configs[strategy_name]
    return configs
def select_best_strategy(
    self, goal: Dict[str, Any], current_context: Dict[str, Any], available_resources_summary: Dict[str, Any]
) -> Optional[Dict[str, Any]]:
    logger.info(
        f"为目标 '{goal.get('goal_id')}' 选择策略。上下文: {current_context}, 资源: {available_resources_summary}"
    )
    goal_type = goal.get("goal_type")
    goal_params = goal.get("parameters", {})
    user_id = current_context.get("user_id")
    user_preferences = self._get_user_preferences(user_id)
    current_trends = self._get_current_trends()
    applicable_rules = self._get_applicable_rules(goal_type, goal_params)
    candidate_strategies = []
    if goal_type == "video_creation" or goal_type == "video_processing":
        theme = goal_params.get("theme", "")
        content_type = goal_params.get("content_type", "")
        duration_seconds = goal_params.get("duration_seconds", 0)
        goal_params.get("style", "")
        platform_target = current_context.get("platform_target", "")
        if theme in ["epic_wins", "highlights", "best_moments"] or content_type in ["sports", "gaming", "action"]:
            if user_preferences.get("editing_style") == "fast_paced" or (
                duration_seconds and duration_seconds <= 60
            ):
                candidate_strategies.append(
                    {
                        "name": "fast_highlight_reel",
                        "score": 0.85,
                        "reason": "主题是精彩集锦，用户偏好快节奏或视频较短",
                    }
                )
            else:
                candidate_strategies.append(
                    {
                        "name": "cinematic_storytelling",
                        "score": 0.75,
                        "reason": "主题是精彩集锦，但用户偏好电影感叙事",
                    }
                )
        elif (
            theme in ["tutorial", "how_to", "guide"]
            or content_type in ["educational", "instructional"]
            or goal_params.get("task_type") == "create_tutorial"
        ):
            candidate_strategies.append(
                {"name": "tutorial_narration", "score": 0.9, "reason": "目标是创建教程视频"}
            )
            candidate_strategies.append(
                {"name": "educational_content", "score": 0.85, "reason": "目标是创建教育内容"}
            )
        elif theme in ["vlog", "daily_life", "personal"] or content_type in ["lifestyle", "personal"]:
            candidate_strategies.append({"name": "vlog_style", "score": 0.9, "reason": "目标是创建Vlog风格视频"})
        elif theme in ["product", "review", "unboxing"] or content_type in ["commercial", "marketing"]:
            candidate_strategies.append(
                {"name": "product_showcase", "score": 0.9, "reason": "目标是创建产品展示视频"}
            )
        elif theme in ["travel", "journey", "adventure"] or content_type in ["travel", "outdoor"]:
            candidate_strategies.append({"name": "travel_montage", "score": 0.9, "reason": "目标是创建旅行视频"})
        elif theme in ["music", "concert", "mv"] or content_type in ["music", "performance"]:
            candidate_strategies.append({"name": "music_video", "score": 0.9, "reason": "目标是创建音乐视频"})
        elif theme in ["comedy", "funny", "humor"] or content_type in ["comedy", "entertainment"]:
            candidate_strategies.append({"name": "comedy_sketch", "score": 0.9, "reason": "目标是创建搞笑视频"})
        elif theme in ["documentary", "investigation", "report"] or content_type in ["documentary", "news"]:
            candidate_strategies.append(
                {"name": "documentary_style", "score": 0.9, "reason": "目标是创建纪录片风格视频"}
            )
        if platform_target in ["tiktok", "douyin", "kuaishou", "instagram_reels"] or (
            duration_seconds and duration_seconds <= 90
        ):
            candidate_strategies.append(
                {
                    "name": "quick_social_clip",
                    "score": 0.8,
                    "reason": f"目标平台是{platform_target}或视频较短，适合短视频",
                }
            )
        if not candidate_strategies:
            candidate_strategies.append(
                {
                    "name": "default_video_creation",
                    "score": 0.6,
                    "reason": "没有找到更合适的策略，使用默认视频创作策略",
                }
            )
        for strategy in candidate_strategies:
            if user_preferences.get("preferred_style") and strategy["name"].startswith(
                user_preferences["preferred_style"]
            ):
                strategy["score"] += 0.1
                strategy["reason"] += f"，用户偏好{user_preferences['preferred_style']}风格"
            if user_preferences.get("preferred_pacing"):
                strategy_config = self.strategy_configs.get(strategy["name"], {})
                if strategy_config.get("parameters", {}).get("pacing") == user_preferences["preferred_pacing"]:
                    strategy["score"] += 0.1
                    strategy["reason"] += f"，用户偏好{user_preferences['preferred_pacing']}节奏"
        for strategy in candidate_strategies:
            if strategy["name"] in current_trends.get("popular_editing_styles", []):
                strategy["score"] += 0.05
                strategy["reason"] += "，符合当前流行的编辑风格"
            if platform_target in current_trends.get("trending_platforms", []):
                strategy["score"] += 0.05
                strategy["reason"] += "，{platform_target}平台当前很流行"
        for strategy in candidate_strategies:
            strategy_config = self.strategy_configs.get(strategy["name"], {})
            resource_requirements = strategy_config.get("resource_requirements", {})
            if resource_requirements.get("gpu") == "high" and not available_resources_summary.get(
                "gpu_available", False
            ):
                strategy["score"] -= 0.2
                strategy["reason"] += "，但GPU资源不足，降低优先级"
            if resource_requirements.get("cpu") == "high" and available_resources_summary.get("cpu_load") == "high":
                strategy["score"] -= 0.1
                strategy["reason"] += "，但CPU负载较高，降低优先级"
        for strategy in candidate_strategies:
            for rule in applicable_rules:
                if rule["condition"] == "always_prefer" and strategy["name"] == rule["strategy"]:
                    strategy["score"] += 0.2
                    strategy["reason"] += f"，根据规则'{rule['name']}'提高优先级"
                elif rule["condition"] == "never_use" and strategy["name"] == rule["strategy"]:
                    strategy["score"] = 0
                    strategy["reason"] += f"，根据规则'{rule['name']}'禁用此策略"
    elif goal_type == "system_maintenance":
        return {
            "strategy_name": "standard_maintenance_procedure",
            "confidence": 1.0,
            "parameters_override": {},
            "reasoning": "系统维护目标，使用标准维护流程",
        }
    candidate_strategies = [s for s in candidate_strategies if s["score"] > 0]
    if not candidate_strategies:
        logger.warning(f"没有找到目标 '{goal.get('goal_id')}' 的候选策略")
        return None
    best_strategy_info = max(candidate_strategies, key=lambda s: s["score"])
    strategy_func = self.available_strategies.get(best_strategy_info["name"])
    parameters_override = {}
    if strategy_func:
        strategy_guidance = strategy_func(goal_params)
        parameters_override = strategy_guidance.get("task_plan_guidance", {})
    selected_strategy_config = {
        "strategy_name": best_strategy_info["name"],
        "confidence": best_strategy_info["score"],
        "parameters_override": parameters_override,
        "reasoning": best_strategy_info["reason"],
    }
    logger.info(
        f"为目标 '{goal.get('goal_id')}' 选择的策略: {selected_strategy_config['strategy_name']} "
        f"(置信度: {selected_strategy_config['confidence']}) 原因: {selected_strategy_config['reasoning']}"
    )
    return selected_strategy_config
def _get_user_preferences(self, user_id: Optional[str]) -> Dict[str, Any]:
    if not user_id or not self.user_profile_store:
        return {
            "editing_style": "balanced",
            "preferred_pacing": "medium",
            "preferred_style": None,
            "preferred_platforms": [],
            "content_preferences": [],
        }
    try:
        profile = self.user_profile_store.get_user_profile(user_id)
        if profile:
            return profile.get("preferences", {})
        else:
            logger.warning(f"未找到用户 {user_id} 的画像")
            return {
                "editing_style": "balanced",
                "preferred_pacing": "medium",
                "preferred_style": None,
                "preferred_platforms": [],
                "content_preferences": [],
            }
    except Exception as e:
        logger.error(f"获取用户 {user_id} 偏好失败: {e}")
        return {
            "editing_style": "balanced",
            "preferred_pacing": "medium",
            "preferred_style": None,
            "preferred_platforms": [],
            "content_preferences": [],
        }
def _get_current_trends(self) -> Dict[str, Any]:
    if not self.trend_analyzer:
        return {
            "popular_editing_styles": ["fast_highlight_reel", "quick_social_clip"],
            "trending_platforms": ["douyin", "kuaishou", "bilibili"],
            "trending_content_types": ["gaming", "comedy", "lifestyle"],
            "trending_effects": ["glitch", "neon", "vintage"],
            "trending_music_genres": ["electronic", "pop", "hip_hop"],
        }
    try:
        trends = self.trend_analyzer.identify_emerging_editing_styles()
        platform_trends = self.trend_analyzer.analyze_platform_specific_trends()
        seasonal_trends = self.trend_analyzer.analyze_seasonal_trends()
        return {
            "popular_editing_styles": trends.get("emerging_styles", []),
            "trending_platforms": platform_trends.get("trending_platforms", []),
            "trending_content_types": platform_trends.get("trending_content_types", []),
            "trending_effects": trends.get("popular_effects", []),
            "trending_music_genres": seasonal_trends.get("trending_music_genres", []),
            "seasonal_themes": seasonal_trends.get("seasonal_themes", []),
        }
    except Exception as e:
        logger.error(f"获取趋势数据失败: {e}")
        return {
            "popular_editing_styles": ["fast_highlight_reel", "quick_social_clip"],
            "trending_platforms": ["douyin", "kuaishou", "bilibili"],
            "trending_content_types": ["gaming", "comedy", "lifestyle"],
            "trending_effects": ["glitch", "neon", "vintage"],
            "trending_music_genres": ["electronic", "pop", "hip_hop"],
        }
def _get_applicable_rules(self, goal_type: str, goal_params: Dict[str, Any]) -> List[Dict[str, Any]]:
    if not self.rule_store:
        return []
    try:
        rule_context = {"goal_type": goal_type, "goal_params": goal_params}
        rules = self.rule_store.get_rules(rule_context)
        strategy_rules = [rule for rule in rules if rule.get("category") == "strategy_selection"]
        return strategy_rules
    except Exception:
        logger.error("操作失败")
        return []
def _strategy_default_video_creation(self, goal_params: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "task_plan_guidance": {
            "clip_selection": {"method": "relevance_score_default"},
            "pacing": "medium",
            "transitions": ["fade", "cut"],
            "effects": [],
            "color_grading": "standard",
            "audio_processing": {"background_music_level": "medium"},
            "text_overlays": {"type": "minimal", "font": "standard"},
            "music_suggestion_tags": [goal_params.get("theme", "general")],
        }
    }
def _strategy_fast_highlight_reel(self, goal_params: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "task_plan_guidance": {
            "clip_selection": {
                "method": "action_intensity_high",
                "min_clip_duration": 1,
                "max_clip_duration": 5,
                "prioritize_motion": True,
            },
            "transitions": ["fast_cuts", "whip_pan", "zoom_blur"],
            "pacing": "fast",
            "effects": ["speed_ramp", "freeze_frame", "dynamic_zoom"],
            "color_grading": "vibrant",
            "audio_processing": {"background_music_level": "high", "sound_effects": True, "beat_sync": True},
            "text_overlays": {"type": "dynamic", "font": "bold_impact", "highlight_moments": True},
            "music_suggestion_tags": ["upbeat", "energetic", "intense", goal_params.get("theme")],
        }
    }
def _strategy_cinematic_storytelling(self, goal_params: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "task_plan_guidance": {
            "clip_selection": {
                "method": "narrative_flow",
                "min_clip_duration": 3,
                "max_clip_duration": 15,
                "prioritize_composition": True,
            },
            "transitions": ["crossfade", "smooth_zoom", "dissolve", "match_cut"],
            "pacing": "slow_to_medium",
            "effects": ["lens_flare", "film_grain", "letterbox"],
            "color_grading_preset": "cinematic_warm",
            "audio_processing": {
                "background_music_level": "medium",
                "ambient_sounds": True,
                "voice_over_clarity_boost": True,
            },
            "text_overlays": {"type": "minimal_elegant", "font": "serif_cinematic", "fade_in_out": True},
            "music_suggestion_tags": ["orchestral", "emotional", "cinematic", goal_params.get("theme")],
        }
    }
def _strategy_tutorial_narration(self, goal_params: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "task_plan_guidance": {
            "clip_selection": {
                "method": "step_by_step_visuals",
                "highlight_ui_elements": True,
                "prioritize_clarity": True,
            },
            "transitions": ["simple_cut", "fade"],
            "pacing": "clear_and_steady",
            "effects": ["zoom_to_detail", "highlight_circle", "arrow_pointer"],
            "color_grading": "neutral_clear",
            "audio_processing": {
                "voice_over_clarity_boost": True,
                "background_music_level": "low",
                "noise_reduction": True,
            },
            "text_overlays": {
                "type": "instructional",
                "font": "clear_sans_seri",
                "step_numbers": True,
                "key_points_highlight": True,
            },
            "music_suggestion_tags": ["ambient", "background", "soft", goal_params.get("topic")],
        }
    }
def _strategy_quick_social_clip(self, goal_params: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "task_plan_guidance": {
            "clip_selection": {
                "method": "most_engaging_short_segments",
                "max_duration_total": goal_params.get("duration_seconds", 60),
                "prioritize_engagement": True,
            },
            "aspect_ratio": goal_params.get("platform_target_aspect_ratio", "9:16"),
            "transitions": ["quick_cut", "swipe", "glitch"],
            "pacing": "very_fast",
            "effects": ["text_animations", "stickers", "emojis", "filters"],
            "color_grading": "social_vibrant",
            "audio_processing": {
                "background_music_level": "high",
                "voice_clarity": True,
                "trending_sound_effects": True,
            },
            "text_overlays": {
                "type": "catchy_titles_subtitles",
                "font": "bold_sans_seri",
                "animated": True,
                "emoji_integration": True,
            },
            "music_suggestion_tags": ["trending_audio", "viral", "upbeat", goal_params.get("theme")],
            "call_to_action_option": True,
        }
    }
def _strategy_vlog_style(self, goal_params: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "task_plan_guidance": {
            "clip_selection": {"method": "chronological_highlights", "prioritize_personal_moments": True},
            "transitions": ["jump_cut", "whip_pan", "zoom_transition"],
            "pacing": "natural_varied",
            "effects": ["handheld_camera", "light_leaks", "day_markers"],
            "color_grading": "lifestyle_warm",
            "audio_processing": {"background_music_level": "medium", "ambient_sounds": True, "voice_clarity": True},
            "text_overlays": {"type": "location_time_stamps", "font": "casual_handwritten", "personal_notes": True},
            "music_suggestion_tags": ["acoustic", "upbeat", "indie", goal_params.get("mood", "cheerful")],
        }
    }
def _strategy_educational_content(self, goal_params: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "task_plan_guidance": {
            "clip_selection": {"method": "concept_explanation", "prioritize_visual_examples": True},
            "transitions": ["smooth_fade", "simple_cut"],
            "pacing": "moderate_thoughtful",
            "effects": ["animated_diagrams", "split_screen", "visual_annotations"],
            "color_grading": "clean_professional",
            "audio_processing": {
                "voice_over_clarity_boost": True,
                "background_music_level": "low",
                "noise_reduction": True,
            },
            "text_overlays": {
                "type": "key_concepts",
                "font": "clear_educational",
                "bullet_points": True,
                "definitions": True,
            },
            "music_suggestion_tags": ["thoughtful", "inspiring", "calm", goal_params.get("subject")],
        }
    }
def _strategy_product_showcase(self, goal_params: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "task_plan_guidance": {
            "clip_selection": {"method": "product_features_highlight", "prioritize_detail_shots": True},
            "transitions": ["smooth_slide", "fade", "product_rotation"],
            "pacing": "clear_informative",
            "effects": ["depth_of_field", "product_callouts", "comparison_split"],
            "color_grading": "product_natural",
            "audio_processing": {
                "voice_over_clarity_boost": True,
                "background_music_level": "low",
                "product_sounds": True,
            },
            "text_overlays": {"type": "feature_specs", "font": "modern_clean", "price_tags": True, "ratings": True},
            "music_suggestion_tags": ["professional", "corporate", "modern", goal_params.get("product_category")],
        }
    }
def _strategy_gaming_highlights(self, goal_params: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "task_plan_guidance": {
            "clip_selection": {"method": "gameplay_highlights", "prioritize_action_moments": True},
            "transitions": ["quick_cut", "glitch", "game_ui_transition"],
            "pacing": "dynamic_fast",
            "effects": ["hitmarkers", "score_counters", "screen_shake"],
            "color_grading": "gaming_vibrant",
            "audio_processing": {"game_audio": True, "reaction_audio": True, "background_music_level": "medium"},
            "text_overlays": {
                "type": "game_stats",
                "font": "gaming_bold",
                "player_callouts": True,
                "achievement_popups": True,
            },
            "music_suggestion_tags": ["electronic", "intense", "gaming", goal_params.get("game_title")],
        }
    }
def _strategy_news_summary(self, goal_params: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "task_plan_guidance": {
            "clip_selection": {"method": "chronological_events", "prioritize_key_moments": True},
            "transitions": ["clean_cut", "simple_wipe", "map_transition"],
            "pacing": "informative_direct",
            "effects": ["lower_thirds", "picture_in_picture", "source_attribution"],
            "color_grading": "news_neutral",
            "audio_processing": {
                "voice_over_clarity_boost": True,
                "background_music_level": "low",
                "ambient_audio": True,
            },
            "text_overlays": {
                "type": "headlines_captions",
                "font": "news_seri",
                "date_stamps": True,
                "source_citations": True,
            },
            "music_suggestion_tags": ["news", "serious", "neutral", goal_params.get("topic")],
        }
    }
def _strategy_interview_compilation(self, goal_params: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "task_plan_guidance": {
            "clip_selection": {"method": "key_quotes_insights", "prioritize_emotional_moments": True},
            "transitions": ["crossfade", "simple_cut", "b_roll_insert"],
            "pacing": "conversational",
            "effects": ["depth_of_field", "subtle_zoom", "reaction_shots"],
            "color_grading": "interview_professional",
            "audio_processing": {
                "voice_clarity": True,
                "background_noise_reduction": True,
                "background_music_level": "very_low",
            },
            "text_overlays": {
                "type": "name_title_quotes",
                "font": "professional_clean",
                "pull_quotes": True,
                "speaker_identification": True,
            },
            "music_suggestion_tags": ["subtle", "thoughtful", "background", goal_params.get("interview_topic")],
        }
    }
def _strategy_travel_montage(self, goal_params: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "task_plan_guidance": {
            "clip_selection": {"method": "scenic_highlights", "prioritize_beautiful_shots": True},
            "transitions": ["smooth_pan", "timelapse", "drone_reveal"],
            "pacing": "flowing_dynamic",
            "effects": ["lens_flare", "parallax", "speed_ramp"],
            "color_grading": "travel_vibrant",
            "audio_processing": {
                "ambient_sounds": True,
                "background_music_level": "high",
                "voice_over_option": True,
            },
            "text_overlays": {
                "type": "location_names",
                "font": "elegant_travel",
                "map_animations": True,
                "date_stamps": True,
            },
            "music_suggestion_tags": ["inspiring", "adventure", "cinematic", goal_params.get("destination")],
        }
    }
def _strategy_music_video(self, goal_params: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "task_plan_guidance": {
            "clip_selection": {"method": "beat_matched_visuals", "prioritize_performance_moments": True},
            "transitions": ["beat_sync_cuts", "flash_transition", "color_wash"],
            "pacing": "music_driven",
            "effects": ["light_leaks", "glitch", "color_pulse", "particles"],
            "color_grading": "music_stylized",
            "audio_processing": {"music_quality_boost": True, "beat_detection": True, "vocal_clarity": True},
            "text_overlays": {
                "type": "minimal_artistic",
                "font": "stylized_music",
                "lyrics_option": True,
                "artist_name": True,
            },
            "music_suggestion_tags": [goal_params.get("music_genre", "pop"), "original", "performance"],
        }
    }
def _strategy_comedy_sketch(self, goal_params: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "task_plan_guidance": {
            "clip_selection": {"method": "comedic_timing", "prioritize_reaction_shots": True},
            "transitions": ["whip_pan", "zoom_punch", "silly_wipe"],
            "pacing": "comedic_rhythm",
            "effects": ["sound_effects", "zoom_emphasis", "cartoon_elements"],
            "color_grading": "bright_cheerful",
            "audio_processing": {
                "laugh_track_option": True,
                "sound_effects": True,
                "background_music_level": "medium",
            },
            "text_overlays": {
                "type": "punchline_emphasis",
                "font": "fun_comic",
                "thought_bubbles": True,
                "onomatopoeia": True,
            },
            "music_suggestion_tags": ["funny", "quirky", "upbeat", goal_params.get("comedy_style", "general")],
        }
    }
def _strategy_documentary_style(self, goal_params: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "task_plan_guidance": {
            "clip_selection": {"method": "narrative_documentary", "prioritize_emotional_impact": True},
            "transitions": ["slow_fade", "historical_photo", "archival_footage"],
            "pacing": "thoughtful_deliberate",
            "effects": ["film_grain", "ken_burns", "vignette"],
            "color_grading": "documentary_mood",
            "audio_processing": {
                "voice_over_clarity_boost": True,
                "ambient_sounds": True,
                "background_music_level": "medium",
            },
            "text_overlays": {
                "type": "contextual_information",
                "font": "documentary_seri",
                "historical_dates": True,
                "expert_quotes": True,
            },
            "music_suggestion_tags": ["emotional", "dramatic", "historical", goal_params.get("subject")],
        }
    }
if __name__ == "__main__":
logging.basicConfig(level=logging.INFO)
class MockRuleStore:
    def get_rules(self, context):
        return [
            {
                "id": "rule1",
                "name": "优先使用快速精彩集锦",
                "category": "strategy_selection",
                "condition": "always_prefer",
                "strategy": "fast_highlight_reel",
            },
            {
                "id": "rule2",
                "name": "禁用电影感叙事",
                "category": "strategy_selection",
                "condition": "never_use",
                "strategy": "cinematic_storytelling",
            },
        ]
class MockUserProfileStore:
    def get_user_profile(self, user_id):
        profiles = {
            "user_fast_editor": {
                "preferences": {
                    "editing_style": "fast_paced",
                    "preferred_pacing": "fast",
                    "preferred_style": "fast_highlight_reel",
                    "preferred_platforms": ["douyin", "kuaishou"],
                    "content_preferences": ["gaming", "sports"],
                }
            },
            "user_cinematic": {
                "preferences": {
                    "editing_style": "cinematic",
                    "preferred_pacing": "slow",
                    "preferred_style": "cinematic_storytelling",
                    "preferred_platforms": ["bilibili", "youtube"],
                    "content_preferences": ["film", "documentary"],
                }
            },
            "user_educational": {
                "preferences": {
                    "editing_style": "clear",
                    "preferred_pacing": "moderate",
                    "preferred_style": "educational_content",
                    "preferred_platforms": ["bilibili", "youtube"],
                    "content_preferences": ["education", "science"],
                }
            },
        }
        return profiles.get(user_id, {"preferences": {"editing_style": "balanced"}})
class MockTrendAnalyzer:
    def identify_emerging_editing_styles(self):
        return {
            "emerging_styles": ["quick_social_clip", "vlog_style", "gaming_highlights"],
            "popular_effects": ["glitch", "neon", "text_animations"],
            "trending_transitions": ["quick_cut", "whip_pan", "glitch"],
        }
    def analyze_platform_specific_trends(self):
        return {
            "trending_platforms": ["douyin", "kuaishou", "bilibili"],
            "trending_content_types": ["gaming", "comedy", "lifestyle"],
            "platform_specific_trends": {
                "douyin": ["dance", "comedy", "daily_life"],
                "bilibili": ["anime", "gaming", "technology"],
                "youtube": ["vlogs", "tutorials", "reviews"],
            },
        }
    def analyze_seasonal_trends(self):
        return {
            "seasonal_themes": ["summer_travel", "back_to_school", "outdoor_activities"],
            "trending_music_genres": ["pop", "electronic", "indie"],
            "upcoming_events": ["summer_festival", "sports_championship"],
        }
selector = StrategySelector(
    rule_store=MockRuleStore(), user_profile_store=MockUserProfileStore(), trend_analyzer=MockTrendAnalyzer()
)
print("\n===== 测试策略选择器 =====\n")
sample_goal_highlights = {
    "goal_id": "gh001",
    "goal_type": "video_creation",
    "parameters": {"theme": "epic_wins", "content_type": "sports", "duration_seconds": 45},
}
sample_context_user1 = {"user_id": "user_fast_editor", "platform_target": "douyin"}
sample_resources_gpu = {"gpu_available": True, "cpu_load": "low"}
print("测试场景1：精彩集锦视频")
strategy1 = selector.select_best_strategy(sample_goal_highlights, sample_context_user1, sample_resources_gpu)
if strategy1:
    print(f"选择的策略: {strategy1['strategy_name']}")
    print(f"置信度: {strategy1['confidence']}")
    print(f"原因: {strategy1['reasoning']}")
    print(f"参数覆盖: {strategy1['parameters_override']}")
print("\n" + "-" * 50 + "\n")
sample_goal_tutorial = {
    "goal_id": "gt002",
    "goal_type": "video_creation",
    "parameters": {"task_type": "create_tutorial", "topic": "python_basics", "content_type": "educational"},
}
sample_context_educational = {"user_id": "user_educational", "platform_target": "bilibili"}
sample_resources_no_gpu = {"gpu_available": False, "cpu_load": "medium"}
print("测试场景2：教程视频")
strategy2 = selector.select_best_strategy(sample_goal_tutorial, sample_context_educational, sample_resources_no_gpu)
if strategy2:
    print(f"选择的策略: {strategy2['strategy_name']}")
    print(f"置信度: {strategy2['confidence']}")
    print(f"原因: {strategy2['reasoning']}")
    print(f"参数覆盖: {strategy2['parameters_override']}")
print("\n" + "-" * 50 + "\n")
sample_goal_social = {
    "goal_id": "gs003",
    "goal_type": "video_creation",
    "parameters": {"duration_seconds": 20, "theme": "funny_cat_clip", "content_type": "comedy"},
}
sample_context_tiktok = {"user_id": "user_social_savvy", "platform_target": "tiktok"}
print("测试场景3：短视频")
strategy3 = selector.select_best_strategy(sample_goal_social, sample_context_tiktok, sample_resources_gpu)
if strategy3:
    print(f"选择的策略: {strategy3['strategy_name']}")
    print(f"置信度: {strategy3['confidence']}")
    print(f"原因: {strategy3['reasoning']}")
    print(f"参数覆盖: {strategy3['parameters_override']}")
print("\n" + "-" * 50 + "\n")
sample_goal_cinematic = {
    "goal_id": "gc004",
    "goal_type": "video_creation",
    "parameters": {"theme": "documentary", "content_type": "film", "duration_seconds": 300},
}
sample_context_cinematic = {"user_id": "user_cinematic", "platform_target": "youtube"}
print("测试场景4：电影风格视频（测试规则禁用）")
strategy4 = selector.select_best_strategy(sample_goal_cinematic, sample_context_cinematic, sample_resources_gpu)
if strategy4:
    print(f"选择的策略: {strategy4['strategy_name']}")
    print(f"置信度: {strategy4['confidence']}")
    print(f"原因: {strategy4['reasoning']}")
    print(f"参数覆盖: {strategy4['parameters_override']}")
else:
    print("没有找到合适的策略")
print("\n" + "-" * 50 + "\n")
sample_goal_vlog = {
    "goal_id": "gv005",
    "goal_type": "video_creation",
    "parameters": {"theme": "daily_life", "content_type": "lifestyle", "duration_seconds": 180},
}
sample_context_vlog = {"user_id": "user_fast_editor", "platform_target": "bilibili"}
print("测试场景5：Vlog风格视频")
strategy5 = selector.select_best_strategy(sample_goal_vlog, sample_context_vlog, sample_resources_gpu)
if strategy5:
    print(f"选择的策略: {strategy5['strategy_name']}")
    print(f"置信度: {strategy5['confidence']}")
    print(f"原因: {strategy5['reasoning']}")
    print(f"参数覆盖: {strategy5['parameters_override']}")
print("\n===== 测试完成 =====\n")