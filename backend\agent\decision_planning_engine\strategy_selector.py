#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
策略选择器
根据当前目标、上下文、可用资源和学习到的知识选择最佳的执行策略
"""

import json
import logging
import os
from typing import Any, Callable, Dict, List, Optional

logger = logging.getLogger(__name__)


class StrategySelector:
    """
    策略选择器类
    
    功能：
    1. 策略评估
    2. 策略选择
    3. 策略配置管理
    4. 用户偏好分析
    """

    def __init__(
        self,
        rule_store=None,
        user_profile_store=None,
        trend_analyzer=None,
        strategies_dir: Optional[str] = None
    ):
        """
        初始化 StrategySelector
        
        Args:
            rule_store: 规则存储实例
            user_profile_store: 用户画像存储实例
            trend_analyzer: 趋势分析器实例
            strategies_dir: 策略配置目录
        """
        self.rule_store = rule_store
        self.user_profile_store = user_profile_store
        self.trend_analyzer = trend_analyzer
        self.strategies_dir = strategies_dir or os.path.join(os.getcwd(), "config", "strategies")
        
        # 确保策略目录存在
        os.makedirs(self.strategies_dir, exist_ok=True)
        
        # 可用策略映射
        self.available_strategies = {
            "default_video_creation": self._strategy_default_video_creation,
            "fast_highlight_reel": self._strategy_fast_highlight_reel,
            "cinematic_storytelling": self._strategy_cinematic_storytelling,
            "social_media_optimized": self._strategy_social_media_optimized,
            "educational_content": self._strategy_educational_content,
            "marketing_focused": self._strategy_marketing_focused,
        }
        
        # 加载策略配置
        self.strategy_configs = self._load_strategy_configs()
        
        logger.info(f"StrategySelector 初始化完成。可用策略: {list(self.available_strategies.keys())}")

    def _load_strategy_configs(self) -> Dict[str, Any]:
        """加载策略配置"""
        configs = {}
        
        # 默认策略配置
        default_configs = {
            "default_video_creation": {
                "name": "默认视频创建",
                "description": "标准的视频创建流程",
                "parameters": {
                    "quality": "high",
                    "pacing": "medium",
                    "effects": ["basic_transitions"],
                    "duration_range": [30, 300]
                },
                "resource_requirements": {
                    "cpu_intensive": False,
                    "gpu_required": False,
                    "memory_usage": "medium"
                }
            },
            "fast_highlight_reel": {
                "name": "快速精彩集锦",
                "description": "快节奏的精彩片段集锦",
                "parameters": {
                    "quality": "high",
                    "pacing": "fast",
                    "effects": ["quick_cuts", "zoom", "speed_ramp"],
                    "duration_range": [15, 60]
                },
                "resource_requirements": {
                    "cpu_intensive": True,
                    "gpu_required": True,
                    "memory_usage": "high"
                }
            },
            "cinematic_storytelling": {
                "name": "电影级叙事",
                "description": "电影级的叙事风格视频",
                "parameters": {
                    "quality": "ultra_high",
                    "pacing": "slow",
                    "effects": ["color_grading", "smooth_transitions", "cinematic_filters"],
                    "duration_range": [120, 600]
                },
                "resource_requirements": {
                    "cpu_intensive": True,
                    "gpu_required": True,
                    "memory_usage": "very_high"
                }
            },
            "social_media_optimized": {
                "name": "社交媒体优化",
                "description": "针对社交媒体平台优化的视频",
                "parameters": {
                    "quality": "high",
                    "pacing": "fast",
                    "effects": ["trendy_filters", "text_overlays", "music_sync"],
                    "duration_range": [15, 90]
                },
                "resource_requirements": {
                    "cpu_intensive": False,
                    "gpu_required": False,
                    "memory_usage": "medium"
                }
            }
        }
        
        # 尝试从文件加载配置
        for strategy_name, default_config in default_configs.items():
            config_file = os.path.join(self.strategies_dir, f"{strategy_name}.json")
            try:
                if os.path.exists(config_file):
                    with open(config_file, "r", encoding="utf-8") as f:
                        configs[strategy_name] = json.load(f)
                else:
                    configs[strategy_name] = default_config
                    # 保存默认配置到文件
                    with open(config_file, "w", encoding="utf-8") as f:
                        json.dump(default_config, f, ensure_ascii=False, indent=2)
            except Exception as e:
                logger.warning(f"加载策略配置失败 {strategy_name}: {e}")
                configs[strategy_name] = default_config
        
        return configs

    def select_strategy(
        self,
        goal: Dict[str, Any],
        current_context: Dict[str, Any],
        available_resources: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        选择最佳策略
        
        Args:
            goal: 目标信息
            current_context: 当前上下文
            available_resources: 可用资源
            
        Returns:
            选择的策略信息
        """
        logger.info(f"开始策略选择，目标: {goal.get('goal_type')}")
        
        goal_type = goal.get("goal_type")
        goal_params = goal.get("parameters", {})
        user_id = current_context.get("user_id")
        
        # 获取用户偏好
        user_preferences = self._get_user_preferences(user_id)
        
        # 获取当前趋势
        current_trends = self._get_current_trends()
        
        # 获取适用规则
        applicable_rules = self._get_applicable_rules(goal_type, goal_params)
        
        # 生成候选策略
        candidate_strategies = []
        
        if goal_type in ["video_creation", "video_processing"]:
            theme = goal_params.get("theme", "")
            duration = goal_params.get("duration", 60)
            target_platforms = goal_params.get("target_platforms", [])
            
            # 根据不同条件推荐策略
            if "highlight" in theme.lower() or "精彩" in theme:
                candidate_strategies.append({
                    "name": "fast_highlight_reel",
                    "score": 0.8,
                    "reason": "主题匹配精彩集锦"
                })
            
            if duration <= 60:
                candidate_strategies.append({
                    "name": "social_media_optimized",
                    "score": 0.7,
                    "reason": "短视频适合社交媒体"
                })
            
            if duration >= 120:
                candidate_strategies.append({
                    "name": "cinematic_storytelling",
                    "score": 0.6,
                    "reason": "长视频适合叙事风格"
                })
            
            # 默认策略
            candidate_strategies.append({
                "name": "default_video_creation",
                "score": 0.5,
                "reason": "默认策略"
            })
        
        # 如果没有候选策略，使用默认策略
        if not candidate_strategies:
            candidate_strategies.append({
                "name": "default_video_creation",
                "score": 0.5,
                "reason": "默认策略"
            })
        
        # 根据用户偏好调整分数
        for strategy in candidate_strategies:
            strategy_config = self.strategy_configs.get(strategy["name"], {})
            if strategy_config.get("parameters", {}).get("pacing") == user_preferences.get("preferred_pacing"):
                strategy["score"] += 0.1
                strategy["reason"] += f"，用户偏好{user_preferences.get('preferred_pacing')}节奏"
        
        # 根据当前趋势调整分数
        for strategy in candidate_strategies:
            if strategy["name"] in current_trends.get("popular_editing_styles", []):
                strategy["score"] += 0.05
                strategy["reason"] += "，符合当前趋势"
        
        # 根据资源可用性调整分数
        for strategy in candidate_strategies:
            strategy_config = self.strategy_configs.get(strategy["name"], {})
            resource_req = strategy_config.get("resource_requirements", {})
            
            if resource_req.get("gpu_required") and available_resources.get("gpu_count", 0) == 0:
                strategy["score"] -= 0.2
                strategy["reason"] += "，但GPU资源不足"
            
            if resource_req.get("memory_usage") == "very_high" and available_resources.get("ram_gb", 0) < 8:
                strategy["score"] -= 0.15
                strategy["reason"] += "，但内存资源不足"
        
        # 选择得分最高的策略
        best_strategy = max(candidate_strategies, key=lambda x: x["score"])
        
        # 获取策略配置
        strategy_config = self.strategy_configs.get(best_strategy["name"], {})
        
        # 生成策略指导参数
        guidance_parameters = self._generate_guidance_parameters(
            best_strategy["name"], goal, current_context, available_resources
        )
        
        result = {
            "strategy_name": best_strategy["name"],
            "strategy_config": strategy_config,
            "confidence": min(best_strategy["score"], 1.0),
            "reasoning": best_strategy["reason"],
            "guidance_parameters": guidance_parameters,
            "alternative_strategies": [s for s in candidate_strategies if s["name"] != best_strategy["name"]]
        }
        
        logger.info(f"选择策略: {best_strategy['name']}, 置信度: {result['confidence']:.2f}")
        return result

    def _get_user_preferences(self, user_id: Optional[str]) -> Dict[str, Any]:
        """获取用户偏好"""
        if not user_id or not self.user_profile_store:
            return {
                "preferred_pacing": "medium",
                "preferred_style": "standard",
                "preferred_duration": 60,
                "preferred_effects": ["basic_transitions"]
            }
        
        # 这里应该从用户画像存储中获取实际偏好
        return {
            "preferred_pacing": "medium",
            "preferred_style": "standard",
            "preferred_duration": 60,
            "preferred_effects": ["basic_transitions"]
        }

    def _get_current_trends(self) -> Dict[str, Any]:
        """获取当前趋势"""
        if not self.trend_analyzer:
            return {
                "popular_editing_styles": ["fast_highlight_reel", "social_media_optimized"],
                "trending_effects": ["quick_cuts", "zoom", "text_overlays"],
                "popular_durations": [30, 60, 90]
            }
        
        # 这里应该从趋势分析器获取实际趋势
        return {
            "popular_editing_styles": ["fast_highlight_reel", "social_media_optimized"],
            "trending_effects": ["quick_cuts", "zoom", "text_overlays"],
            "popular_durations": [30, 60, 90]
        }

    def _get_applicable_rules(self, goal_type: str, goal_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取适用规则"""
        if not self.rule_store:
            return []
        
        # 这里应该从规则存储中获取适用规则
        return []

    def _generate_guidance_parameters(
        self,
        strategy_name: str,
        goal: Dict[str, Any],
        current_context: Dict[str, Any],
        available_resources: Dict[str, Any]
    ) -> Dict[str, Any]:
        """生成策略指导参数"""
        strategy_config = self.strategy_configs.get(strategy_name, {})
        base_params = strategy_config.get("parameters", {})
        
        # 根据具体情况调整参数
        guidance = base_params.copy()
        
        # 根据目标参数调整
        goal_params = goal.get("parameters", {})
        if "duration" in goal_params:
            guidance["target_duration"] = goal_params["duration"]
        
        if "quality" in goal_params:
            guidance["quality"] = goal_params["quality"]
        
        # 根据可用资源调整
        if available_resources.get("gpu_count", 0) == 0:
            # 没有GPU时降低质量要求
            if guidance.get("quality") == "ultra_high":
                guidance["quality"] = "high"
            # 移除需要GPU的效果
            guidance["effects"] = [e for e in guidance.get("effects", []) if e not in ["cinematic_filters", "advanced_color_grading"]]
        
        return guidance

    # 策略实现方法
    def _strategy_default_video_creation(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """默认视频创建策略"""
        return {"strategy": "default_video_creation", "parameters": params}

    def _strategy_fast_highlight_reel(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """快速精彩集锦策略"""
        return {"strategy": "fast_highlight_reel", "parameters": params}

    def _strategy_cinematic_storytelling(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """电影级叙事策略"""
        return {"strategy": "cinematic_storytelling", "parameters": params}

    def _strategy_social_media_optimized(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """社交媒体优化策略"""
        return {"strategy": "social_media_optimized", "parameters": params}

    def _strategy_educational_content(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """教育内容策略"""
        return {"strategy": "educational_content", "parameters": params}

    def _strategy_marketing_focused(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """营销导向策略"""
        return {"strategy": "marketing_focused", "parameters": params}


# 演示函数
def main():
    """演示策略选择器功能"""
    print("=== 策略选择器演示 ===")
    
    # 初始化策略选择器
    selector = StrategySelector()
    
    # 模拟目标
    goal = {
        "goal_type": "video_creation",
        "parameters": {
            "theme": "精彩动作集锦",
            "duration": 30,
            "target_platforms": ["douyin", "bilibili"]
        }
    }
    
    # 模拟上下文
    context = {
        "user_id": "user_123",
        "timestamp": "2023-12-01T12:00:00Z"
    }
    
    # 模拟可用资源
    resources = {
        "cpu_cores": 4,
        "ram_gb": 16,
        "gpu_count": 1,
        "disk_space_gb": 100
    }
    
    # 选择策略
    result = selector.select_strategy(goal, context, resources)
    
    print(f"选择的策略: {result['strategy_name']}")
    print(f"置信度: {result['confidence']:.2f}")
    print(f"原因: {result['reasoning']}")
    print(f"指导参数: {result['guidance_parameters']}")


if __name__ == "__main__":
    main()
