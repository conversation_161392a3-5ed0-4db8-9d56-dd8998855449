# backend.agent.knowledge_base.memory_manager

import json
import os
from typing import Any
from typing import Dict
from typing import Optional

# 假设数据存储在项目根目录下的 data/kb/ 文件夹中
DEFAULT_KB_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", "..", "..", "data", "kb")
SHORT_TERM_MEMORY_FILE = os.path.join(DEFAULT_KB_PATH, "short_term_memory.json")

class MemoryManager:
    """管理智能体的短期和长期记忆"""

    def __init__(self, knowledge_base_path: str = DEFAULT_KB_PATH):
        """
        初始化 MemoryManager。

        Args:
            knowledge_base_path (str): 知识库数据存储的根路径。
        """
        self.kb_path = knowledge_base_path
        self.short_term_memory_file = os.path.join(self.kb_path, "short_term_memory.json")
        self._ensure_kb_directory()
        self.short_term_memory: Dict[str, Any] = self._load_json_file(self.short_term_memory_file)

    def _ensure_kb_directory(self):
        """确保知识库目录存在"""
        if not os.path.exists(self.kb_path):
            os.makedirs(self.kb_path)
            print("知识库目录已创建: {self.kb_path}")

    def _load_json_file(self, file_path: str) -> Dict[str, Any]:
        """
        从JSON文件加载数据。

        Args:
            file_path (str): JSON文件的路径。

        Returns:
            Dict[str, Any]: 加载的数据，如果文件不存在或解析失败则返回空字典。
        """
        if os.path.exists(file_path):
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    return json.load(f)
            except json.JSONDecodeError:
                print("错误: 无法解析JSON文件 {file_path}。将使用空数据结构。")
            except Exception as e:
                print(f"加载JSON文件 {file_path} 时出错: {e}")
        return {}

    def _save_json_file(self, data: Dict[str, Any], file_path: str):
        """
        将数据保存到JSON文件。

        Args:
            data (Dict[str, Any]): 要保存的数据。
            file_path (str): JSON文件的路径。
        """
        try:
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print("保存JSON文件 {file_path} 时出错: {e}")

    # --- 短期记忆管理 ---
    def update_short_term_memory(self, key: str, value: Any):
        """
        更新短期记忆中的特定键值对。

        Args:
            key (str): 要更新的键。
            value (Any): 对应的值。
        """
        self.short_term_memory[key] = value
        self._save_json_file(self.short_term_memory, self.short_term_memory_file)
        print("短期记忆已更新: {key} = {value}")

    def get_from_short_term_memory(self, key: str, default: Optional[Any] = None) -> Optional[Any]:
        """
        从短期记忆中获取值。

        Args:
            key (str): 要获取的键。
            default (Optional[Any], optional): 如果键不存在则返回的默认值。Defaults to None.

        Returns:
            Optional[Any]: 获取到的值或默认值。
        """
        return self.short_term_memory.get(key, default)

    def clear_short_term_memory(self):
        """清空短期记忆"""
        self.short_term_memory = {}
        self._save_json_file(self.short_term_memory, self.short_term_memory_file)
        print("短期记忆已清空。")

    # --- 长期记忆管理 (占位符) ---
    # 长期记忆的具体实现会分散到 RuleStore, UserProfileStore, MediaFeatureStore 等专门的类中
    # MemoryManager 可以提供一个统一的接口或协调这些存储

    def store_long_term_knowledge(self, category: str, identifier: str, data: Dict[str, Any]):
        """
        存储长期知识 (通用占位符方法)。
        实际应用中，会调用更具体的存储类的方法。

        Args:
            category (str): 知识的类别 (e.g., 'rules', 'user_profiles', 'media_features')。
            identifier (str): 该条知识的唯一标识符。
            data (Dict[str, Any]): 要存储的知识内容。
        """
        # TODO: 根据 category 调用相应的存储模块
        # 例如: if category == 'rules': self.rule_store.add_rule(identifier, data)
        print("长期知识存储请求 (占位符): 类别={category}, 标识符={identifier}, 数据={data}")
        # 示例：简单地存入一个以类别命名的JSON文件
        category_file = os.path.join(self.kb_path, "long_term_{category}.json")
        current_data = self._load_json_file(category_file)
        current_data[identifier] = data
        self._save_json_file(current_data, category_file)

    def retrieve_long_term_knowledge(self, category: str, identifier: str) -> Optional[Dict[str, Any]]:
        """
        检索长期知识 (通用占位符方法)。

        Args:
            category (str): 知识的类别。
            identifier (str): 知识的唯一标识符。

        Returns:
            Optional[Dict[str, Any]]: 检索到的知识内容，如果不存在则返回None。
        """
        # TODO: 根据 category 调用相应的检索模块
        print("长期知识检索请求 (占位符): 类别={category}, 标识符={identifier}")
        category_file = os.path.join(self.kb_path, "long_term_{category}.json")
        current_data = self._load_json_file(category_file)
        return current_data.get(identifier)

if __name__ == "__main__":
    # 测试 MemoryManager
    # 注意：直接运行此脚本时，DEFAULT_KB_PATH 的相对路径计算可能不准确
    # 最好在项目根目录通过模块导入的方式测试
    test_kb_path = os.path.join(os.getcwd(), "test_kb_data")  # 创建一个临时的测试知识库目录
    if not os.path.exists(test_kb_path):
        os.makedirs(test_kb_path)

    manager = MemoryManager(knowledge_base_path=test_kb_path)

    # 测试短期记忆
    manager.update_short_term_memory("current_task_id", "task_123")
    manager.update_short_term_memory("user_preference", {"style": "fast-paced", "music": "electronic"})
    print("当前任务ID:", manager.get_from_short_term_memory("current_task_id"))
    print("用户偏好:", manager.get_from_short_term_memory("user_preference"))

    # 测试长期记忆 (占位符)
    manager.store_long_term_knowledge("rules", "rule_001", {"condition": "duration > 60", "action": "add_intro"})
    retrieved_rule = manager.retrieve_long_term_knowledge("rules", "rule_001")
    print("检索到的规则:", retrieved_rule)

    manager.clear_short_term_memory()
    print("清空后短期记忆中的任务ID:", manager.get_from_short_term_memory("current_task_id"))

    # 清理测试目录和文件
    # import shutil
    # if os.path.exists(test_kb_path):
    #     shutil.rmtree(test_kb_path)
    #     print("测试目录 {test_kb_path} 已删除")
