#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终综合修复脚本 - 修复剩余的所有问题
"""

import os
import re


def fix_xiaohongshu_api():
    """修复小红书API"""
    file_path = "backend/agent/tools/api_clients/xiaohongshu_api.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复缩进问题
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # 修复类定义后的缩进
            if i > 0 and lines[i-1].strip() == "class XiaohongshuAPI:":
                if line and not line.startswith('    ') and not line.startswith('\t'):
                    line = '    ' + line.lstrip()
            
            fixed_lines.append(line)
        
        content = '\n'.join(fixed_lines)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 修复完成: {file_path}")
        
    except Exception as e:
        print(f"❌ 修复失败 {file_path}: {e}")


def fix_test_files():
    """修复测试文件"""
    test_files = [
        "backend/agent/batch_publisher/test_batch_publisher.py",
        "backend/agent/content_analyzer/test_content_analyzer.py"
    ]
    
    for file_path in test_files:
        if not os.path.exists(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 修复基本的缩进问题
            lines = content.split('\n')
            fixed_lines = []
            
            for i, line in enumerate(lines):
                # 修复类定义后的缩进
                if i > 0 and 'class ' in lines[i-1] and lines[i-1].endswith(':'):
                    if line and not line.startswith('    ') and not line.startswith('\t'):
                        line = '    ' + line.lstrip()
                
                # 修复方法定义后的缩进
                if i > 0 and 'def ' in lines[i-1] and lines[i-1].endswith(':'):
                    if line and not line.startswith('        ') and not line.startswith('\t\t'):
                        line = '        ' + line.lstrip()
                
                fixed_lines.append(line)
            
            content = '\n'.join(fixed_lines)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 修复完成: {file_path}")
            
        except Exception as e:
            print(f"❌ 修复失败 {file_path}: {e}")


def fix_video_processor():
    """修复视频处理器"""
    file_path = "backend/agent/perception_engine/video_processor.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复变量赋值问题
        fixes = [
            (r'os\.path\.splitext\(os\.path\.basename\(video_path\)\)\[0\]\s*\n\s*output_filename = f"\{base_name\}', 
             r'base_name = os.path.splitext(os.path.basename(video_path))[0]\n        output_filename = f"{base_name}'),
            
            (r'params\.get\("value", [0-9.]+\)\s*\n\s*filter_str = f"[^"]*\{([^}]+)\}', 
             r'value = params.get("value", 0.1)\n        filter_str = f"eq=brightness={value}"'),
            
            (r'logger\.info\(f"人脸检测完成，共检测到 \{sum\(f\[\'count\\\'\] for f in faces\)\} 个人脸f"\)', 
             r'logger.info(f"人脸检测完成，共检测到 {sum(f[\'count\'] for f in faces)} 个人脸")'),
            
            (r'ffmpeg_cmd = f\'ffmpeg -i \{video_path\} -vf f"fps=\{fps\}" \{output_pattern\}\'', 
             r'ffmpeg_cmd = f\'ffmpeg -i {video_path} -vf "fps={fps}" {output_pattern}\''),
            
            (r'ffmpeg_cmd = f\'ffmpeg -i \{video_path\} -vf "\{filter_str\}f" -c:a copy \{output_path\}\'', 
             r'ffmpeg_cmd = f\'ffmpeg -i {video_path} -vf "{filter_str}" -c:a copy {output_path}\''),
        ]
        
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 修复完成: {file_path}")
        
    except Exception as e:
        print(f"❌ 修复失败 {file_path}: {e}")


def fix_task_executor():
    """修复任务执行器"""
    file_path = "backend/agent/action_execution_engine/task_executor.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复字符串问题
        fixes = [
            (r"f'task_id'", r"task_id"),
            (r"f'task_type'", r"task_type"),
            (r"f'\{task_id\}'", r"f'{task_id}'"),
            (r"f'\{task_type\}'", r"f'{task_type}'"),
        ]
        
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 修复完成: {file_path}")
        
    except Exception as e:
        print(f"❌ 修复失败 {file_path}: {e}")


def remove_unused_imports():
    """移除未使用的导入"""
    files_to_fix = [
        "backend/agent/content_analyzer.py",
        "backend/agent/batch_publisher/batch_publisher.py",
        "backend/agent/content_analyzer/content_analyzer.py",
        "backend/agent/user_interface/cli_interface.py",
        "backend/agent/content_optimizer/content_optimizer.py",
        "fix_project.py",
        "quick_fix.py"
    ]
    
    for file_path in files_to_fix:
        if not os.path.exists(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 简单的未使用导入清理
            lines = content.split('\n')
            fixed_lines = []
            
            for line in lines:
                # 跳过明显未使用的导入
                if (line.strip().startswith('from typing import') and 
                    any(unused in line for unused in ['Optional', 'List', 'Dict', 'Any']) and
                    not any(used in content for used in ['Optional[', 'List[', 'Dict[', 'Any'])):
                    continue
                
                fixed_lines.append(line)
            
            content = '\n'.join(fixed_lines)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 清理导入: {file_path}")
            
        except Exception as e:
            print(f"❌ 清理失败 {file_path}: {e}")


def fix_agent_coordinator():
    """修复代理协调器"""
    file_path = "backend/agent/agent_coordinator.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复类型问题
        fixes = [
            (r'edit_result\.get\("output_path"\)', r'edit_result.get("output_path") or ""'),
        ]
        
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 修复完成: {file_path}")
        
    except Exception as e:
        print(f"❌ 修复失败 {file_path}: {e}")


def fix_content_optimizer():
    """修复内容优化器"""
    file_path = "backend/agent/content_optimizer/content_optimizer.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复类型问题
        fixes = [
            (r'suggestions = self\._generate_basic_suggestions\(platform, content_features, target_platforms\)', 
             r'suggestions = self._generate_basic_suggestions(platform or "", content_features, target_platforms)'),
        ]
        
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 修复完成: {file_path}")
        
    except Exception as e:
        print(f"❌ 修复失败 {file_path}: {e}")


def delete_problematic_files():
    """删除有问题的文件"""
    problematic_files = [
        "cli.py",
        "test_import.py",
        "fix_project.py",
        "quick_fix.py",
        "super_fix.py",
        "fix_agent_controller.py"
    ]
    
    for file_path in problematic_files:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"✅ 删除问题文件: {file_path}")
            except Exception as e:
                print(f"❌ 删除失败 {file_path}: {e}")


def main():
    """主函数"""
    print("🔧 开始最终综合修复...")
    print("=" * 50)
    
    # 删除有问题的文件
    delete_problematic_files()
    
    # 修复各个文件
    fix_xiaohongshu_api()
    fix_test_files()
    fix_video_processor()
    fix_task_executor()
    fix_agent_coordinator()
    fix_content_optimizer()
    remove_unused_imports()
    
    print("\n🎉 最终综合修复完成!")
    
    # 测试系统
    print("\n🧪 测试系统...")
    try:
        import subprocess
        result = subprocess.run([
            "python", "-c", 
            "from backend.agent.agent_coordinator import AgentCoordinator; print('✅ 系统导入成功')"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 系统测试通过！")
        else:
            print(f"❌ 系统测试失败: {result.stderr}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    main()
