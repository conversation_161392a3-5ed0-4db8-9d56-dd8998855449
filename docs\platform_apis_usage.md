# 平台API客户端使用指南

本文档介绍了如何使用IntelliCutAgent中的平台API客户端获取视频播放数据和收益信息。

## 支持的平台

目前支持以下平台：

1. 西瓜视频 (XiguaAPI)
2. 今日头条 (ToutiaoAPI)
3. 小红书 (XiaohongshuAPI)
4. YouTube (YouTubeAPI)

## 认证方式

所有平台API客户端支持两种认证方式：

1. 使用凭证文件（credentials_path）
2. 使用Cookie或API密钥（cookie/api_key）

## 基本用法

### 导入API客户端

```python
from backend.agent.tools.api_clients import (
    YouTubeAPI,
    XiguaAPI,
    ToutiaoAPI,
    XiaohongshuAPI
)
```

### 初始化API客户端

```python
# 西瓜视频
xigua_api = XiguaAPI(
    credentials_path="path/to/xigua_credentials.json",  # 可选
    cookie="your_xigua_cookie"  # 可选
)

# 今日头条
toutiao_api = ToutiaoAPI(
    credentials_path="path/to/toutiao_credentials.json",  # 可选
    cookie="your_toutiao_cookie"  # 可选
)

# 小红书
xiaohongshu_api = XiaohongshuAPI(
    credentials_path="path/to/xiaohongshu_credentials.json",  # 可选
    cookie="your_xiaohongshu_cookie"  # 可选
)

# YouTube
youtube_api = YouTubeAPI(
    credentials_path="path/to/youtube_credentials.json",  # 可选
    api_key="your_youtube_api_key"  # 可选
)
```

### 认证

在使用API之前，需要先进行认证：

```python
# 认证
success = xigua_api.authenticate()
if success:
    print("认证成功")
else:
    print("认证失败")
```

注意：大多数API方法会在需要时自动调用认证方法，所以通常不需要显式调用。

## 获取视频信息

### 西瓜视频

```python
# 获取视频信息
video_info = xigua_api.get_video_info(video_id="your_video_id")
print(f"视频标题: {video_info['title']}")
print(f"播放量: {video_info['view_count']}")
```

### 今日头条

```python
# 获取内容信息（视频或文章）
content_info = toutiao_api.get_content_info(
    content_id="your_content_id",
    content_type="video"  # 或 "article"
)
print(f"内容标题: {content_info['title']}")
print(f"播放量: {content_info['view_count']}")
```

### 小红书

```python
# 获取笔记信息
note_info = xiaohongshu_api.get_note_info(note_id="your_note_id")
print(f"笔记标题: {note_info['title']}")
print(f"浏览量: {note_info['view_count']}")
```

### YouTube

```python
# 获取视频信息
video_info = youtube_api.get_video_info(video_id="your_video_id")
print(f"视频标题: {video_info['title']}")
print(f"播放量: {video_info['view_count']}")
```

## 获取分析数据

### 西瓜视频

```python
# 获取视频分析数据
analytics = xigua_api.get_video_analytics(
    video_id="your_video_id",
    start_date="2023-01-01",
    end_date="2023-01-31",
    metrics=["views", "likes", "comments", "shares", "watch_time"]
)

# 打印总计
print(f"总播放量: {analytics['totals']['views']}")
print(f"总点赞数: {analytics['totals']['likes']}")
```

### 今日头条

```python
# 获取内容分析数据
analytics = toutiao_api.get_content_analytics(
    content_id="your_content_id",
    content_type="video",  # 或 "article"
    start_date="2023-01-01",
    end_date="2023-01-31",
    metrics=["views", "likes", "comments", "shares", "watch_time", "completion_rate"]
)

# 打印总计
print(f"总播放量: {analytics['totals']['views']}")
print(f"总点赞数: {analytics['totals']['likes']}")
```

### 小红书

```python
# 获取笔记分析数据
analytics = xiaohongshu_api.get_note_analytics(
    note_id="your_note_id",
    start_date="2023-01-01",
    end_date="2023-01-31",
    metrics=["views", "likes", "collects", "comments", "shares", "follows"]
)

# 打印总计
print(f"总浏览量: {analytics['totals']['views']}")
print(f"总点赞数: {analytics['totals']['likes']}")
```

### YouTube

```python
# 获取视频分析数据
analytics = youtube_api.get_video_analytics(
    video_id="your_video_id",
    start_date="2023-01-01",
    end_date="2023-01-31",
    metrics=["views", "likes", "comments", "shares", "watchTimeMinutes"]
)

# 打印总计
print(f"总播放量: {analytics['totals']['views']}")
print(f"总点赞数: {analytics['totals']['likes']}")
```

## 获取收益数据

### 西瓜视频

```python
# 获取收益数据
revenue_data = xigua_api.get_revenue_data(
    video_id="your_video_id",  # 可选，如果不提供则获取所有视频的收益
    start_date="2023-01-01",
    end_date="2023-01-31"
)

# 打印收益摘要
print(f"总收益: {revenue_data['summary']['total_revenue']}")
print(f"广告收益: {revenue_data['summary']['ad_revenue']}")
```

### 今日头条

```python
# 获取收益数据
revenue_data = toutiao_api.get_revenue_data(
    content_id="your_content_id",  # 可选
    content_type="video",  # 可选，"video" 或 "article"
    start_date="2023-01-01",
    end_date="2023-01-31"
)

# 打印收益摘要
print(f"总收益: {revenue_data['summary']['total_revenue']}")
print(f"广告收益: {revenue_data['summary']['ad_revenue']}")
```

### 小红书

```python
# 获取收益数据
revenue_data = xiaohongshu_api.get_revenue_data(
    note_id="your_note_id",  # 可选，如果不提供则获取所有笔记的收益
    start_date="2023-01-01",
    end_date="2023-01-31"
)

# 打印收益摘要
print(f"总收益: {revenue_data['summary']['total_revenue']}")
print(f"品牌合作收益: {revenue_data['summary']['brand_collaboration_revenue']}")
```

## 获取账号信息

### 西瓜视频

```python
# 获取账号信息
account_info = xigua_api.get_account_info()
print(f"用户名: {account_info['username']}")
print(f"粉丝数: {account_info['follower_count']}")
```

### 今日头条

```python
# 获取账号信息
account_info = toutiao_api.get_account_info()
print(f"用户名: {account_info['username']}")
print(f"粉丝数: {account_info['follower_count']}")
```

### 小红书

```python
# 获取账号信息
account_info = xiaohongshu_api.get_account_info()
print(f"用户名: {account_info['username']}")
print(f"粉丝数: {account_info['follower_count']}")
```

### YouTube

```python
# 获取频道信息
channel_id = "your_channel_id"
channel_info = youtube_api.get_channel_info(channel_id=channel_id)
print(f"频道名称: {channel_info['channel_title']}")
print(f"订阅者数: {channel_info['subscriber_count']}")
```

## 示例脚本

查看 `examples/platform_revenue_example.py` 文件，了解如何使用这些API客户端获取多个平台的收益数据。

运行示例脚本：

```bash
python examples/platform_revenue_example.py --platforms youtube xigua toutiao xiaohongshu --start-date 2023-01-01 --end-date 2023-01-31
```

## 注意事项

1. 这些API客户端目前是模拟实现，实际应用中需要替换为真实的API调用。
2. 认证信息（凭证文件、Cookie、API密钥等）应妥善保管，不要提交到代码仓库。
3. 请遵守各平台的API使用限制和条款。
4. 收益数据可能会有延迟，通常前一天的数据要到第二天才能获取。