import asyncio
import os
import sys
import unittest
from unittest.mock import MagicMock

# 动态调整sys.path以确保backend模块可导入
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from main import IntelliCutAgentCore


class TestIntelliCutAgentCore(unittest.TestCase):

    def setUp(self):
        # 初始化IntelliCutAgentCore实例
        self.agent = IntelliCutAgentCore()

        # 模拟所有依赖的业务模块
        self.agent.material_manager = MagicMock()
        self.agent.content_analyzer = MagicMock()
        self.agent.smart_editor = MagicMock()
        self.agent.platform_adapter = MagicMock()
        self.agent.batch_publisher = MagicMock()

        # 配置模拟行为
        self.agent.material_manager.upload_material.return_value = {
            "id": "mock_material_id",
            "path": "mock/path/material.mp4",
        }
        self.agent.content_analyzer.analyze_video.return_value = {"speech_to_text": "mock analysis", "scenes": []}
        self.agent.smart_editor.auto_edit_video.return_value = "mock/path/edited_video.mp4"

        # 模拟platform_adapter的adapt_for_platform和generate_metadata方法
        self.agent.platform_adapter.adapt_for_platform.side_effect = (
            lambda video_path, platform: f"mock/path/adapted_{platform}.mp4"
        )
        self.agent.platform_adapter.generate_metadata.side_effect = lambda platform, title, description, tags: {
            "title": title,
            "description": description,
            "tags": tags,
            "platform_specific": platform,
        }

        # 模拟batch_publisher的publish_to_platforms方法
        self.agent.batch_publisher.publish_to_platforms.side_effect = lambda video_path, platforms, metadata: {
            p: {"status": "completed", "video_id": "mock_video_id_{p}", "url": f"http://mock.com/{p}/video"}
            for p in platforms
        }

    def test_create_and_publish_video_success(self):
        # 模拟一个成功的视频创作与发布请求
        user_command = {
            "action": "create_and_publish_video",
            "params": {
                "material_path": "/path/to/raw_footage.mp4",
                "platforms": ["douyin", "kuaishou", "bilibili"],
                "edit_rules": {"duration": "30s", "style": "action"},
                "title": "精彩动作集锦",
                "description": "AI自动剪辑的精彩动作电影集锦",
                "tags": ["动作", "AI", "电影"],
            },
        }
        config_data = {}

        # 运行process_request
        result = asyncio.run(self.agent.process_request(user_command, config_data))

        # 验证结果
        self.assertEqual(result["status"], "success")
        self.assertIn("message", result)
        self.assertIn("publish_results", result)

        # 验证每个平台都成功发布
        self.assertIn("douyin", result["publish_results"])
        self.assertEqual(result["publish_results"]["douyin"]["status"], "completed")
        self.assertIn("kuaishou", result["publish_results"])
        self.assertEqual(result["publish_results"]["kuaishou"]["status"], "completed")
        self.assertIn("bilibili", result["publish_results"])
        self.assertEqual(result["publish_results"]["bilibili"]["status"], "completed")

        # 验证各模块方法被调用
        self.agent.material_manager.upload_material.assert_called_once()
        self.agent.content_analyzer.analyze_video.assert_called_once()
        self.agent.smart_editor.auto_edit_video.assert_called_once()
        self.assertEqual(self.agent.platform_adapter.adapt_for_platform.call_count, 3)
        self.assertEqual(self.agent.platform_adapter.generate_metadata.call_count, 3)
        self.assertEqual(self.agent.batch_publisher.publish_to_platforms.call_count, 3)

    def test_create_and_publish_video_missing_material_path(self):
        # 模拟缺少素材路径的请求
        user_command = {
            "action": "create_and_publish_video",
            "params": {"platforms": ["douyin"], "edit_rules": {"duration": "30s"}},
        }
        config_data = {}

        result = asyncio.run(self.agent.process_request(user_command, config_data))

        self.assertEqual(result["status"], "error")
        self.assertIn("缺少素材路径", result["message"])

    def test_create_and_publish_video_upload_material_failure(self):
        # 模拟素材上传失败
        self.agent.material_manager.upload_material.return_value = None

        user_command = {
            "action": "create_and_publish_video",
            "params": {
                "material_path": "/path/to/raw_footage.mp4",
                "platforms": ["douyin"],
                "edit_rules": {"duration": "30s"},
            },
        }
        config_data = {}

        result = asyncio.run(self.agent.process_request(user_command, config_data))

        self.assertEqual(result, {"status": "error", "message": "素材上传失败。"})

    def test_create_and_publish_video_content_analysis_failure(self):
        # 模拟内容分析失败
        self.agent.content_analyzer.analyze_video.return_value = None

        user_command = {
            "action": "create_and_publish_video",
            "params": {
                "material_path": "/path/to/raw_footage.mp4",
                "platforms": ["douyin"],
                "edit_rules": {"duration": "30s"},
            },
        }
        config_data = {}

        result = asyncio.run(self.agent.process_request(user_command, config_data))

        self.assertEqual(result["status"], "error")
        self.assertIn("内容分析失败", result["message"])

    def test_create_and_publish_video_smart_editor_failure(self):
        # 模拟智能剪辑失败
        self.agent.smart_editor.auto_edit_video.return_value = None

        user_command = {
            "action": "create_and_publish_video",
            "params": {
                "material_path": "/path/to/raw_footage.mp4",
                "platforms": ["douyin"],
                "edit_rules": {"duration": "30s"},
            },
        }
        config_data = {}

        result = asyncio.run(self.agent.process_request(user_command, config_data))

        self.assertEqual(result["status"], "error")
        self.assertIn("视频剪辑失败", result["message"])

    def test_create_and_publish_video_platform_adapt_failure(self):
        # 模拟平台适配失败，但其他平台成功
        self.agent.platform_adapter.adapt_for_platform.side_effect = [
            "mock/path/adapted_douyin.mp4",  # douyin 成功
            None,  # kuaishou 失败
            "mock/path/adapted_bilibili.mp4",  # bilibili 成功
        ]

        user_command = {
            "action": "create_and_publish_video",
            "params": {
                "material_path": "/path/to/raw_footage.mp4",
                "platforms": ["douyin", "kuaishou", "bilibili"],
                "edit_rules": {"duration": "30s"},
            },
        }
        config_data = {}

        result = asyncio.run(self.agent.process_request(user_command, config_data))

        self.assertEqual(result["status"], "success")  # 即使部分失败，只要有成功就认为是成功
        self.assertIn("douyin", result["publish_results"])
        self.assertNotIn("kuaishou", result["publish_results"])  # kuaishou 应该被跳过
        self.assertIn("bilibili", result["publish_results"])

    def test_create_and_publish_video_unknown_action(self):
        # 模拟未知动作的请求
        user_command = {"action": "unknown_action", "params": {"some_param": "value"}}
        config_data = {}

        result = asyncio.run(self.agent.process_request(user_command, config_data))

        self.assertEqual(result["status"], "warning")
        self.assertIn("未知命令或未实现的核心业务流程", result["message"])


if __name__ == "__main__":
    unittest.main()
