"""
VideoEditor 单元测试
"""

import os
import shutil
import sys
import tempfile
import unittest
from unittest.mock import MagicMock, patch

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from backend.agent.smart_editor.video_editor import VideoEditor


class TestVideoEditor(unittest.TestCase):
    """VideoEditor 类的单元测试"""

    def setUp(self):
        """测试前的准备工作"""
        self.temp_dir = tempfile.mkdtemp()
        self.video_editor = VideoEditor(temp_dir=self.temp_dir, auto_cleanup=False)

        # 创建测试资源目录
        self.test_resources_dir = os.path.join(self.temp_dir, "test_resources")
        os.makedirs(self.test_resources_dir, exist_ok=True)

    def tearDown(self):
        """测试后的清理工作"""
        # 清理临时文件
        self.video_editor.cleanup_temp_files()

        # 删除临时目录
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    @patch("backend.agent.smart_editor.video_editor.mp.VideoFileClip")
    def test_load_video(self, mock_video_file_clip):
        """测试加载视频功能"""
        # 设置模拟对象
        mock_clip = MagicMock()
        mock_video_file_clip.return_value = mock_clip

        # 创建测试视频文件
        test_video_path = os.path.join(self.test_resources_dir, "test_video.mp4")
        with open(test_video_path, "w") as f:
            f.write("dummy video content")

        # 测试加载视频
        result = self.video_editor.load_video(test_video_path)

        # 验证结果
        self.assertEqual(result, mock_clip)
        mock_video_file_clip.assert_called_once_with(test_video_path)

    @patch("backend.agent.smart_editor.video_editor.mp.VideoFileClip")
    def test_load_video_unsupported_format(self, mock_video_file_clip):
        """测试加载不支持的视频格式"""
        # 创建测试视频文件
        test_video_path = os.path.join(self.test_resources_dir, "test_video.xyz")
        with open(test_video_path, "w") as f:
            f.write("dummy video content")

        # 测试加载视频
        result = self.video_editor.load_video(test_video_path)

        # 验证结果
        self.assertIsNone(result)
        mock_video_file_clip.assert_not_called()

    @patch("backend.agent.smart_editor.video_editor.mp.VideoFileClip")
    def test_cut_video(self, mock_video_file_clip):
        """测试剪切视频功能"""
        # 设置模拟对象
        mock_clip = MagicMock()
        mock_subclip = MagicMock()
        mock_clip.subclip.return_value = mock_subclip
        mock_clip.duration = 10.0

        # 测试剪切视频
        result = self.video_editor.cut_video(mock_clip, 2.0, 8.0)

        # 验证结果
        self.assertEqual(result, mock_subclip)
        mock_clip.subclip.assert_called_once_with(2.0, 8.0)

    @patch("backend.agent.smart_editor.video_editor.cv2.VideoCapture")
    def test_extract_scenes(self, mock_video_capture):
        """测试场景提取功能"""
        # 设置模拟对象
        mock_cap = MagicMock()
        mock_video_capture.return_value = mock_cap
        mock_cap.isOpened.return_value = True
        mock_cap.get.side_effect = lambda prop: 30.0 if prop == 5 else 300  # FPS=30, 帧数=300

        # 模拟读取帧
        mock_cap.read.side_effect = [
            (True, MagicMock()),  # 第1帧
            (True, MagicMock()),  # 第2帧
            (True, MagicMock()),  # 第3帧
            (False, None),  # 结束
        ]

        # 模拟场景检测模块
        with patch("backend.agent.smart_editor.scene_detection.extract_scenes_threshold") as mock_extract:
            mock_extract.return_value = [(0.0, 5.0), (5.0, 10.0)]

            # 测试场景提取
            result = self.video_editor.extract_scenes("dummy_path.mp4")

            # 验证结果
            self.assertEqual(result, [(0.0, 5.0), (5.0, 10.0)])
            mock_extract.assert_called_once()

    @patch("backend.agent.smart_editor.video_editor.VideoEditor.load_video")
    @patch("backend.agent.smart_editor.video_editor.VideoEditor.extract_scenes")
    @patch("backend.agent.smart_editor.video_editor.mp.concatenate_videoclips")
    def test_extract_highlights(self, mock_concatenate, mock_extract_scenes, mock_load_video):
        """测试精彩片段提取功能"""
        # 设置模拟对象
        mock_video_clip = MagicMock()
        mock_video_clip.duration = 30.0
        mock_load_video.return_value = mock_video_clip

        mock_extract_scenes.return_value = [(0.0, 5.0), (10.0, 15.0), (20.0, 25.0)]

        mock_subclip1 = MagicMock()
        mock_subclip2 = MagicMock()
        mock_video_clip.subclip.side_effect = [mock_subclip1, mock_subclip2]

        mock_result_clip = MagicMock()
        mock_concatenate.return_value = mock_result_clip

        # 测试精彩片段提取
        result = self.video_editor.extract_highlights("dummy_path.mp4", duration=10.0)

        # 验证结果
        self.assertEqual(result, mock_result_clip)
        mock_load_video.assert_called_once_with("dummy_path.mp4")
        mock_extract_scenes.assert_called_once()
        self.assertEqual(mock_video_clip.subclip.call_count, 2)
        mock_concatenate.assert_called_once()

    @patch("backend.agent.smart_editor.video_editor.VideoEditor._apply_brightness")
    def test_apply_effect(self, mock_apply_brightness):
        """测试应用特效功能"""
        # 设置模拟对象
        mock_video_clip = MagicMock()
        mock_result_clip = MagicMock()
        mock_apply_brightness.return_value = mock_result_clip

        # 测试应用特效
        result = self.video_editor.apply_effect(mock_video_clip, "brightness", factor=1.5)

        # 验证结果
        self.assertEqual(result, mock_result_clip)
        mock_apply_brightness.assert_called_once_with(mock_video_clip, factor=1.5)

    def test_apply_effect_unsupported(self):
        """测试应用不支持的特效"""
        # 设置模拟对象
        mock_video_clip = MagicMock()

        # 测试应用特效
        result = self.video_editor.apply_effect(mock_video_clip, "unsupported_effect")

        # 验证结果
        self.assertIsNone(result)

    @patch("backend.agent.smart_editor.video_editor.mp.VideoFileClip")
    def test_register_temp_file(self, _):
        """测试注册临时文件功能"""
        # 创建测试文件
        test_file_path = os.path.join(self.temp_dir, "test_temp_file.txt")
        with open(test_file_path, "w") as f:
            f.write("test content")

        # 注册临时文件
        self.video_editor.register_temp_file(test_file_path)

        # 验证结果
        self.assertIn(test_file_path, self.video_editor.temp_files)

    @patch("backend.agent.smart_editor.video_editor.mp.VideoFileClip")
    def test_cleanup_temp_files(self, _):
        """测试清理临时文件功能"""
        # 创建测试文件
        test_file_path = os.path.join(self.temp_dir, "test_temp_file.txt")
        with open(test_file_path, "w") as f:
            f.write("test content")

        # 注册临时文件
        self.video_editor.register_temp_file(test_file_path)

        # 清理临时文件
        self.video_editor.cleanup_temp_files()

        # 验证结果
        self.assertFalse(os.path.exists(test_file_path))
        self.assertEqual(len(self.video_editor.temp_files), 0)


if __name__ == "__main__":
    unittest.main()
