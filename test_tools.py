#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试工具模块的功能。
"""

import json
import logging
import os

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# 导入工具模块
from backend.agent.tools import (
    BasicAudioAnalyzer,
    BasicVideoEditor,
    ConfigManager,
    FileHelper,
    IDGenerator,
    TaskManager,
    TextProcessor,
    VideoProcessor,
    YouTubeAPI,
)


def test_video_tools():
    """测试视频工具"""
    logger.info("=== 测试视频工具 ===")

    # 创建输出和临时目录
    os.makedirs("output", exist_ok=True)
    os.makedirs("temp", exist_ok=True)

    # 初始化视频编辑器
    video_editor = BasicVideoEditor(temp_dir="temp", output_dir="output")

    # 模拟视频路径
    video_path = "temp/sample_video.mp4"

    # 创建一个空的示例视频文件
    with open(video_path, "wb") as f:
        f.write(b"MOCK VIDEO FILE")

    # 测试视频剪切
    cut_video_path = video_editor.cut_video(video_path, 10.0, 20.0)
    logger.info("剪切视频路径: {cut_video_path}")

    # 测试添加效果
    effect_video_path = video_editor.add_effect(video_path, "brightness", {"value": 0.2})
    logger.info("添加效果后的视频路径: {effect_video_path}")

    # 测试获取视频信息
    video_info = video_editor.get_video_info(video_path)
    logger.info("视频信息: {json.dumps(video_info, indent=2, ensure_ascii=False)}")

    # 初始化视频处理器
    video_processor = VideoProcessor(temp_dir="temp")

    # 测试场景检测
    scenes = video_processor.detect_scenes(video_path)
    logger.info("检测到的场景数量: {len(scenes)}")

    # 测试人脸检测
    faces = video_processor.detect_faces(video_path)
    logger.info("检测到的人脸: {json.dumps(faces, indent=2, ensure_ascii=False)}")

    # 测试生成缩略图
    thumbnail_path = video_processor.generate_thumbnail(video_path)
    logger.info("缩略图路径: {thumbnail_path}")


def test_audio_tools():
    """测试音频工具"""
    logger.info("=== 测试音频工具 ===")

    # 创建临时目录
    os.makedirs("temp", exist_ok=True)

    # 初始化音频分析器
    audio_analyzer = BasicAudioAnalyzer(temp_dir="temp")

    # 模拟音频路径
    audio_path = "temp/sample_audio.wav"

    # 创建一个空的示例音频文件
    with open(audio_path, "wb") as f:
        f.write(b"MOCK AUDIO FILE")

    # 测试获取音频信息
    audio_info = audio_analyzer.get_audio_info(audio_path)
    logger.info("音频信息: {json.dumps(audio_info, indent=2, ensure_ascii=False)}")

    # 测试语音片段检测
    speech_segments = audio_analyzer.detect_speech_segments(audio_path)
    logger.info("检测到的语音片段数量: {len(speech_segments)}")

    # 测试音频转录
    transcription = audio_analyzer.transcribe_audio(audio_path)
    logger.info("转录结果: {json.dumps(transcription, indent=2, ensure_ascii=False)}")

    # 测试生成字幕
    subtitles_path = audio_analyzer.generate_subtitles(transcription)
    logger.info("字幕文件路径: {subtitles_path}")


def test_nlp_tools():
    """测试NLP工具"""
    logger.info("=== 测试NLP工具 ===")

    # 初始化文本处理器
    text_processor = TextProcessor()

    # 测试文本
    text = "智能视频剪辑是一种利用人工智能技术自动处理视频内容的方法。它可以分析视频中的场景、人物、动作和情感，提取关键片段，并根据预设的规则或风格进行编辑。这种技术大大提高了视频制作的效率，降低了对专业技能的要求。"

    # 测试语言检测
    language = text_processor.detect_language(text)
    logger.info("检测到的语言: {language}")

    # 测试关键词提取
    keywords = text_processor.extract_keywords(text)
    logger.info("提取的关键词: {json.dumps(keywords, indent=2, ensure_ascii=False)}")

    # 测试情感分析
    sentiment = text_processor.analyze_sentiment(text)
    logger.info("情感分析结果: {json.dumps(sentiment, indent=2, ensure_ascii=False)}")

    # 测试文本摘要
    summary = text_processor.summarize_text(text)
    logger.info("文本摘要: {json.dumps(summary, indent=2, ensure_ascii=False)}")

    # 测试标题生成
    titles = text_processor.generate_title(text)
    logger.info("生成的标题: {json.dumps(titles, indent=2, ensure_ascii=False)}")

    # 测试标签生成
    tags = text_processor.generate_tags(text)
    logger.info("生成的标签: {json.dumps(tags, indent=2, ensure_ascii=False)}")


def test_utility_tools():
    """测试实用工具"""
    logger.info("=== 测试实用工具 ===")

    # 创建测试目录
    os.makedirs("test_data", exist_ok=True)

    # 初始化文件辅助工具
    file_helper = FileHelper(base_dir="test_data")

    # 测试目录创建
    test_dir = file_helper.ensure_directory("test_subdir")
    logger.info("创建的目录: {test_dir}")

    # 测试文件写入
    test_file = os.path.join(test_dir, "test_file.txt")
    file_helper.write_text(test_file, "这是一个测试文件")
    logger.info("写入的文件: {test_file}")

    # 测试文件读取
    content = file_helper.read_text(test_file)
    logger.info("读取的内容: {content}")

    # 测试文件元数据获取
    metadata = file_helper.get_file_metadata(test_file)
    logger.info("文件元数据: {json.dumps(metadata, indent=2, ensure_ascii=False)}")

    # 初始化配置管理器
    config_manager = ConfigManager(config_dir="test_data/config")

    # 测试配置设置和保存
    config_manager.set("app_name", "IntelliCutAgent")
    config_manager.set("version", "1.0.0")
    config_manager.set("settings", {"output_dir": "output", "temp_dir": "temp", "max_threads": 4})
    config_manager.save_config()
    logger.info("保存的配置: {json.dumps(config_manager.config, indent=2, ensure_ascii=False)}")

    # 测试ID生成器
    uuid = IDGenerator.generate_uuid()
    short_id = IDGenerator.generate_short_id()
    timestamp_id = IDGenerator.generate_timestamp_id("task_")
    hash_id = IDGenerator.generate_hash_id("test_data")

    logger.info("生成的UUID: {uuid}")
    logger.info("生成的短ID: {short_id}")
    logger.info("生成的时间戳ID: {timestamp_id}")
    logger.info("生成的哈希ID: {hash_id}")

    # 初始化任务管理器
    task_manager = TaskManager(tasks_dir="test_data/tasks")

    # 测试任务创建
    task_id = task_manager.create_task(
        "video_edit",
        {
            "video_path": "temp/sample_video.mp4",
            "output_path": "output/edited_video.mp4",
            "effects": ["brightness", "contrast"],
        },
    )
    logger.info("创建的任务ID: {task_id}")

    # 测试任务状态更新
    task_manager.update_task_status(task_id, "processing", 25)
    task_manager.update_task_status(task_id, "completed", 100, {"output_path": "output/edited_video.mp4"})

    # 测试任务获取
    task = task_manager.get_task(task_id)
    logger.info("获取的任务: {json.dumps(task, indent=2, ensure_ascii=False)}")


def test_api_clients():
    """测试API客户端"""
    logger.info("=== 测试API客户端 ===")

    # 初始化YouTube API客户端
    youtube_api = YouTubeAPI(api_key="mock_api_key")

    # 测试认证
    authenticated = youtube_api.authenticate()
    logger.info("认证状态: {authenticated}")

    # 测试视频上传
    video_path = "temp/sample_video.mp4"

    # 确保测试视频文件存在
    os.makedirs("temp", exist_ok=True)
    if not os.path.exists(video_path):
        with open(video_path, "wb") as f:
            f.write(b"MOCK VIDEO FILE")

    upload_result = youtube_api.upload_video(
        video_path=video_path,
        title="测试视频",
        description="这是一个测试视频，用于测试YouTube API客户端。",
        tags=["测试", "API", "YouTube"],
    )
    logger.info("上传结果: {json.dumps(upload_result, indent=2, ensure_ascii=False)}")

    # 测试视频信息获取
    video_id = upload_result.get("video_id", "dQw4w9WgXcQ")  # 使用上传返回的ID或默认ID
    video_info = youtube_api.get_video_info(video_id)
    logger.info("视频信息: {json.dumps(video_info, indent=2, ensure_ascii=False)}")

    # 测试视频分析数据获取
    analytics = youtube_api.get_video_analytics(video_id)
    logger.info("视频分析数据: {json.dumps(analytics, indent=2, ensure_ascii=False)}")

    # 测试视频搜索
    search_result = youtube_api.search_videos("智能视频剪辑", max_results=3)
    logger.info("搜索结果: {json.dumps(search_result, indent=2, ensure_ascii=False)}")

    # 测试配额使用情况获取
    quota_usage = youtube_api.get_quota_usage()
    logger.info("配额使用情况: {json.dumps(quota_usage, indent=2, ensure_ascii=False)}")


def main():
    """主函数"""
    logger.info("开始测试工具模块...")

    # 测试视频工具
    test_video_tools()

    # 测试音频工具
    test_audio_tools()

    # 测试NLP工具
    test_nlp_tools()

    # 测试实用工具
    test_utility_tools()

    # 测试API客户端
    test_api_clients()

    logger.info("工具模块测试完成！")


if __name__ == "__main__":
    main()
