#!/usr/bin/env python3
"""
task_executor module
执行由任务规划器生成的具体任务。
"""

import random
import time
from typing import Any, Callable, Dict, Optional


class TaskExecutor:
    """
    任务执行器，负责执行由任务规划器生成的具体任务。
    """

    def __init__(self, tool_interface: Optional[Any] = None, resource_allocator: Optional[Any] = None):
        """
        初始化 TaskExecutor。
        Args:
            tool_interface: 工具接口实例，用于调用外部工具或服务。
            resource_allocator: 资源分配器实例，用于管理任务执行所需的资源。
        """
        self.tool_interface = tool_interface
        self.resource_allocator = resource_allocator
        self.task_handlers: Dict[str, Callable[[Dict[str, Any]], Dict[str, Any]]] = self._register_task_handlers()
        print("TaskExecutor 初始化完毕。")

    def _register_task_handlers(self) -> Dict[str, Callable[[Dict[str, Any]], Dict[str, Any]]]:
        """注册不同任务类型的处理函数。"""
        return {
            "load_media": self._handle_load_media,
            "analyze_media_content": self._handle_analyze_media_content,
            "select_clips_for_timeline": self._handle_select_clips_for_timeline,
            "assemble_video_timeline": self._handle_assemble_video_timeline,
            "render_final_video": self._handle_render_final_video,
            "prepare_training_data": self._handle_prepare_training_data,
            "train_model": self._handle_train_model,
            "evaluate_model": self._handle_evaluate_model,
            "error_handling": self._handle_error_task,
            "unsupported_goal_type": self._handle_error_task,  # Treat as error
        }

    def execute_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行单个任务。
        Args:
            task (Dict[str, Any]): 要执行的任务。
        Returns:
            Dict[str, Any]: 任务执行结果。
        """
        task_id = task.get("task_id", "unknown_task")
        task_type = task.get("task_type")
        task_params = task.get("parameters", {})
        print(f"开始执行任务 f'{task_id}' (类型: {task_type})。参数: {task_params}")
        start_time = time.time()
        allocated_resources_details = None
        result = {"task_id": task_id, "task_type": task_type}
        try:
            if self.resource_allocator:
                allocated_resources_details = self.resource_allocator.allocate_resources_for_task(task)
                if not allocated_resources_details:
                    raise RuntimeError(f"资源分配失败 for task {task_id}")
                print(f"  任务 f'{task_id}' 已分配资源: {allocated_resources_details}")
            handler = self.task_handlers.get(task_type)
            if not handler:
                raise NotImplementedError(f"任务类型 f'{task_type}' 的处理器未实现。")
            if allocated_resources_details:
                task_params["_allocated_resources"] = allocated_resources_details
            execution_output = handler(task_params)
            result["status"] = "completed"
            result["output"] = execution_output
            print(f"任务 f'{task_id}' 执行成功。输出: {execution_output}")
        except NotImplementedError as e:
            result["status"] = "failed"
            result["error_message"] = str(e)
            print(f"任务 f'{task_id}' 执行失败: {e}")
        except RuntimeError as e:  # 例如资源分配失败
            result["status"] = "failed"
            result["error_message"] = str(e)
            print(f"任务 f'{task_id}' 执行失败: {e}")
        except Exception as e:
            result["status"] = "failed"
            result["error_message"] = f"执行任务 f'{task_id}' 时发生意外错误: {type(e).__name__} - {e}"
            print(result["error_message"])
        finally:
            if self.resource_allocator and allocated_resources_details:
                self.resource_allocator.release_resources_for_task(task, allocated_resources_details)
                print(f"  任务 f'{task_id}' 的资源已释放。")
            end_time = time.time()
            result["execution_time_ms"] = round((end_time - start_time) * 1000)
        return result
    def _handle_load_media(self, params: Dict[str, Any]) -> Dict[str, Any]:
        media_paths_or_ids = params.get("media_paths_or_ids", [])
        print(f"  处理中: 加载媒体 {media_paths_or_ids}")
        time.sleep(random.uniform(0.1, 0.5))
        loaded_media_info = []
        for m_id in media_paths_or_ids:
            info = {
                "id": m_id,
                "path": f"/media/{m_id}",
                "duration": random.uniform(30, 300),
                "format": m_id.split(".")[-1] if "." in m_id else "mp4",
            }
            loaded_media_info.append(info)
        return {"loaded_media_info": loaded_media_info, "count": len(loaded_media_info)}

    def _handle_analyze_media_content(self, params: Dict[str, Any]) -> Dict[str, Any]:
        analysis_type = params.get("analysis_type", "general")
        keywords = params.get("keywords", [])
        print(f"  处理中: 分析媒体内容 (类型: {analysis_type}, 关键词: {keywords})")
        time.sleep(random.uniform(0.5, 2.0))
        analysis_results = {
            "scenes_detected": random.randint(5, 20),
            "dominant_colors": ["blue", "green"],
            "action_segments_found": random.randint(0, 5) if analysis_type == "action_detection" else 0,
        }
        return {"analysis_results": analysis_results}
    def _handle_select_clips_for_timeline(self, params: Dict[str, Any]) -> Dict[str, Any]:
        target_duration = params.get("target_duration_seconds")
        theme = params.get("theme")
        print(f"  处理中: 选择剪辑 (目标时长: {target_duration}s, 主题: {theme})")
        time.sleep(random.uniform(0.2, 0.8))
        selected_clips = [
            {"clip_id": "clip_A", "start_time": 10.5, "end_time": 15.2, "source_media": "video01.mp4"},
            {"clip_id": "clip_B", "start_time": 120.0, "end_time": 128.5, "source_media": "video02.mp4"},
        ]
        if target_duration and target_duration < 30:  # 模拟短视频选择更多片段
            selected_clips.append(
                {"clip_id": "clip_C", "start_time": 5.0, "end_time": 8.0, "source_media": "video01.mp4"}
            )
        return {
            "selected_clips": selected_clips,
            "total_duration_selected": sum(c["end_time"] - c["start_time"] for c in selected_clips),
        }

    def _handle_assemble_video_timeline(self, params: Dict[str, Any]) -> Dict[str, Any]:
        music_pref = params.get("music_preference")
        print(f"  处理中: 编排时间线 (音乐偏好: {music_pref})")
        time.sleep(random.uniform(0.3, 1.0))
        timeline_structure = {"track_count": 2, "total_length_seconds": 25.7, "effects_applied": ["crossfade"]}
        return {"timeline_structure": timeline_structure}

    def _handle_render_final_video(self, params: Dict[str, Any]) -> Dict[str, Any]:
        output_format = params.get("output_format", "mp4")
        quality = params.get("quality", "high")
        print(f"  处理中: 渲染最终视频 (格式: {output_format}, 质量: {quality})")
        render_time = random.uniform(1.0, 5.0) if quality == "high" else random.uniform(0.5, 2.5)
        time.sleep(render_time)
        output_file_path = f"/output/final_video_{int(time.time())}.{output_format}"
        return {"output_file_path": output_file_path, "file_size_mb": random.uniform(10, 200)}
    def _handle_prepare_training_data(self, params: Dict[str, Any]) -> Dict[str, Any]:
        source = params.get("source")
        print(f"  处理中: 准备训练数据 (来源: {source})")
        time.sleep(random.uniform(1, 3))
        return {
            "prepared_data_path": f"/processed_data/{params.get('model_type', 'generic')}/",
            "sample_count": random.randint(1000, 10000),
        }

    def _handle_train_model(self, params: Dict[str, Any]) -> Dict[str, Any]:
        model_name = params.get("model_name")
        print(f"  处理中: 训练模型 ({model_name})")
        time.sleep(random.uniform(5, 15))  # 模拟长时间训练
        return {
            "trained_model_path": f"/models/{model_name}/v{random.randint(1, 5)}.{random.randint(0, 9)}.h5",
            "accuracy": random.uniform(0.7, 0.95),
        }

    def _handle_evaluate_model(self, params: Dict[str, Any]) -> Dict[str, Any]:
        model_name = params.get("model_name")
        print(f"  处理中: 评估模型 ({model_name})")
        time.sleep(random.uniform(1, 3))
        return {"evaluation_metrics": {"precision": random.uniform(0.7, 0.9), "recall": random.uniform(0.6, 0.88)}}

    def _handle_error_task(self, params: Dict[str, Any]) -> Dict[str, Any]:
        error_message = params.get("error_message", params.get("message", "未指定的错误任务"))
        print(f"  处理中: 错误任务 - {error_message}")
        return {"original_error": error_message}


if __name__ == "__main__":
    class MockResourceAllocator:
        def allocate_resources_for_task(self, task):
            print(f"(模拟) 为任务 {task.get('task_id')} 分配资源。")
            return {
                "task_id": task.get("task_id"),
                "assigned_gpus": ["gpu0"] if "render" in task.get("task_type") else [],
            }

        def release_resources_for_task(self, task, details):
            print(f"(模拟) 释放任务 {task.get('task_id')} 的资源。")

    executor = TaskExecutor(resource_allocator=MockResourceAllocator())

    sample_tasks = [
        {
            "task_id": "vid001_load",
            "goal_id": "vid001",
            "task_type": "load_media",
            "parameters": {"media_paths_or_ids": ["movie.mp4"]},
            "status": "pending",
        },
        {
            "task_id": "vid001_analyze",
            "goal_id": "vid001",
            "task_type": "analyze_media_content",
            "parameters": {"analysis_type": "action_detection"},
            "status": "pending",
        },
        {
            "task_id": "vid001_select",
            "goal_id": "vid001",
            "task_type": "select_clips_for_timeline",
            "parameters": {"target_duration_seconds": 30, "theme": "action"},
            "status": "pending",
        },
        {
            "task_id": "vid001_render",
            "goal_id": "vid001",
            "task_type": "render_final_video",
            "parameters": {"output_format": "mp4", "quality": "low"},
            "status": "pending",
        },
        {
            "task_id": "err001_unsupported",
            "goal_id": "err001",
            "task_type": "unknown_task_type",
            "parameters": {},
            "status": "pending",
        },
        {
            "task_id": "err002_explicit",
            "goal_id": "err002",
            "task_type": "error_handling",
            "parameters": {"error_message": "Previous step failed validation"},
            "status": "pending",
        },
    ]

    for task_to_run in sample_tasks:
        print(f"\n--- 提交任务: {task_to_run['task_id']} ({task_to_run['task_type']}) ---")
        result = executor.execute_task(task_to_run)
        print(f"执行结果 for {result['task_id']}: Status - {result['status']}")
        if result["status"] == "completed":
            print(f"  Output: {result.get('output')}")
        elif result["status"] == "failed":
            print(f"  Error: {result.get('error_message')}")
        print(f"  Time: {result.get('execution_time_ms')}ms")